<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>تسجيل الدخول - نظام نقطة المبيعات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 60px 40px;
        }
        
        .login-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 60px 40px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .welcome-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .form-floating label {
            right: 15px;
            left: auto;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
            }
            
            .login-form, .login-image {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Form -->
                <div class="col-md-6">
                    <div class="login-form">
                        <div class="text-center mb-4">
                            <i class="fas fa-cash-register text-primary" style="font-size: 3rem;"></i>
                            <h2 class="mt-3 mb-1">مرحباً بك</h2>
                            <p class="text-muted">سجل دخولك للوصول إلى نظام نقطة المبيعات</p>
                        </div>

                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form method="POST" action="{{ route('login') }}">
                            @csrf
                            
                            <div class="form-floating mb-3">
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email') }}" 
                                       placeholder="البريد الإلكتروني"
                                       required 
                                       autofocus>
                                <label for="email">البريد الإلكتروني</label>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-floating mb-3">
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       placeholder="كلمة المرور"
                                       required>
                                <label for="password">كلمة المرور</label>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                للحصول على المساعدة، تواصل مع مدير النظام
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Welcome Section -->
                <div class="col-md-6">
                    <div class="login-image">
                        <div>
                            <div class="logo">
                                <i class="fas fa-store"></i>
                            </div>
                            <div class="welcome-text">نظام نقطة المبيعات</div>
                            <p class="mb-4">نظام شامل لإدارة المبيعات والمخزون مصمم خصيصاً للسوق اليمني</p>
                            
                            <div class="row text-center">
                                <div class="col-4">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                    <p class="small">إدارة المبيعات</p>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-boxes fa-2x mb-2"></i>
                                    <p class="small">إدارة المخزون</p>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <p class="small">التقارير</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Accounts Info -->
    <div class="position-fixed bottom-0 start-0 m-3">
        <div class="card" style="max-width: 300px;">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">حسابات تجريبية</h6>
            </div>
            <div class="card-body small">
                <p class="mb-2"><strong>مدير النظام:</strong></p>
                <p class="mb-1">البريد: <EMAIL></p>
                <p class="mb-3">كلمة المرور: 123456</p>
                
                <p class="mb-2"><strong>كاشير:</strong></p>
                <p class="mb-1">البريد: <EMAIL></p>
                <p class="mb-0">كلمة المرور: 123456</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
