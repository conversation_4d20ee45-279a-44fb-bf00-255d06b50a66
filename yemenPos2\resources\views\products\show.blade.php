@extends('layouts.app')

@section('title', 'عرض المنتج - ' . $product->name_ar)
@section('page-title', 'عرض المنتج')

@push('styles')
<style>
    .product-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .product-image {
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        max-height: 300px;
        object-fit: cover;
    }
    
    .info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }
    
    .info-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }
    
    .info-title i {
        margin-left: 10px;
        font-size: 1.2rem;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: 2px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        border-color: #667eea;
        transform: translateY(-3px);
    }
    
    .stat-value {
        font-size: 1.8rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }
    
    .status-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
    }
    
    .barcode-display {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        font-family: 'Courier New', monospace;
        font-size: 1.2rem;
        font-weight: bold;
        letter-spacing: 2px;
    }
</style>
@endpush

@section('content')
<!-- Product Header -->
<div class="product-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">{{ $product->name_ar }}</h1>
            @if($product->name_en)
                <h5 class="mb-3 opacity-75">{{ $product->name_en }}</h5>
            @endif
            <div class="d-flex align-items-center">
                <span class="badge bg-light text-dark me-3">{{ $product->sku }}</span>
                @if($product->is_active)
                    <span class="status-badge bg-success text-white">نشط</span>
                @else
                    <span class="status-badge bg-secondary text-white">غير نشط</span>
                @endif
            </div>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{{ route('products.edit', $product) }}" class="btn btn-light">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
                <a href="{{ route('products.index') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Product Image and Basic Info -->
    <div class="col-md-4">
        <div class="info-card">
            <h6 class="info-title">
                <i class="fas fa-image"></i>
                صورة المنتج
            </h6>
            @if($product->image)
                <img src="{{ asset('storage/' . $product->image) }}" 
                     alt="{{ $product->name_ar }}" 
                     class="img-fluid product-image w-100">
            @else
                <div class="text-center py-5">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد صورة</p>
                </div>
            @endif
        </div>
        
        <!-- Barcode -->
        @if($product->barcode)
        <div class="info-card">
            <h6 class="info-title">
                <i class="fas fa-barcode"></i>
                الباركود
            </h6>
            <div class="barcode-display">
                {{ $product->barcode }}
            </div>
        </div>
        @endif
    </div>
    
    <!-- Product Details -->
    <div class="col-md-8">
        <!-- Basic Information -->
        <div class="info-card">
            <h6 class="info-title">
                <i class="fas fa-info-circle"></i>
                المعلومات الأساسية
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>التصنيف:</strong> {{ $product->category->name_ar ?? 'غير مصنف' }}</p>
                    <p><strong>الوحدة:</strong> {{ $product->unit }}</p>
                    <p><strong>تتبع المخزون:</strong> 
                        @if($product->track_stock)
                            <span class="badge bg-success">نعم</span>
                        @else
                            <span class="badge bg-secondary">لا</span>
                        @endif
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>تاريخ الإنشاء:</strong> {{ $product->created_at->format('Y-m-d H:i') }}</p>
                    <p><strong>آخر تحديث:</strong> {{ $product->updated_at->format('Y-m-d H:i') }}</p>
                </div>
            </div>
        </div>
        
        <!-- Pricing Information -->
        <div class="info-card">
            <h6 class="info-title">
                <i class="fas fa-dollar-sign"></i>
                معلومات الأسعار
            </h6>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">{{ number_format($product->purchase_price, 2) }}</div>
                        <div class="stat-label">سعر الشراء (ر.ي)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">{{ number_format($product->selling_price, 2) }}</div>
                        <div class="stat-label">سعر البيع (ر.ي)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">{{ number_format($product->profit, 2) }}</div>
                        <div class="stat-label">الربح (ر.ي)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">{{ number_format($product->profit_margin, 1) }}%</div>
                        <div class="stat-label">نسبة الربح</div>
                    </div>
                </div>
            </div>
            @if($product->wholesale_price)
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">{{ number_format($product->wholesale_price, 2) }}</div>
                        <div class="stat-label">سعر الجملة (ر.ي)</div>
                    </div>
                </div>
            </div>
            @endif
        </div>
        
        <!-- Stock Information -->
        <div class="info-card">
            <h6 class="info-title">
                <i class="fas fa-boxes"></i>
                معلومات المخزون
            </h6>
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-value {{ $product->isOutOfStock() ? 'text-danger' : ($product->isLowStock() ? 'text-warning' : 'text-success') }}">
                            {{ $product->stock_quantity }}
                        </div>
                        <div class="stat-label">الكمية المتوفرة</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-value">{{ $product->min_stock_level }}</div>
                        <div class="stat-label">الحد الأدنى</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-value">
                            @if($product->isOutOfStock())
                                <span class="text-danger">نفد المخزون</span>
                            @elseif($product->isLowStock())
                                <span class="text-warning">مخزون منخفض</span>
                            @else
                                <span class="text-success">متوفر</span>
                            @endif
                        </div>
                        <div class="stat-label">حالة المخزون</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Description -->
        @if($product->description_ar || $product->description_en)
        <div class="info-card">
            <h6 class="info-title">
                <i class="fas fa-align-left"></i>
                الوصف
            </h6>
            @if($product->description_ar)
                <div class="mb-3">
                    <strong>الوصف (عربي):</strong>
                    <p class="mt-2">{{ $product->description_ar }}</p>
                </div>
            @endif
            @if($product->description_en)
                <div>
                    <strong>الوصف (إنجليزي):</strong>
                    <p class="mt-2">{{ $product->description_en }}</p>
                </div>
            @endif
        </div>
        @endif
    </div>
</div>

<!-- Sales History -->
@if($product->saleItems->count() > 0)
<div class="info-card mt-4">
    <h6 class="info-title">
        <i class="fas fa-chart-line"></i>
        تاريخ المبيعات
    </h6>
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>التاريخ</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                @foreach($product->saleItems->take(10) as $saleItem)
                <tr>
                    <td>
                        <a href="{{ route('sales.show', $saleItem->sale) }}">
                            {{ $saleItem->sale->invoice_number }}
                        </a>
                    </td>
                    <td>{{ $saleItem->sale->sale_date->format('Y-m-d') }}</td>
                    <td>{{ $saleItem->quantity }} {{ $saleItem->unit }}</td>
                    <td>{{ number_format($saleItem->unit_price, 2) }} ر.ي</td>
                    <td>{{ number_format($saleItem->total_price, 2) }} ر.ي</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @if($product->saleItems->count() > 10)
        <div class="text-center">
            <small class="text-muted">عرض آخر 10 مبيعات فقط</small>
        </div>
    @endif
</div>
@endif
@endsection
