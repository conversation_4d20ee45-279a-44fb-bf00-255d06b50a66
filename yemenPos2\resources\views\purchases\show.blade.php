@extends('layouts.app')

@section('title', 'تفاصيل المشترى - ' . $purchase->purchase_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-eye me-2"></i>
                        تفاصيل المشترى {{ $purchase->purchase_number }}
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('purchases.index') }}">المشتريات</a></li>
                            <li class="breadcrumb-item active">{{ $purchase->purchase_number }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('purchases.edit', $purchase) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                    <a href="{{ route('purchases.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Purchase Info -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات المشترى
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>رقم المشترى:</strong></div>
                                <div class="col-sm-7">{{ $purchase->purchase_number }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>التاريخ:</strong></div>
                                <div class="col-sm-7">{{ $purchase->purchase_date->format('Y-m-d') }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>الوقت:</strong></div>
                                <div class="col-sm-7">{{ $purchase->created_at->format('H:i') }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>الحالة:</strong></div>
                                <div class="col-sm-7">
                                    @if($purchase->status === 'مكتملة')
                                        <span class="badge bg-success">مكتملة</span>
                                    @elseif($purchase->status === 'معلقة')
                                        <span class="badge bg-warning">معلقة</span>
                                    @else
                                        <span class="badge bg-secondary">{{ $purchase->status }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>المستخدم:</strong></div>
                                <div class="col-sm-7">{{ $purchase->user->name ?? 'غير محدد' }}</div>
                            </div>
                            @if($purchase->notes)
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>ملاحظات:</strong></div>
                                <div class="col-sm-7">{{ $purchase->notes }}</div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Supplier Info -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-truck me-2"></i>
                                معلومات المورد
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>الاسم:</strong></div>
                                <div class="col-sm-7">{{ $purchase->supplier->name }}</div>
                            </div>
                            @if($purchase->supplier->company_name)
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>الشركة:</strong></div>
                                <div class="col-sm-7">{{ $purchase->supplier->company_name }}</div>
                            </div>
                            @endif
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>الهاتف:</strong></div>
                                <div class="col-sm-7">
                                    <a href="tel:{{ $purchase->supplier->phone }}" class="text-decoration-none">
                                        {{ $purchase->supplier->phone }}
                                    </a>
                                </div>
                            </div>
                            @if($purchase->supplier->email)
                            <div class="row mb-3">
                                <div class="col-sm-5"><strong>البريد:</strong></div>
                                <div class="col-sm-7">
                                    <a href="mailto:{{ $purchase->supplier->email }}" class="text-decoration-none">
                                        {{ $purchase->supplier->email }}
                                    </a>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Totals -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>
                                المجاميع
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span>{{ number_format($purchase->subtotal, 2) }} ر.ي</span>
                            </div>
                            @if($purchase->discount_amount > 0)
                            <div class="d-flex justify-content-between mb-2">
                                <span>الخصم:</span>
                                <span class="text-danger">- {{ number_format($purchase->discount_amount, 2) }} ر.ي</span>
                            </div>
                            @endif
                            @if($purchase->tax_amount > 0)
                            <div class="d-flex justify-content-between mb-2">
                                <span>الضريبة:</span>
                                <span>{{ number_format($purchase->tax_amount, 2) }} ر.ي</span>
                            </div>
                            @endif
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>الإجمالي:</strong>
                                <strong class="text-primary">{{ number_format($purchase->total_amount, 2) }} ر.ي</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Purchase Items -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-boxes me-2"></i>
                                المنتجات ({{ $purchase->purchaseItems->count() }} صنف)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>#</th>
                                            <th>المنتج</th>
                                            <th>الكود</th>
                                            <th>الكمية</th>
                                            <th>الوحدة</th>
                                            <th>سعر الشراء</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($purchase->purchaseItems as $index => $item)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>
                                                <div>
                                                    <strong>{{ $item->product_name }}</strong>
                                                    @if($item->product)
                                                        <br><small class="text-muted">المخزون الحالي: {{ $item->product->stock }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>{{ $item->product_sku }}</td>
                                            <td>
                                                <span class="badge bg-info">{{ $item->quantity }}</span>
                                            </td>
                                            <td>{{ $item->unit }}</td>
                                            <td>{{ number_format($item->unit_cost, 2) }} ر.ي</td>
                                            <td><strong>{{ number_format($item->total_cost, 2) }} ر.ي</strong></td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th colspan="6" class="text-end">الإجمالي:</th>
                                            <th>{{ number_format($purchase->purchaseItems->sum('total_cost'), 2) }} ر.ي</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .card-body .row {
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush
@endsection
