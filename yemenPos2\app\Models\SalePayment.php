<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'amount',
        'payment_method',
        'notes',
        'user_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * العلاقة مع الفاتورة
     */
    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    /**
     * العلاقة مع المستخدم الذي قام بالدفع
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
