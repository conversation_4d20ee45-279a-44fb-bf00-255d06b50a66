<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Sale extends Model
{
    protected $fillable = [
        'invoice_number',
        'customer_id',
        'user_id',
        'sale_date',
        'sale_time',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'remaining_amount',
        'payment_method',
        'status',
        'is_posted',
        'posted_at',
        'posted_by',
        'currency',
        'exchange_rate',
        'notes',
        'is_printed',
        'is_sent_whatsapp'
    ];

    protected $casts = [
        'sale_date' => 'date',
        'sale_time' => 'datetime:H:i',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'is_printed' => 'boolean',
        'is_sent_whatsapp' => 'boolean',
        'is_posted' => 'boolean',
        'posted_at' => 'datetime'
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع المستخدم (الكاشير)
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع عناصر المبيعات
     */
    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    /**
     * العلاقة مع المستخدم الذي قام بالترحيل
     */
    public function postedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'posted_by');
    }

    /**
     * العلاقة مع دفعات الفاتورة
     */
    public function salePayments()
    {
        return $this->hasMany(SalePayment::class);
    }

    /**
     * التحقق من إمكانية التعديل
     */
    public function canEdit(): bool
    {
        return !$this->is_posted;
    }

    /**
     * التحقق من إمكانية الحذف
     */
    public function canDelete(): bool
    {
        return !$this->is_posted;
    }

    /**
     * التحقق من إمكانية الترحيل
     */
    public function canPost(): bool
    {
        return !$this->is_posted;
    }

    /**
     * التحقق من إمكانية الاسترجاع
     */
    public function canRefund(): bool
    {
        return !$this->is_posted && $this->status !== 'مسترجعة';
    }

    /**
     * ترحيل الفاتورة
     */
    public function post($userId = null): bool
    {
        if (!$this->canPost()) {
            return false;
        }

        $this->update([
            'is_posted' => true,
            'posted_at' => now(),
            'posted_by' => $userId ?? auth()->id()
        ]);

        return true;
    }

    /**
     * إلغاء ترحيل الفاتورة (للمدير فقط)
     */
    public function unpost(): bool
    {
        if (!auth()->user()->hasRole('admin')) {
            return false;
        }

        $this->update([
            'is_posted' => false,
            'posted_at' => null,
            'posted_by' => null
        ]);

        return true;
    }

    /**
     * الحصول على حالة الترحيل مع اللون
     */
    public function getPostingStatusBadgeAttribute(): string
    {
        if ($this->is_posted) {
            return '<span class="badge bg-success">مرحلة</span>';
        } else {
            return '<span class="badge bg-warning">غير مرحلة</span>';
        }
    }

    /**
     * التحقق من اكتمال الدفع
     */
    public function isFullyPaid(): bool
    {
        return $this->remaining_amount <= 0;
    }

    /**
     * التحقق من وجود مبلغ متبقي
     */
    public function hasRemainingAmount(): bool
    {
        return $this->remaining_amount > 0;
    }

    /**
     * حساب إجمالي الأرباح
     */
    public function getTotalProfitAttribute(): float
    {
        $totalProfit = 0;
        foreach ($this->saleItems as $item) {
            $product = $item->product;
            if ($product) {
                $profit = ($item->unit_price - $product->purchase_price) * $item->quantity;
                $totalProfit += $profit;
            }
        }
        return $totalProfit;
    }

    /**
     * إنشاء رقم فاتورة تلقائي
     */
    public static function generateInvoiceNumber(): string
    {
        $prefix = Setting::getValue('invoice_prefix', 'INV-');
        $startNumber = Setting::getValue('invoice_start_number', 1);

        $lastSale = self::latest('id')->first();
        $nextNumber = $lastSale ? $lastSale->id + 1 : $startNumber;

        return $prefix . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }
}
