@extends('layouts.app')

@section('title', 'إضافة تصنيف جديد - نظام نقطة المبيعات')
@section('page-title', 'إضافة تصنيف جديد')

@push('styles')
<style>
    .category-form-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 200px);
        padding: 30px 0;
    }
    
    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }
    
    .form-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }
    
    .form-header::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }
    
    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-section:hover {
        border-color: #28a745;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.1);
    }
    
    .section-title {
        color: #28a745;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }
    
    .section-title i {
        margin-left: 10px;
        font-size: 1.2rem;
    }
    
    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
    }
    
    .btn-back {
        border: 2px solid #6c757d;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .btn-back:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }
    
    .required-asterisk {
        color: #dc3545;
        font-weight: bold;
    }
    
    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
    
    .image-upload-area {
        border: 3px dashed #dee2e6;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .image-upload-area:hover {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.05);
    }
    
    .image-upload-area.dragover {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.1);
    }
</style>
@endpush

@section('content')
<div class="category-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card">
                    <div class="form-header">
                        <i class="fas fa-tags fa-3x mb-3"></i>
                        <h2 class="mb-2">إضافة تصنيف جديد</h2>
                        <p class="mb-0 opacity-75">أضف تصنيف جديد لتنظيم المنتجات</p>
                    </div>
                    <div class="p-4">
                        <form action="{{ route('categories.store') }}" method="POST" enctype="multipart/form-data" id="categoryForm">
                            @csrf
                            
                            <!-- المعلومات الأساسية -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    المعلومات الأساسية
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_ar" class="form-label">
                                                اسم التصنيف (عربي) <span class="required-asterisk">*</span>
                                            </label>
                                            <input type="text" class="form-control @error('name_ar') is-invalid @enderror"
                                                   id="name_ar" name="name_ar" value="{{ old('name_ar') }}"
                                                   placeholder="أدخل اسم التصنيف بالعربية" required>
                                            @error('name_ar')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_en" class="form-label">اسم التصنيف (إنجليزي)</label>
                                            <input type="text" class="form-control @error('name_en') is-invalid @enderror"
                                                   id="name_en" name="name_en" value="{{ old('name_en') }}"
                                                   placeholder="أدخل اسم التصنيف بالإنجليزية">
                                            @error('name_en')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الوصف والصورة -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-align-left"></i>
                                    الوصف والصورة
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="description_ar" class="form-label">الوصف (عربي)</label>
                                            <textarea class="form-control @error('description_ar') is-invalid @enderror"
                                                      id="description_ar" name="description_ar" rows="4"
                                                      placeholder="أدخل وصف التصنيف بالعربية...">{{ old('description_ar') }}</textarea>
                                            @error('description_ar')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description_en" class="form-label">الوصف (إنجليزي)</label>
                                            <textarea class="form-control @error('description_en') is-invalid @enderror"
                                                      id="description_en" name="description_en" rows="4"
                                                      placeholder="أدخل وصف التصنيف بالإنجليزية...">{{ old('description_en') }}</textarea>
                                            @error('description_en')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">صورة التصنيف</label>
                                            <div class="image-upload-area" onclick="document.getElementById('category_image').click()">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <h6 class="text-muted">اضغط لرفع صورة التصنيف</h6>
                                                <p class="text-muted small mb-0">أو اسحب الصورة هنا</p>
                                                <input type="file" id="category_image" name="image" accept="image/*" style="display: none;">
                                            </div>
                                            <div id="image-preview" class="mt-3" style="display: none;">
                                                <img id="preview-img" src="" alt="معاينة الصورة" class="img-fluid rounded" style="max-height: 200px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- الإعدادات -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-cogs"></i>
                                    إعدادات التصنيف
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                <strong>تصنيف نشط</strong>
                                                <br><small class="text-muted">يظهر التصنيف في قوائم المنتجات</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- أزرار الإجراءات -->
                            <div class="d-flex justify-content-between align-items-center pt-4">
                                <a href="{{ route('categories.index') }}" class="btn btn-back">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    العودة للقائمة
                                </a>
                                
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-save">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التصنيف
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// معاينة الصورة
function handleImagePreview() {
    const input = document.getElementById('category_image');
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    
    input.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('categoryForm').reset();
        document.getElementById('image-preview').style.display = 'none';
        showToast('تم إعادة تعيين النموذج', 'info');
    }
}

// إظهار رسائل التنبيه
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// التحقق من صحة النموذج قبل الإرسال
function validateForm() {
    const form = document.getElementById('categoryForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        showToast('يرجى ملء جميع الحقول المطلوبة', 'danger');
        return false;
    }
    
    return true;
}

// إعداد الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الصورة
    handleImagePreview();
    
    // التحقق من النموذج عند الإرسال
    document.getElementById('categoryForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
});

// السحب والإفلات للصور
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.image-upload-area');
    const fileInput = document.getElementById('category_image');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight(e) {
        uploadArea.classList.add('dragover');
    }
    
    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }
    
    uploadArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);
        }
    }
});
</script>
@endpush
