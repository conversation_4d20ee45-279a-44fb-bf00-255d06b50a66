@extends('layouts.app')

@section('title', 'الفواتير المرحلة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-check-circle me-2 text-success"></i>
                        الفواتير المرحلة (المبيعات النهائية)
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('sales.index') }}">المبيعات</a></li>
                            <li class="breadcrumb-item active">المرحلة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('sales.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>العودة للمبيعات
                    </a>
                    <a href="{{ route('sales.unposted') }}" class="btn btn-warning me-2">
                        <i class="fas fa-clock me-2"></i>غير المرحلة
                    </a>
                    <a href="{{ route('pos.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>فاتورة جديدة
                    </a>
                </div>
            </div>

            <!-- Alert -->
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>المبيعات النهائية:</strong> هذه الفواتير مرحلة ومؤكدة وتحسب ضمن المبيعات النهائية.
                لا يمكن تعديلها أو حذفها إلا من قبل المدير.
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>{{ $countPosted }}</h3>
                            <p class="mb-0">عدد الفواتير المرحلة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>{{ number_format($totalPosted, 2) }}</h3>
                            <p class="mb-0">إجمالي المبيعات (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>{{ number_format($cashSales, 2) }}</h3>
                            <p class="mb-0">النقدي المحصل (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>{{ number_format($creditSales, 2) }}</h3>
                            <p class="mb-0">المديونيات (ر.ي)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Statistics Row -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3>{{ number_format($bankSales, 2) }}</h3>
                            <p class="mb-0">البنك المحصل (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <h6 class="text-muted">نقدي محصل</h6>
                                    <h5 class="text-info">{{ number_format(($cashSales / max(($cashSales + $creditSales + $bankSales), 1)) * 100, 1) }}%</h5>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-muted">مديونيات</h6>
                                    <h5 class="text-warning">{{ number_format(($creditSales / max(($cashSales + $creditSales + $bankSales), 1)) * 100, 1) }}%</h5>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-muted">بنك محصل</h6>
                                    <h5 class="text-secondary">{{ number_format(($bankSales / max(($cashSales + $creditSales + $bankSales), 1)) * 100, 1) }}%</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('sales.posted') }}">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search"
                                       value="{{ request('search') }}" placeholder="رقم الفاتورة أو اسم العميل...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ البيع</label>
                                <input type="date" class="form-control" name="date_from"
                                       value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ البيع</label>
                                <input type="date" class="form-control" name="date_to"
                                       value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ الترحيل</label>
                                <input type="date" class="form-control" name="posted_from"
                                       value="{{ request('posted_from') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ الترحيل</label>
                                <input type="date" class="form-control" name="posted_to"
                                       value="{{ request('posted_to') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="customer_id">
                                    <option value="">جميع العملاء</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                                {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="row g-3 mt-2">
                            <div class="col-md-2">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" name="payment_method">
                                    <option value="">جميع الطرق</option>
                                    <option value="نقدي" {{ request('payment_method') == 'نقدي' ? 'selected' : '' }}>نقدي</option>
                                    <option value="بطاقة ائتمان" {{ request('payment_method') == 'بطاقة ائتمان' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                    <option value="تحويل بنكي" {{ request('payment_method') == 'تحويل بنكي' ? 'selected' : '' }}>تحويل بنكي</option>
                                    <option value="آجل" {{ request('payment_method') == 'آجل' ? 'selected' : '' }}>آجل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="{{ route('sales.posted') }}" class="btn btn-secondary">
                                        <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sales Table -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        الفواتير المرحلة ({{ $sales->total() }})
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-download me-1"></i>
                            تصدير Excel
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-file-pdf me-1"></i>
                            تصدير PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if($sales->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>تاريخ البيع</th>
                                        <th>تاريخ الترحيل</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>طريقة الدفع</th>
                                        <th>حالة الدفع</th>
                                        <th>الكاشير</th>
                                        <th>من رحل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sales as $sale)
                                    <tr>
                                        <td>
                                            <a href="{{ route('sales.show', $sale) }}" class="text-decoration-none fw-bold">
                                                {{ $sale->invoice_number }}
                                            </a>
                                            <br>
                                            <small class="text-success">
                                                <i class="fas fa-check-circle me-1"></i>مرحلة
                                            </small>
                                        </td>
                                        <td>
                                            @if($sale->customer)
                                                <div>
                                                    <span class="fw-bold">{{ $sale->customer->name }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $sale->customer->phone }}</small>
                                                </div>
                                            @else
                                                <span class="text-muted">عميل عادي</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>
                                                {{ $sale->sale_date->format('Y-m-d') }}
                                                <br>
                                                <small class="text-muted">
                                                    {{ $sale->sale_time->format('g:i') }}
                                                    {{ $sale->sale_time->format('A') == 'AM' ? 'ص' : 'م' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                {{ $sale->posted_at->format('Y-m-d') }}
                                                <br>
                                                <small class="text-muted">{{ $sale->posted_at->format('H:i') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">
                                                {{ number_format($sale->total_amount, 2) }} ر.ي
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $sale->payment_method }}</span>
                                        </td>
                                        <td>
                                            @if($sale->status === 'مكتملة')
                                                <span class="badge bg-success">مكتملة</span>
                                            @elseif($sale->status === 'غير مدفوعة')
                                                <span class="badge bg-danger">غير مدفوعة</span>
                                            @elseif($sale->status === 'جزئي')
                                                <span class="badge bg-warning">دفع جزئي</span>
                                            @elseif($sale->status === 'مسترجعة')
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-undo me-1"></i>مسترجعة
                                                </span>
                                            @else
                                                <span class="badge bg-info">{{ $sale->status }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $sale->user->name }}</small>
                                        </td>
                                        <td>
                                            @if($sale->postedBy)
                                                <small class="text-muted">{{ $sale->postedBy->name }}</small>
                                            @else
                                                <small class="text-muted">-</small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('sales.show', $sale) }}"
                                                   class="btn btn-sm btn-outline-info"
                                                   title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('sales.print', $sale) }}"
                                                   class="btn btn-sm btn-outline-secondary"
                                                   title="طباعة"
                                                   target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>

                                                @if($sale->customer && $sale->customer->whatsapp)
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-success"
                                                            title="إرسال واتساب"
                                                            onclick="sendWhatsApp({{ $sale->id }})">
                                                        <i class="fab fa-whatsapp"></i>
                                                    </button>
                                                @endif

                                                <!-- إلغاء الترحيل للمدير فقط -->
                                                @if(auth()->user()->hasRole('admin'))
                                                    <form method="POST" action="{{ route('sales.unpost', $sale) }}" style="display: inline;">
                                                        @csrf
                                                        <button type="submit"
                                                                class="btn btn-sm btn-outline-warning"
                                                                title="إلغاء الترحيل"
                                                                onclick="return confirm('هل تريد إلغاء ترحيل هذه الفاتورة؟')">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="صفحات الفواتير المرحلة">
                                {{ $sales->links('pagination::bootstrap-4') }}
                            </nav>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد فواتير مرحلة</h5>
                            <p class="text-muted">لم يتم ترحيل أي فواتير بعد</p>
                            <a href="{{ route('sales.unposted') }}" class="btn btn-warning">
                                <i class="fas fa-clock me-2"></i>
                                عرض الفواتير غير المرحلة
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
