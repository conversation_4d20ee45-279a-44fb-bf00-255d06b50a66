<?php $__env->startSection('title', 'إدارة المبيعات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-shopping-cart me-2"></i>
                        إدارة المبيعات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المبيعات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?php echo e(route('pos.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>فاتورة جديدة
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('sales.index')); ?>">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search"
                                       value="<?php echo e(request('search')); ?>" placeholder="رقم الفاتورة أو اسم العميل...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from"
                                       value="<?php echo e(request('date_from')); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to"
                                       value="<?php echo e(request('date_to')); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="customer_id">
                                    <option value="">جميع العملاء</option>
                                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($customer->id); ?>"
                                                <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                            <?php echo e($customer->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" name="payment_method">
                                    <option value="">جميع الطرق</option>
                                    <option value="نقدي" <?php echo e(request('payment_method') == 'نقدي' ? 'selected' : ''); ?>>نقدي</option>
                                    <option value="بطاقة ائتمان" <?php echo e(request('payment_method') == 'بطاقة ائتمان' ? 'selected' : ''); ?>>بطاقة ائتمان</option>
                                    <option value="تحويل بنكي" <?php echo e(request('payment_method') == 'تحويل بنكي' ? 'selected' : ''); ?>>تحويل بنكي</option>
                                    <option value="آجل" <?php echo e(request('payment_method') == 'آجل' ? 'selected' : ''); ?>>آجل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">حالة الترحيل</label>
                                <select class="form-select" name="posting_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="مرحلة" <?php echo e(request('posting_status') == 'مرحلة' ? 'selected' : ''); ?>>مرحلة</option>
                                    <option value="غير_مرحلة" <?php echo e(request('posting_status') == 'غير_مرحلة' ? 'selected' : ''); ?>>غير مرحلة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row g-3 mt-2">
                            <div class="col-md-2">
                                <label class="form-label">حالة الدفع</label>
                                <select class="form-select" name="payment_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="مدفوعة" <?php echo e(request('payment_status') == 'مدفوعة' ? 'selected' : ''); ?>>مدفوعة</option>
                                    <option value="غير_مدفوعة" <?php echo e(request('payment_status') == 'غير_مدفوعة' ? 'selected' : ''); ?>>غير مدفوعة</option>
                                    <option value="جزئي" <?php echo e(request('payment_status') == 'جزئي' ? 'selected' : ''); ?>>دفع جزئي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="<?php echo e(route('sales.unposted')); ?>" class="btn btn-warning">
                                        <i class="fas fa-clock me-2"></i>غير مرحلة
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

<!-- Sales Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-receipt me-2"></i>
            قائمة المبيعات (<?php echo e($sales->total()); ?>)
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if($sales->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المتبقي</th>
                            <th>طريقة الدفع</th>
                            <th>حالة الدفع</th>
                            <th>حالة الترحيل</th>
                            <th>الكاشير</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <a href="<?php echo e(route('sales.show', $sale)); ?>" class="text-decoration-none fw-bold">
                                    <?php echo e($sale->invoice_number); ?>

                                </a>
                            </td>
                            <td>
                                <?php if($sale->customer): ?>
                                    <div>
                                        <span class="fw-bold"><?php echo e($sale->customer->name); ?></span>
                                        <br>
                                        <small class="text-muted"><?php echo e($sale->customer->phone); ?></small>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">عميل عادي</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div>
                                    <?php echo e($sale->sale_date->format('Y-m-d')); ?>

                                    <br>
                                    <small class="text-muted">
                                        <?php echo e($sale->sale_time->format('g:i')); ?>

                                        <?php echo e($sale->sale_time->format('A') == 'AM' ? 'ص' : 'م'); ?>

                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold text-success">
                                    <?php echo e(number_format($sale->total_amount, 2)); ?> ر.ي
                                </span>
                            </td>
                            <td>
                                <span class="text-primary">
                                    <?php echo e(number_format($sale->paid_amount, 2)); ?> ر.ي
                                </span>
                            </td>
                            <td>
                                <?php if($sale->remaining_amount > 0): ?>
                                    <span class="text-danger fw-bold">
                                        <?php echo e(number_format($sale->remaining_amount, 2)); ?> ر.ي
                                    </span>
                                <?php else: ?>
                                    <span class="text-success">
                                        <i class="fas fa-check"></i> مدفوع
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($sale->payment_method); ?></span>
                            </td>
                            <td>
                                <?php if($sale->status === 'مكتملة'): ?>
                                    <span class="badge bg-success">مكتملة</span>
                                <?php elseif($sale->status === 'غير مدفوعة'): ?>
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                <?php elseif($sale->status === 'جزئي'): ?>
                                    <span class="badge bg-warning">دفع جزئي</span>
                                <?php elseif($sale->status === 'مسترجعة'): ?>
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-undo me-1"></i>مسترجعة
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-info"><?php echo e($sale->status); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($sale->is_posted): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>مرحلة
                                    </span>
                                    <?php if($sale->posted_at): ?>
                                        <br><small class="text-muted"><?php echo e($sale->posted_at->format('Y-m-d H:i')); ?></small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>غير مرحلة
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted"><?php echo e($sale->user->name); ?></small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('sales.show', $sale)); ?>"
                                       class="btn btn-sm btn-outline-info"
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('sales.print', $sale)); ?>"
                                       class="btn btn-sm btn-outline-secondary"
                                       title="طباعة"
                                       target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <?php if($sale->customer && $sale->customer->whatsapp): ?>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-success"
                                                title="إرسال واتساب"
                                                onclick="sendWhatsApp(<?php echo e($sale->id); ?>)">
                                            <i class="fab fa-whatsapp"></i>
                                        </button>
                                    <?php endif; ?>

                                    <!-- أزرار الترحيل -->
                                    <?php if(!$sale->is_posted): ?>
                                        <form method="POST" action="<?php echo e(route('sales.post', $sale)); ?>" style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit"
                                                    class="btn btn-sm btn-outline-primary"
                                                    title="ترحيل الفاتورة"
                                                    onclick="return confirm('هل تريد ترحيل هذه الفاتورة؟')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <?php if(auth()->user()->hasRole('admin')): ?>
                                            <form method="POST" action="<?php echo e(route('sales.unpost', $sale)); ?>" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-warning"
                                                        title="إلغاء ترحيل الفاتورة"
                                                        onclick="return confirm('هل تريد إلغاء ترحيل هذه الفاتورة؟')">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if(!$sale->is_posted): ?>
                                        <?php if($sale->status !== 'مسترجعة'): ?>
                                            <a href="<?php echo e(route('sales.edit', $sale)); ?>"
                                               class="btn btn-sm btn-outline-warning"
                                               title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <form method="POST" action="<?php echo e(route('sales.refund', $sale)); ?>" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-info"
                                                        title="استرجاع"
                                                        onclick="return confirm('هل تريد استرجاع هذه الفاتورة؟ سيتم إعادة المخزون.')">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <?php if($sale->status !== 'مسترجعة'): ?>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    title="حذف"
                                                    onclick="deleteSale(<?php echo e($sale->id); ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">مسترجعة</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="صفحات المبيعات">
                    <?php echo e($sales->links('pagination::bootstrap-4')); ?>

                </nav>
            </div>

            <!-- Summary -->
            <div class="row mt-4">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5><?php echo e($sales->count()); ?></h5>
                            <p class="mb-0">إجمالي الفواتير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5><?php echo e(number_format($postedSales ?? 0, 0)); ?></h5>
                            <p class="mb-0">المبيعات المرحلة (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5><?php echo e(number_format($unpostedSales ?? 0, 0)); ?></h5>
                            <p class="mb-0">غير المرحلة (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h5><?php echo e(number_format($cashSales ?? 0, 0)); ?></h5>
                            <p class="mb-0">المبيعات النقدية (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h5><?php echo e(number_format($creditSales ?? 0, 0)); ?></h5>
                            <p class="mb-0">المبيعات الآجلة (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h5><?php echo e(number_format($sales->sum('remaining_amount'), 2)); ?></h5>
                            <p class="mb-0">إجمالي المتبقي (ر.ي)</p>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مبيعات</h5>
                <p class="text-muted">لم يتم العثور على مبيعات تطابق معايير البحث</p>
                <a href="<?php echo e(route('pos.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء أول فاتورة
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين البطاقات الإحصائية */
.card h5 {
    font-size: 1.5rem;
    font-weight: bold;
}

.card p {
    font-size: 0.875rem;
}

/* تحسين الفلاتر */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-control, .form-select {
    font-size: 0.875rem;
}

/* تحسين breadcrumb */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item {
    font-size: 0.875rem;
}

/* تحسين الـ badges */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function deleteSale(saleId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/sales/${saleId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function sendWhatsApp(saleId) {
    fetch(`/sales/${saleId}/send-whatsapp`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.whatsapp_url, '_blank');
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إرسال الرسالة');
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/sales/index.blade.php ENDPATH**/ ?>