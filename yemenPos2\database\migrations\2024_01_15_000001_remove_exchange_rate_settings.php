<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // حذف إعدادات أسعار الصرف للعملات الأخرى
        $exchangeRateSettings = [
            'exchange_rate_egp',
            'exchange_rate_usd', 
            'exchange_rate_sar',
            'auto_update_exchange_rates'
        ];

        foreach ($exchangeRateSettings as $key) {
            Setting::where('key', $key)->delete();
        }

        // التأكد من أن العملة الافتراضية هي اليمنية
        Setting::where('key', 'default_currency')->update(['value' => 'YER']);
        
        // التأكد من أن موضع العملة "بعد"
        Setting::where('key', 'currency_position')->update(['value' => 'after']);
        
        // التأكد من رمز العملة اليمنية
        Setting::where('key', 'currency_symbol')->update(['value' => 'ر.ي']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة إنشاء إعدادات أسعار الصرف (في حالة الحاجة للتراجع)
        $exchangeRateSettings = [
            [
                'key' => 'exchange_rate_egp',
                'value' => '80',
                'type' => 'number',
                'group' => 'currency',
                'label' => 'سعر صرف الجنيه المصري',
                'description' => 'كم ريال يمني يساوي جنيه مصري واحد',
                'is_public' => false
            ],
            [
                'key' => 'exchange_rate_usd',
                'value' => '2530',
                'type' => 'number',
                'group' => 'currency',
                'label' => 'سعر صرف الدولار',
                'description' => 'كم ريال يمني يساوي دولار واحد',
                'is_public' => false
            ],
            [
                'key' => 'exchange_rate_sar',
                'value' => '665',
                'type' => 'number',
                'group' => 'currency',
                'label' => 'سعر صرف الريال السعودي',
                'description' => 'كم ريال يمني يساوي ريال سعودي واحد',
                'is_public' => false
            ],
            [
                'key' => 'auto_update_exchange_rates',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'currency',
                'label' => 'تحديث أسعار الصرف تلقائياً',
                'description' => 'تحديث أسعار الصرف من مصادر خارجية',
                'is_public' => false
            ]
        ];

        foreach ($exchangeRateSettings as $setting) {
            Setting::create($setting);
        }
    }
};
