@extends('layouts.app')

@section('title', 'تفاصيل الفاتورة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-receipt me-2"></i>
                        تفاصيل الفاتورة {{ $sale->invoice_number }}
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('sales.index') }}">المبيعات</a></li>
                            <li class="breadcrumb-item active">{{ $sale->invoice_number }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('sales.print', $sale) }}" class="btn btn-primary" target="_blank">
                        <i class="fas fa-print me-2"></i>طباعة
                    </a>
                    @if($sale->customer && $sale->customer->whatsapp)
                    <button class="btn btn-success" onclick="sendWhatsApp({{ $sale->id }})">
                        <i class="fab fa-whatsapp me-2"></i>واتساب
                    </button>
                    @endif
                    <a href="{{ route('sales.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم الفاتورة:</strong></td>
                                            <td>{{ $sale->invoice_number }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>التاريخ:</strong></td>
                                            <td>{{ $sale->sale_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الوقت:</strong></td>
                                            <td>
                                                {{ $sale->sale_time->format('g:i') }}
                                                {{ $sale->sale_time->format('A') == 'AM' ? 'ص' : 'م' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الكاشير:</strong></td>
                                            <td>{{ $sale->user->name ?? 'غير محدد' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>العميل:</strong></td>
                                            <td>{{ $sale->customer->name ?? 'عميل عادي' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>طريقة الدفع:</strong></td>
                                            <td>{{ $sale->payment_method }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                @if($sale->status === 'مكتملة')
                                                    <span class="badge bg-success">مكتملة</span>
                                                @elseif($sale->status === 'غير مدفوعة')
                                                    <span class="badge bg-danger">غير مدفوعة</span>
                                                @elseif($sale->status === 'جزئي')
                                                    <span class="badge bg-warning">دفع جزئي</span>
                                                @else
                                                    <span class="badge bg-info">{{ $sale->status }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @if($sale->notes)
                                        <tr>
                                            <td><strong>ملاحظات:</strong></td>
                                            <td>{{ $sale->notes }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر الفاتورة -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                عناصر الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>#</th>
                                            <th>المنتج</th>
                                            <th>الكود</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الخصم</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($sale->saleItems as $index => $item)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>
                                                <div>
                                                    <strong>{{ $item->product_name }}</strong>
                                                    @if($item->product)
                                                        <br><small class="text-muted">{{ $item->product->name_en ?? '' }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>{{ $item->product_sku }}</td>
                                            <td>{{ $item->quantity }} {{ $item->unit ?? 'قطعة' }}</td>
                                            <td>{{ number_format($item->unit_price, 2) }} ر.ي</td>
                                            <td>{{ number_format($item->discount_amount ?? 0, 2) }} ر.ي</td>
                                            <td>
                                                <strong>{{ number_format($item->total_price, 2) }} ر.ي</strong>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>
                                ملخص الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td>المجموع الفرعي:</td>
                                    <td class="text-end">{{ number_format($sale->subtotal ?? 0, 2) }} ر.ي</td>
                                </tr>
                                <tr>
                                    <td>الخصم:</td>
                                    <td class="text-end text-danger">- {{ number_format($sale->discount_amount ?? 0, 2) }} ر.ي</td>
                                </tr>
                                <tr>
                                    <td>الضريبة:</td>
                                    <td class="text-end">{{ number_format($sale->tax_amount ?? 0, 2) }} ر.ي</td>
                                </tr>
                                <tr class="table-dark">
                                    <td><strong>الإجمالي:</strong></td>
                                    <td class="text-end"><strong>{{ number_format($sale->total_amount, 2) }} ر.ي</strong></td>
                                </tr>
                                @if($sale->paid_amount)
                                <tr>
                                    <td>المبلغ المدفوع:</td>
                                    <td class="text-end text-success">{{ number_format($sale->paid_amount, 2) }} ر.ي</td>
                                </tr>
                                @endif
                                @if($sale->remaining_amount && $sale->remaining_amount > 0)
                                <tr>
                                    <td>المبلغ المتبقي:</td>
                                    <td class="text-end text-warning"><strong>{{ number_format($sale->remaining_amount, 2) }} ر.ي</strong></td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    @if($sale->customer)
                    <!-- معلومات العميل -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                معلومات العميل
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>{{ $sale->customer->name }}</td>
                                </tr>
                                @if($sale->customer->phone)
                                <tr>
                                    <td><strong>الهاتف:</strong></td>
                                    <td>{{ $sale->customer->phone }}</td>
                                </tr>
                                @endif
                                @if($sale->customer->email)
                                <tr>
                                    <td><strong>البريد:</strong></td>
                                    <td>{{ $sale->customer->email }}</td>
                                </tr>
                                @endif
                                @if($sale->customer->address)
                                <tr>
                                    <td><strong>العنوان:</strong></td>
                                    <td>{{ $sale->customer->address }}</td>
                                </tr>
                                @endif
                            </table>
                            <div class="d-grid">
                                <a href="{{ route('customers.show', $sale->customer) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-2"></i>عرض ملف العميل
                                </a>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- إجراءات سريعة -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if(($sale->status === 'جزئي' || $sale->status === 'غير مدفوعة') && $sale->remaining_amount > 0)
                                <button class="btn btn-warning" onclick="payDebt({{ $sale->id }}, {{ $sale->remaining_amount }})">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    دفع المتبقي
                                </button>
                                @endif

                                <a href="{{ route('sales.print', $sale) }}" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة الفاتورة
                                </a>

                                @if($sale->customer && $sale->customer->whatsapp)
                                <button class="btn btn-success" onclick="sendWhatsApp({{ $sale->id }})">
                                    <i class="fab fa-whatsapp me-2"></i>
                                    إرسال واتساب
                                </button>
                                @endif

                                <a href="{{ route('pos.index') }}" class="btn btn-info">
                                    <i class="fas fa-plus me-2"></i>
                                    فاتورة جديدة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendWhatsApp(saleId) {
    fetch(`/sales/${saleId}/send-whatsapp`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.whatsapp_url, '_blank');
        } else {
            alert('خطأ: ' + data.message);
        }
    });
}

function payDebt(saleId, remainingAmount) {
    if (confirm(`هل تريد دفع المبلغ المتبقي ${remainingAmount.toFixed(2)} ر.ي؟`)) {
        window.location.href = `/sales-debts?sale_id=${saleId}`;
    }
}
</script>
@endsection
