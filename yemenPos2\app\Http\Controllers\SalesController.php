<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class SalesController extends Controller
{
    /**
     * عرض جميع المبيعات
     */
    public function index(Request $request)
    {
        $query = Sale::with(['customer', 'user'])
            ->orderBy('created_at', 'desc');

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        // فلترة حسب العميل
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب طريقة الدفع
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        $sales = $query->paginate(20);
        $customers = Customer::where('is_active', true)->get();

        // إحصائيات
        $postedSales = Sale::where('is_posted', true)->sum('total_amount');
        $unpostedSales = Sale::where('is_posted', false)->sum('total_amount');
        $cashSales = Sale::where('payment_method', 'نقدي')->sum('total_amount');
        $creditSales = Sale::where('payment_method', 'آجل')->sum('total_amount');

        return view('sales.index', compact(
            'sales',
            'customers',
            'postedSales',
            'unpostedSales',
            'cashSales',
            'creditSales'
        ));
    }

    /**
     * عرض تفاصيل فاتورة
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'user', 'saleItems.product']);
        return view('sales.show', compact('sale'));
    }

    /**
     * دفع مديونية
     */
    public function payDebt(Request $request)
    {
        $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'payment_amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:نقدي,بطاقة ائتمان,تحويل بنكي',
            'notes' => 'nullable|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            $sale = Sale::findOrFail($request->sale_id);

            // التحقق من أن الفاتورة لها مبلغ متبقي
            if ($sale->remaining_amount <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'هذه الفاتورة مدفوعة بالكامل'
                ]);
            }

            // التحقق من أن المبلغ المدفوع لا يتجاوز المتبقي
            if ($request->payment_amount > $sale->remaining_amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'المبلغ المدفوع أكبر من المبلغ المتبقي'
                ]);
            }

            // تحديث المبالغ
            $sale->paid_amount += $request->payment_amount;
            $sale->remaining_amount -= $request->payment_amount;

            // تحديث الحالة
            if ($sale->remaining_amount <= 0) {
                $sale->status = 'مكتملة';
                $sale->remaining_amount = 0; // للتأكد من عدم وجود أرقام سالبة
            } else {
                $sale->status = 'دفع جزئي';
            }

            $sale->save();

            // تسجيل عملية الدفع (يمكن إضافة جدول منفصل للدفعات)
            // PaymentLog::create([...]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم دفع المديونية بنجاح',
                'new_status' => $sale->status,
                'remaining_amount' => $sale->remaining_amount
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'خطأ في معالجة الدفع: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * عرض المديونيات
     */
    public function debts(Request $request)
    {
        $query = Sale::with(['customer', 'user'])
            ->where(function($q) {
                $q->where('status', 'دفع جزئي')
                  ->orWhere('status', 'غير مدفوعة');
            })
            ->where('remaining_amount', '>', 0)
            ->latest('sale_date')->latest('sale_time');

        // فلترة حسب العميل
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        $partialSales = $query->paginate(20);
        $customers = Customer::where('is_active', true)->get();

        // إحصائيات المديونيات
        $totalDebts = Sale::where('remaining_amount', '>', 0)->sum('remaining_amount');
        $partialDebts = Sale::where('status', 'دفع جزئي')->sum('remaining_amount');
        $unpaidDebts = Sale::where('status', 'غير مدفوعة')->sum('remaining_amount');
        $customersWithDebts = Sale::where('remaining_amount', '>', 0)
            ->whereNotNull('customer_id')
            ->distinct('customer_id')
            ->count();

        return view('sales.debts', compact(
            'partialSales',
            'customers',
            'totalDebts',
            'partialDebts',
            'unpaidDebts',
            'customersWithDebts'
        ));
    }

    /**
     * ترحيل فاتورة
     */
    public function post(Sale $sale)
    {
        if ($sale->is_posted) {
            return redirect()->back()->with('error', 'الفاتورة مرحلة مسبقاً');
        }

        $sale->update([
            'is_posted' => true,
            'posted_at' => now(),
            'posted_by' => Auth::id()
        ]);

        return redirect()->back()->with('success', 'تم ترحيل الفاتورة بنجاح');
    }

    /**
     * إلغاء ترحيل فاتورة
     */
    public function unpost(Sale $sale)
    {
        if (!$sale->is_posted) {
            return redirect()->back()->with('error', 'الفاتورة غير مرحلة');
        }

        $sale->update([
            'is_posted' => false,
            'posted_at' => null,
            'posted_by' => null
        ]);

        return redirect()->back()->with('success', 'تم إلغاء ترحيل الفاتورة بنجاح');
    }

    /**
     * طباعة فاتورة
     */
    public function print(Sale $sale)
    {
        $sale->load(['customer', 'user', 'saleItems.product']);
        return view('sales.print', compact('sale'));
    }
}
