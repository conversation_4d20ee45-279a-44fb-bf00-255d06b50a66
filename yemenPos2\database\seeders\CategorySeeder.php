<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name_ar' => 'مشروبات',
                'name_en' => 'Beverages',
                'description_ar' => 'جميع أنواع المشروبات الباردة والساخنة',
                'description_en' => 'All types of cold and hot beverages',
                'is_active' => true,
            ],
            [
                'name_ar' => 'مواد غذائية',
                'name_en' => 'Food Items',
                'description_ar' => 'المواد الغذائية الأساسية والمعلبات',
                'description_en' => 'Basic food items and canned goods',
                'is_active' => true,
            ],
            [
                'name_ar' => 'منظفات',
                'name_en' => 'Cleaning Products',
                'description_ar' => 'مواد التنظيف والمنظفات المنزلية',
                'description_en' => 'Cleaning materials and household detergents',
                'is_active' => true,
            ],
            [
                'name_ar' => 'أدوات شخصية',
                'name_en' => 'Personal Care',
                'description_ar' => 'أدوات العناية الشخصية والصحة',
                'description_en' => 'Personal care and health products',
                'is_active' => true,
            ],
            [
                'name_ar' => 'حلويات',
                'name_en' => 'Sweets',
                'description_ar' => 'الحلويات والشوكولاتة والسكاكر',
                'description_en' => 'Sweets, chocolates and candies',
                'is_active' => true,
            ],
            [
                'name_ar' => 'أدوات مكتبية',
                'name_en' => 'Office Supplies',
                'description_ar' => 'الأدوات المكتبية والقرطاسية',
                'description_en' => 'Office supplies and stationery',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
