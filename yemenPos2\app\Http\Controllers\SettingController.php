<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingController extends Controller
{
    /**
     * عرض صفحة الإعدادات
     */
    public function index()
    {
        $settings = Setting::orderBy('group')->orderBy('key')->get()->groupBy('group');
        return view('settings.index', compact('settings'));
    }

    /**
     * تحديث الإعدادات
     */
    public function update(Request $request)
    {
        try {
            // الحصول على جميع الإعدادات من قاعدة البيانات
            $allSettings = Setting::all();

            foreach ($allSettings as $setting) {
                $key = $setting->key;

                // معالجة القيم حسب النوع
                $processedValue = match ($setting->type) {
                    'boolean' => $request->get($key, '0') === '1' ? 'true' : 'false',
                    'json' => is_array($request->get($key)) ? json_encode($request->get($key)) : $request->get($key, $setting->value),
                    'file' => $this->handleFileUpload($request, $key, $setting),
                    default => $request->get($key, $setting->value),
                };

                // تحديث الإعداد إذا تم إرساله في الطلب أو كان boolean
                if ($setting->type === 'boolean' || $request->has($key)) {
                    $setting->update(['value' => $processedValue]);

                    // إضافة log للتتبع
                    if ($setting->type === 'boolean') {
                        \Log::info("تحديث إعداد boolean: {$key} = {$processedValue}", [
                            'request_value' => $request->get($key),
                            'old_value' => $setting->value,
                            'new_value' => $processedValue
                        ]);
                    }
                }
            }

            // مسح الكاش
            Cache::forget('settings');

            return redirect()->route('settings.index')
                ->with('success', 'تم تحديث الإعدادات بنجاح');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطأ في تحديث الإعدادات: ' . $e->getMessage()]);
        }
    }

    /**
     * معالجة رفع الملفات
     */
    private function handleFileUpload(Request $request, string $key, Setting $setting)
    {
        if (!$request->hasFile($key)) {
            return $setting->value; // إرجاع القيمة الحالية إذا لم يتم رفع ملف جديد
        }

        $file = $request->file($key);

        // التحقق من نوع الملف
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            throw new \Exception('نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو WebP');
        }

        // التحقق من حجم الملف (2MB)
        if ($file->getSize() > 2048 * 1024) {
            throw new \Exception('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت');
        }

        // حذف الملف القديم إذا وجد
        if ($setting->value && file_exists(public_path('storage/' . $setting->value))) {
            unlink(public_path('storage/' . $setting->value));
        }

        // رفع الملف الجديد
        $path = $file->store('settings', 'public');

        return $path;
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public function reset(Request $request)
    {
        try {
            $group = $request->get('group');

            if ($group) {
                // إعادة تعيين مجموعة معينة
                $this->resetGroup($group);
                $message = "تم إعادة تعيين إعدادات {$group} للقيم الافتراضية";
            } else {
                // إعادة تعيين جميع الإعدادات
                $this->resetAllSettings();
                $message = "تم إعادة تعيين جميع الإعدادات للقيم الافتراضية";
            }

            // مسح الكاش
            Cache::forget('settings');

            return redirect()->route('settings.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطأ في إعادة التعيين: ' . $e->getMessage()]);
        }
    }

    /**
     * إعادة تعيين مجموعة معينة
     */
    private function resetGroup(string $group)
    {
        $defaultSettings = $this->getDefaultSettings();

        foreach ($defaultSettings as $key => $default) {
            if (isset($default['group']) && $default['group'] === $group) {
                Setting::where('key', $key)->update(['value' => $default['value']]);
            }
        }
    }

    /**
     * إعادة تعيين جميع الإعدادات
     */
    private function resetAllSettings()
    {
        $defaultSettings = $this->getDefaultSettings();

        foreach ($defaultSettings as $key => $default) {
            Setting::where('key', $key)->update(['value' => $default['value']]);
        }
    }

    /**
     * الحصول على الإعدادات الافتراضية
     */
    private function getDefaultSettings(): array
    {
        return [
            // إعدادات عامة
            'app_name' => ['value' => 'نظام نقطة المبيعات', 'group' => 'general'],
            'app_description' => ['value' => 'نظام إدارة المبيعات والمخزون', 'group' => 'general'],
            'app_logo' => ['value' => '', 'group' => 'general'],
            'app_favicon' => ['value' => '', 'group' => 'general'],
            'timezone' => ['value' => 'Asia/Riyadh', 'group' => 'general'],
            'language' => ['value' => 'ar', 'group' => 'general'],
            'currency' => ['value' => 'YER', 'group' => 'general'],
            'currency_symbol' => ['value' => 'ر.ي', 'group' => 'general'],

            // إعدادات الفواتير
            'invoice_prefix' => ['value' => 'INV-', 'group' => 'invoice'],
            'invoice_start_number' => ['value' => '1', 'group' => 'invoice'],
            'invoice_footer' => ['value' => 'شكراً لتعاملكم معنا', 'group' => 'invoice'],
            'show_barcode_on_invoice' => ['value' => 'true', 'group' => 'invoice'],
            'auto_print_invoice' => ['value' => 'false', 'group' => 'invoice'],

            // إعدادات المخزون
            'low_stock_threshold' => ['value' => '10', 'group' => 'inventory'],
            'auto_generate_sku' => ['value' => 'true', 'group' => 'inventory'],
            'auto_generate_barcode' => ['value' => 'true', 'group' => 'inventory'],
            'track_inventory' => ['value' => 'true', 'group' => 'inventory'],

            // إعدادات الدفع
            'default_payment_method' => ['value' => 'نقدي', 'group' => 'payment'],
            'allow_partial_payment' => ['value' => 'true', 'group' => 'payment'],
            'payment_terms' => ['value' => '30', 'group' => 'payment'],

            // إعدادات الإشعارات
            'enable_email_notifications' => ['value' => 'false', 'group' => 'notifications'],
            'enable_sms_notifications' => ['value' => 'false', 'group' => 'notifications'],
            'low_stock_notifications' => ['value' => 'true', 'group' => 'notifications'],

            // إعدادات النسخ الاحتياطي
            'auto_backup' => ['value' => 'false', 'group' => 'backup'],
            'backup_frequency' => ['value' => 'daily', 'group' => 'backup'],
            'backup_retention_days' => ['value' => '30', 'group' => 'backup'],
        ];
    }

    /**
     * تصدير الإعدادات
     */
    public function export()
    {
        try {
            $settings = Setting::all()->mapWithKeys(function ($setting) {
                return [$setting->key => $setting->value];
            });

            $filename = 'settings_backup_' . date('Y-m-d_H-i-s') . '.json';

            return response()->json($settings)
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطأ في تصدير الإعدادات: ' . $e->getMessage()]);
        }
    }

    /**
     * استيراد الإعدادات
     */
    public function import(Request $request)
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json|max:1024'
        ]);

        try {
            $file = $request->file('settings_file');
            $content = file_get_contents($file->getPathname());
            $settings = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('ملف JSON غير صالح');
            }

            foreach ($settings as $key => $value) {
                Setting::where('key', $key)->update(['value' => $value]);
            }

            // مسح الكاش
            Cache::forget('settings');

            return redirect()->route('settings.index')
                ->with('success', 'تم استيراد الإعدادات بنجاح');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطأ في استيراد الإعدادات: ' . $e->getMessage()]);
        }
    }
}
