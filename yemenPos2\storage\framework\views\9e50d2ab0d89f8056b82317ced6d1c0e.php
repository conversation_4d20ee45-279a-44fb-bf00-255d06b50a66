<?php $__env->startSection('title', 'كشف حساب العملاء'); ?>
<?php $__env->startSection('page-title', 'كشف حساب العملاء'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-file-invoice-dollar text-primary me-2"></i>
                        كشف حساب العملاء
                    </h2>
                    <p class="text-muted mb-0">عرض تفصيلي لحساب العميل مع إمكانية التصدير والإرسال</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر البحث
            </h5>
        </div>
        <div class="card-body">
            <form id="statementForm" method="GET">
                <div class="row">
                    <!-- اختيار العميل -->
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">العميل <span class="text-danger">*</span></label>
                            <select class="form-select" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($customer->id); ?>"
                                        <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                    <?php echo e($customer->name); ?>

                                    <?php if($customer->phone): ?>
                                        - <?php echo e($customer->phone); ?>

                                    <?php endif; ?>
                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <!-- نوع الفترة -->
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">نوع الفترة</label>
                            <select class="form-select" id="periodType" name="period_type">
                                <option value="custom" <?php echo e(request('period_type') == 'custom' ? 'selected' : ''); ?>>فترة مخصصة</option>
                                <option value="month" <?php echo e(request('period_type') == 'month' ? 'selected' : ''); ?>>شهر محدد</option>
                            </select>
                        </div>
                    </div>

                    <!-- السنة -->
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">السنة</label>
                            <select class="form-select" name="year">
                                <?php for($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                <option value="<?php echo e($y); ?>" <?php echo e((request('year') ?? date('Y')) == $y ? 'selected' : ''); ?>>
                                    <?php echo e($y); ?>

                                </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- فترة مخصصة -->
                <div class="row" id="customPeriod" style="<?php echo e(request('period_type') == 'month' ? 'display: none;' : ''); ?>">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from"
                                   value="<?php echo e(request('date_from') ?? date('Y-m-01')); ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to"
                                   value="<?php echo e(request('date_to') ?? date('Y-m-t')); ?>">
                        </div>
                    </div>
                </div>

                <!-- شهر محدد -->
                <div class="row" id="monthPeriod" style="<?php echo e(request('period_type') != 'month' ? 'display: none;' : ''); ?>">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الشهر</label>
                            <select class="form-select" name="month">
                                <option value="">اختر الشهر</option>
                                <?php for($m = 1; $m <= 12; $m++): ?>
                                <option value="<?php echo e($m); ?>" <?php echo e(request('month') == $m ? 'selected' : ''); ?>>
                                    <?php echo e(\Carbon\Carbon::createFromDate(null, $m, 1)->translatedFormat('F')); ?>

                                </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>عرض كشف الحساب
                    </button>

                    <?php if($selectedCustomer): ?>
                    <button type="button" class="btn btn-success" onclick="exportPdf()">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>

                    <button type="button" class="btn btn-info" onclick="sendWhatsApp()">
                        <i class="fab fa-whatsapp me-2"></i>إرسال واتساب
                    </button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <?php if($selectedCustomer && $statement): ?>
    <!-- معلومات العميل -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-user me-2"></i>
                معلومات العميل: <?php echo e($selectedCustomer->name); ?>

            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>الاسم:</strong> <?php echo e($selectedCustomer->name); ?>

                </div>
                <div class="col-md-3">
                    <strong>الهاتف:</strong> <?php echo e($selectedCustomer->phone ?? 'غير محدد'); ?>

                </div>
                <div class="col-md-3">
                    <strong>العنوان:</strong> <?php echo e($selectedCustomer->address ?? 'غير محدد'); ?>

                </div>
                <div class="col-md-3">
                    <strong>الفترة:</strong>
                    <?php echo e($statement['date_from']->format('Y-m-d')); ?> إلى <?php echo e($statement['date_to']->format('Y-m-d')); ?>

                </div>
            </div>
        </div>
    </div>

    <!-- ملخص الحساب -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-balance-scale fa-2x mb-2"></i>
                    <h4><?php echo e(number_format($summary['opening_balance'], 2)); ?> ر.ي</h4>
                    <p class="mb-0">الرصيد الافتتاحي</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <h4><?php echo e(number_format($summary['total_sales'], 2)); ?> ر.ي</h4>
                    <p class="mb-0">إجمالي المبيعات</p>
                    <small>(<?php echo e($summary['sales_count']); ?> فاتورة)</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <h4><?php echo e(number_format($summary['total_paid'], 2)); ?> ر.ي</h4>
                    <p class="mb-0">إجمالي المدفوع</p>
                    <small>(<?php echo e($summary['payments_count']); ?> دفعة)</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4><?php echo e(number_format($summary['total_remaining'], 2)); ?> ر.ي</h4>
                    <p class="mb-0">إجمالي المتبقي</p>
                    <small>من فواتير الفترة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card <?php echo e($summary['closing_balance'] > 0 ? 'bg-danger' : 'bg-secondary'); ?> text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-2x mb-2"></i>
                    <h4><?php echo e(number_format($summary['closing_balance'], 2)); ?> ر.ي</h4>
                    <p class="mb-0">الرصيد الختامي</p>
                    <small><?php echo e($summary['closing_balance'] > 0 ? 'مديونية' : 'مسدد'); ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h4><?php echo e(number_format($summary['total_sales'] - $summary['total_paid'], 2)); ?> ر.ي</h4>
                    <p class="mb-0">صافي المديونية</p>
                    <small>للفترة المحددة</small>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الفواتير -->
    <?php if($statement['sales']->count() > 0): ?>
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل الفواتير (<?php echo e($statement['sales']->count()); ?> فاتورة)
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>الإجمالي</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>الحالة</th>
                            <th>طريقة الدفع</th>
                            <th>تفاصيل الدفعات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $statement['sales']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><strong><?php echo e($sale->invoice_number); ?></strong></td>
                            <td><?php echo e($sale->sale_date->format('Y-m-d')); ?></td>
                            <td><strong><?php echo e(number_format($sale->total_amount, 2)); ?> ر.ي</strong></td>
                            <td class="text-success"><strong><?php echo e(number_format($sale->paid_amount ?? 0, 2)); ?> ر.ي</strong></td>
                            <td class="text-danger"><strong><?php echo e(number_format($sale->remaining_amount, 2)); ?> ر.ي</strong></td>
                            <td>
                                <?php if($sale->status === 'مكتملة'): ?>
                                    <span class="badge bg-success">مكتملة</span>
                                <?php elseif($sale->status === 'دفع جزئي'): ?>
                                    <span class="badge bg-warning">دفع جزئي</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($sale->payment_method); ?></td>
                            <td>
                                <?php if($sale->salePayments->count() > 0): ?>
                                    <div class="small">
                                        <?php $__currentLoopData = $sale->salePayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="mb-1">
                                                <span class="badge bg-info"><?php echo e(number_format($payment->amount, 2)); ?> ر.ي</span>
                                                <small class="text-muted"><?php echo e($payment->created_at->format('m-d H:i')); ?></small>
                                                <?php if($payment->payment_method): ?>
                                                    <small class="text-muted">(<?php echo e($payment->payment_method); ?>)</small>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted small">لا توجد دفعات</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- ملاحظات توضيحية -->
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>
                ملاحظات مهمة حول كشف الحساب
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">📊 شرح الأرقام:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-circle text-primary me-2"></i><strong>الرصيد الافتتاحي:</strong> المديونيات المتراكمة من الفترات السابقة</li>
                        <li><i class="fas fa-circle text-success me-2"></i><strong>إجمالي المبيعات:</strong> مجموع فواتير الفترة المحددة</li>
                        <li><i class="fas fa-circle text-info me-2"></i><strong>إجمالي المدفوع:</strong> مجموع المدفوعات لفواتير الفترة</li>
                        <li><i class="fas fa-circle text-warning me-2"></i><strong>إجمالي المتبقي:</strong> المديونيات من فواتير الفترة فقط</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-danger">🧮 طريقة الحساب:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-calculator text-danger me-2"></i><strong>الرصيد الختامي =</strong> الرصيد الافتتاحي + إجمالي المتبقي</li>
                        <li><i class="fas fa-chart-line text-dark me-2"></i><strong>صافي المديونية =</strong> إجمالي المبيعات - إجمالي المدفوع</li>
                    </ul>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>تنبيه:</strong> تفاصيل الدفعات تظهر في عمود "تفاصيل الدفعات" لكل فاتورة مع التواريخ وطرق الدفع.
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
// تبديل نوع الفترة
document.getElementById('periodType').addEventListener('change', function() {
    const customPeriod = document.getElementById('customPeriod');
    const monthPeriod = document.getElementById('monthPeriod');

    if (this.value === 'month') {
        customPeriod.style.display = 'none';
        monthPeriod.style.display = 'block';
    } else {
        customPeriod.style.display = 'block';
        monthPeriod.style.display = 'none';
    }
});

// تصدير PDF
function exportPdf() {
    const form = document.getElementById('statementForm');
    const formData = new FormData(form);

    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value) params.append(key, value);
    }

    window.open(`<?php echo e(route('statements.export-pdf')); ?>?${params.toString()}`, '_blank');
}

// إرسال واتساب
function sendWhatsApp() {
    const form = document.getElementById('statementForm');
    const formData = new FormData(form);

    // إظهار مؤشر التحميل
    showAlert('info', 'جاري إنشاء كشف الحساب...');

    fetch('<?php echo e(route("statements.send-whatsapp")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);

            // إظهار تعليمات إرفاق الملف
            if (data.file_url) {
                const instructionsAlert = document.createElement('div');
                instructionsAlert.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                instructionsAlert.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 400px; max-width: 500px;';
                instructionsAlert.innerHTML = `
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تعليمات إرسال كشف الحساب:</strong><br><br>

                    <div class="mb-3">
                        <strong>الخطوة 1:</strong> تحميل الملف أو نسخ الرابط
                        <br>
                        <div class="btn-group mt-1" role="group">
                            <a href="${data.file_url}" target="_blank" class="btn btn-sm btn-primary">
                                <i class="fas fa-download me-1"></i>تحميل الملف
                            </a>
                            <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('${data.file_url}', this)">
                                <i class="fas fa-copy me-1"></i>نسخ الرابط
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <strong>الخطوة 2:</strong> فتح الواتساب
                        <br>
                        <button class="btn btn-sm btn-success mt-1" onclick="window.open('${data.whatsapp_url}', '_blank')">
                            <i class="fab fa-whatsapp me-1"></i>فتح الواتساب
                        </button>
                    </div>

                    <div class="mb-2">
                        <strong>الخطوة 3:</strong> إرفاق الملف في الواتساب
                        <br>
                        <small class="text-muted">
                            • اضغط على أيقونة المرفقات (📎) في الواتساب<br>
                            • اختر "مستند" أو "ملف"<br>
                            • ابحث عن الملف في مجلد التحميلات (Downloads)<br>
                            • اسم الملف: <strong>${data.file_name}</strong><br>
                            • ارفق الملف واكتب الرسالة وأرسل
                        </small>
                    </div>

                    <div class="alert alert-info p-2 mt-2">
                        <small>
                            <i class="fas fa-lightbulb me-1"></i>
                            <strong>نصيحة:</strong> يمكنك أيضاً نسخ رابط الملف ولصقه في الواتساب بدلاً من إرفاق الملف.
                        </small>
                    </div>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(instructionsAlert);

                // إزالة التنبيه بعد 30 ثانية
                setTimeout(() => {
                    if (instructionsAlert.parentNode) {
                        instructionsAlert.parentNode.removeChild(instructionsAlert);
                    }
                }, 30000);
            }

            // فتح الواتساب تلقائياً بعد 3 ثوانٍ
            setTimeout(() => {
                window.open(data.whatsapp_url, '_blank');
            }, 3000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('danger', 'حدث خطأ أثناء إرسال الرسالة');
    });
}

// دالة إظهار التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// دالة نسخ النص إلى الحافظة
function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>تم النسخ!';
        button.classList.remove('btn-outline-primary');
        button.classList.add('btn-success');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function(err) {
        console.error('فشل في نسخ النص: ', err);
        showAlert('danger', 'فشل في نسخ الرابط');
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/statements/index.blade.php ENDPATH**/ ?>