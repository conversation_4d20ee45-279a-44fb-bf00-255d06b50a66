<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->boolean('is_posted')->default(false)->after('status')->comment('هل تم ترحيل الفاتورة');
            $table->timestamp('posted_at')->nullable()->after('is_posted')->comment('تاريخ الترحيل');
            $table->unsignedBigInteger('posted_by')->nullable()->after('posted_at')->comment('من قام بالترحيل');

            $table->foreign('posted_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropForeign(['posted_by']);
            $table->dropColumn(['is_posted', 'posted_at', 'posted_by']);
        });
    }
};
