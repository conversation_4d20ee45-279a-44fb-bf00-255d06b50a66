@extends('layouts.app')

@section('title', 'نقطة البيع - نظام نقطة المبيعات')
@section('page-title', 'نقطة البيع')

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
<style>
    .pos-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 0;
        margin: 0;
    }

    .pos-header {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255,255,255,0.2);
        padding: 15px 30px;
        color: white;
    }

    .pos-main {
        display: flex;
        min-height: calc(100vh - 80px);
        overflow: hidden;
    }

    .cart-and-checkout {
        display: flex;
        flex-direction: column;
        min-width: 400px;
        max-width: 450px;
        min-height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .products-section {
        flex: 2;
        background: white;
        display: flex;
        flex-direction: column;
    }

    .cart-section {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 400px;
    }

    .checkout-section {
        background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
        color: white;
        border-top: 3px solid #f39c12;
        box-shadow: 0 -5px 15px rgba(0,0,0,0.3);
        flex-shrink: 0;
    }

    .search-bar {
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .search-input {
        position: relative;
    }

    .search-input input {
        width: 100%;
        padding: 15px 50px 15px 20px;
        border: 2px solid #e9ecef;
        border-radius: 25px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .search-input input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .search-input .search-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }

    .barcode-scanner {
        margin-top: 15px;
    }

    .barcode-input {
        display: flex;
        gap: 10px;
    }

    .barcode-input input {
        flex: 1;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }

    .scan-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .scan-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    }

    .categories-filter {
        padding: 0 20px 20px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .category-btn {
        background: white;
        border: 2px solid #e9ecef;
        color: #495057;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .category-btn:hover,
    .category-btn.active {
        background: #667eea;
        border-color: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .products-grid {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
        align-content: start;
    }

    .product-card {
        background: white;
        border-radius: 12px;
        padding: 10px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
        text-align: center;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .product-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #667eea;
    }

    .product-image {
        width: 50px;
        height: 50px;
        background: #f8f9fa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
    }

    .product-image i {
        font-size: 1.2rem;
        color: #6c757d;
    }

    .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .product-name {
        font-weight: 600;
        margin-bottom: 4px;
        color: #2c3e50;
        font-size: 12px;
        line-height: 1.2;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 28px;
    }

    .product-price {
        font-size: 13px;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 2px;
    }

    .product-stock {
        font-size: 10px;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 3px;
    }

    .stock-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #28a745;
    }

    .stock-indicator.low {
        background: #ffc107;
    }

    .stock-indicator.out {
        background: #dc3545;
    }

    .cart-header {
        padding: 20px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        text-align: center;
    }

    .cart-header h4 {
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .invoice-number {
        background: rgba(255,255,255,0.1);
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 14px;
        margin-top: 10px;
    }

    .customer-select {
        padding: 20px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .customer-select select {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 10px;
        background: rgba(255,255,255,0.1);
        color: white;
        font-size: 14px;
    }

    .customer-select select option {
        background: #2c3e50;
        color: white;
    }

    .customer-select-wrapper {
        position: relative;
    }

    .customer-select-wrapper select {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 10px;
        background: rgba(255,255,255,0.1);
        color: white;
        font-size: 14px;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }

    .customer-select-wrapper::after {
        content: '\f078';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255,255,255,0.6);
        pointer-events: none;
    }

    .cart-items {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        min-height: 200px;
    }

    .cart-item {
        background: rgba(255,255,255,0.05);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        border: 1px solid rgba(255,255,255,0.1);
    }

    .cart-item:hover {
        background: rgba(255,255,255,0.1);
        transform: translateX(5px);
    }

    .cart-item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .cart-item-name {
        font-weight: 600;
        font-size: 14px;
        line-height: 1.3;
        flex: 1;
    }

    .cart-item-remove {
        background: #dc3545;
        border: none;
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .cart-item-remove:hover {
        background: #c82333;
        transform: scale(1.1);
    }

    .cart-item-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 5px;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 5px;
    }

    .qty-btn {
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .qty-btn:hover {
        background: rgba(255,255,255,0.3);
        transform: scale(1.1);
    }

    .qty-input {
        background: transparent;
        border: none;
        color: white;
        text-align: center;
        width: 40px;
        font-weight: bold;
    }

    .cart-item-total {
        font-weight: bold;
        color: #28a745;
        font-size: 16px;
    }

    .cart-summary {
        padding: 20px;
        border-top: 1px solid rgba(255,255,255,0.1);
        background: rgba(0,0,0,0.2);
        flex-shrink: 0;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .summary-row.total {
        font-size: 18px;
        font-weight: bold;
        color: #28a745;
        border-top: 1px solid rgba(255,255,255,0.1);
        padding-top: 10px;
        margin-top: 15px;
    }

    /* صف مزدوج للخصم والضريبة */
    .summary-row-dual {
        display: flex;
        gap: 15px;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .summary-item {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .tax-section {
        position: relative;
    }

    .tax-input-container {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 2px;
    }

    .tax-rate-display {
        font-size: 0.75rem;
        opacity: 0.8;
        line-height: 1;
        color: rgba(255, 255, 255, 0.7);
    }

    .discount-input,
    .tax-input {
        width: 80px;
        padding: 5px 8px;
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 5px;
        background: rgba(255,255,255,0.1);
        color: white;
        text-align: center;
        font-size: 14px;
    }

    .discount-input:focus,
    .tax-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
    }

    .payment-section {
        padding: 20px;
        border-top: 1px solid rgba(255,255,255,0.1);
        flex-shrink: 0;
        background: rgba(0,0,0,0.3);
        margin-top: auto;
    }

    .payment-methods {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-bottom: 20px;
    }

    .payment-method {
        background: rgba(255,255,255,0.1);
        border: 2px solid rgba(255,255,255,0.2);
        color: white;
        padding: 12px;
        border-radius: 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        font-weight: 500;
    }

    .payment-method:hover,
    .payment-method.active {
        background: #28a745;
        border-color: #28a745;
        transform: translateY(-2px);
    }

    .checkout-btn {
        width: 100%;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 15px;
        border-radius: 10px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .checkout-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
    }

    .checkout-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .empty-cart {
        text-align: center;
        padding: 40px 20px;
        color: rgba(255,255,255,0.6);
    }

    .empty-cart i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }

    .loading-spinner i {
        font-size: 2rem;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 9999;
        transform: translateX(400px);
        transition: transform 0.3s ease;
    }

    .notification.show {
        transform: translateX(0);
    }

    .notification.error {
        background: #dc3545;
    }

    .notification.warning {
        background: #ffc107;
        color: #212529;
    }

    .payment-amount-section {
        margin: 15px 0;
        padding: 15px;
        background: rgba(255,255,255,0.05);
        border-radius: 8px;
        border: 1px solid rgba(255,255,255,0.1);
    }

    .payment-amount-input {
        position: relative;
    }

    .payment-amount-input input {
        background: rgba(255,255,255,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        color: white;
        padding: 12px;
        border-radius: 6px;
        width: 100%;
        font-size: 16px;
    }

    .payment-amount-input input:focus {
        background: rgba(255,255,255,0.15);
        border-color: #f39c12;
        outline: none;
        box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.2);
    }

    .payment-amount-input input::placeholder {
        color: rgba(255,255,255,0.6);
    }

    .remaining-amount {
        margin-top: 10px;
        padding: 8px 12px;
        background: rgba(231, 76, 60, 0.1);
        border: 1px solid rgba(231, 76, 60, 0.3);
        border-radius: 4px;
        color: #e74c3c;
        font-weight: bold;
        text-align: center;
    }

    .remaining-amount.positive {
        background: rgba(46, 204, 113, 0.1);
        border-color: rgba(46, 204, 113, 0.3);
        color: #2ecc71;
    }

    .checkout-btn.partial-payment {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        border: none;
        color: white;
        animation: pulse 2s infinite;
    }

    .checkout-btn.partial-payment:hover {
        background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
        transform: translateY(-2px);
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(243, 156, 18, 0); }
        100% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0); }
    }

    /* Responsive Design */
    @media (min-width: 1200px) {
        .cart-items {
            max-height: calc(100vh - 400px);
        }

        .cart-section {
            max-width: 500px;
        }
    }

    @media (min-width: 1400px) {
        .cart-items {
            max-height: calc(100vh - 350px);
        }
    }

    @media (max-width: 768px) {
        .pos-main {
            flex-direction: column;
            height: auto;
        }

        .cart-section {
            min-width: auto;
            max-width: none;
            order: -1;
            height: auto;
        }

        .cart-items {
            max-height: 300px;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
        }

        .payment-methods {
            grid-template-columns: 1fr;
        }

        /* تحسين responsive للصف المزدوج */
        .summary-row-dual {
            flex-direction: column;
            gap: 8px;
        }

        .summary-item {
            justify-content: space-between;
        }

        .discount-input, .tax-input {
            width: 70px;
        }
    }
</style>
@endpush

@section('content')
<div class="pos-container">
    <!-- Header -->
    <div class="pos-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">
                    <i class="fas fa-cash-register me-2"></i>
                    نقطة البيع
                </h4>
                <small class="opacity-75">{{ now()->format('l, F j, Y - H:i') }}</small>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div class="text-end">
                    <div class="fw-bold">{{ auth()->user()->name }}</div>
                    <small class="opacity-75">كاشير</small>
                </div>
                <div class="bg-white bg-opacity-25 rounded-circle p-2">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pos-main">
        <!-- Products Section -->
        <div class="products-section">
            <!-- Barcode Scanner -->
            <div class="search-bar">
                <div class="barcode-scanner">
                    <label class="form-label fw-bold">
                        <i class="fas fa-barcode me-2"></i>
                        مسح الباركود
                    </label>
                    <div class="barcode-input">
                        <input type="text" id="barcodeInput" placeholder="امسح أو أدخل الباركود...">
                        <button class="scan-btn" onclick="scanBarcode()">
                            <i class="fas fa-qrcode me-2"></i>
                            مسح
                        </button>
                    </div>
                </div>
            </div>

            <!-- Categories Filter -->
            <div class="categories-filter">
                <button class="category-btn active" data-category="">جميع المنتجات</button>
                @foreach($categories as $category)
                    <button class="category-btn" data-category="{{ $category->id }}">
                        {{ $category->name_ar }}
                    </button>
                @endforeach
            </div>

            <!-- Search Bar -->
            <div class="search-bar">
                <div class="search-input">
                    <input type="text" id="productSearch" placeholder="ابحث عن المنتجات بالاسم أو الكود...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-grid" id="productsGrid">
                <div class="loading-spinner" id="loadingSpinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>جاري تحميل المنتجات...</p>
                </div>
            </div>
        </div>

        <!-- Cart and Checkout Container -->
        <div class="cart-and-checkout">
            <!-- Cart Section -->
            <div class="cart-section">
                <!-- Cart Header -->
                <div class="cart-header">
                    <h4>
                        <i class="fas fa-shopping-cart"></i>
                        سلة المشتريات
                    </h4>
                    <div class="invoice-number" id="invoiceNumber">
                        فاتورة: INV-{{ date('Y') }}-{{ str_pad(1, 6, '0', STR_PAD_LEFT) }}
                    </div>
                </div>

                <!-- Customer Selection -->
                <div class="customer-select">
                    <label class="form-label fw-bold mb-2">
                        <i class="fas fa-user me-2"></i>
                        العميل
                    </label>
                    <div class="customer-select-wrapper">
                        <select id="customerSelect" class="form-select">
                            <option value="">عميل عادي</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Cart Items -->
                <div class="cart-items" id="cartItems">
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <h6>السلة فارغة</h6>
                        <p>أضف منتجات لبدء البيع</p>
                    </div>
                </div>
            </div>

            <!-- Checkout Section (أسفل السلة) -->
            <div class="checkout-section">
            <!-- Cart Summary -->
            <div class="cart-summary" id="cartSummary" style="display: block !important;">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0.00 ر.ي</span>
                </div>

                <!-- صف الخصم والضريبة جنباً إلى جنب -->
                <div class="summary-row-dual">
                    <div class="summary-item">
                        <span>الخصم:</span>
                        <input type="number" class="discount-input" id="discountInput" value="0" min="0" step="0.01">
                    </div>
                    <div class="summary-item tax-section" id="taxSection">
                        <span id="taxLabel">الضريبة:</span>
                        <div class="tax-input-container">
                            <input type="number" class="tax-input" id="taxInput" value="0" min="0" step="0.01">
                            <div class="tax-rate-display" id="taxRateDisplay" style="display: none;">
                                <small class="text-muted">معدل: <span id="currentTaxRate">0</span>%</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="summary-row total">
                    <span>الإجمالي:</span>
                    <span id="totalAmount">0.00 ر.ي</span>
                </div>
            </div>

            <!-- Payment Section -->
            <div class="payment-section" id="paymentSection" style="display: block !important;">
                <label class="form-label fw-bold mb-3">
                    <i class="fas fa-credit-card me-2"></i>
                    طريقة الدفع
                </label>
                <div class="payment-methods">
                    <button class="payment-method active" data-method="نقدي">
                        <i class="fas fa-money-bill-wave d-block mb-2"></i>
                        نقدي
                    </button>
                    <button class="payment-method" data-method="بطاقة ائتمان">
                        <i class="fas fa-credit-card d-block mb-2"></i>
                        بطاقة
                    </button>
                    <button class="payment-method" data-method="تحويل بنكي">
                        <i class="fas fa-university d-block mb-2"></i>
                        تحويل
                    </button>
                    <button class="payment-method" data-method="آجل">
                        <i class="fas fa-calendar-alt d-block mb-2"></i>
                        آجل
                    </button>
                </div>

                <!-- Payment Amount Section (للعملاء المحددين) -->
                <div class="payment-amount-section" id="paymentAmountSection" style="display: none;">
                    <label class="form-label fw-bold mb-2">
                        <i class="fas fa-money-bill me-2"></i>
                        المبلغ المدفوع
                    </label>
                    <div class="payment-amount-input">
                        <input type="number" id="paidAmountInput" class="form-control" placeholder="أدخل المبلغ المدفوع..." step="0.01" min="0">
                        <div class="remaining-amount" id="remainingAmount">
                            المتبقي: <span id="remainingAmountValue">0.00</span> ر.ي
                        </div>
                    </div>
                </div>

                <button class="checkout-btn" id="checkoutBtn" onclick="processCheckout()">
                    <i class="fas fa-check me-2"></i>
                    إتمام البيع
                </button>
            </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    تم إتمام البيع بنجاح
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="fas fa-receipt fa-3x text-success mb-3"></i>
                    <h4 id="successInvoiceNumber"></h4>
                    <div id="successPaymentStatus"></div>
                    <p class="text-muted">تم إنشاء الفاتورة بنجاح</p>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="printInvoice()">
                        <i class="fas fa-print me-2"></i>
                        طباعة الفاتورة
                    </button>
                    <button class="btn btn-info" onclick="printInvoice()">
                        <i class="fas fa-print me-2"></i>
                        إعادة طباعة
                    </button>
                    <button class="btn btn-warning" onclick="continueCurrentSale()">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الفاتورة
                    </button>
                    <button class="btn btn-success" onclick="newSale()">
                        <i class="fas fa-plus me-2"></i>
                        بيع جديد
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// متغيرات عامة
let cart = [];
let products = [];
let selectedPaymentMethod = 'نقدي';
let currentSaleId = null;
let selectedCategoryId = '';

// إعدادات الضريبة
let taxSettings = {
    tax_enabled: false,
    tax_rate: 0,
    tax_name: 'ضريبة القيمة المضافة'
};

// تحميل المنتجات عند بدء التشغيل
document.addEventListener('DOMContentLoaded', function() {
    // تحميل إعدادات الضريبة
    loadTaxSettings();

    // تحميل السلة المحفوظة
    loadCartFromStorage();

    // تحميل المنتجات مع تأخير قصير
    setTimeout(() => {
        loadProducts();
    }, 500);

    setupEventListeners();
    updateInvoiceNumber();
});

// تحميل إعدادات الضريبة
async function loadTaxSettings() {
    try {
        const response = await fetch('/pos/tax-settings');
        if (response.ok) {
            taxSettings = await response.json();
            console.log('تم تحميل إعدادات الضريبة:', taxSettings);

            // تحديث واجهة الضريبة
            updateTaxInterface();
        }
    } catch (error) {
        console.error('خطأ في تحميل إعدادات الضريبة:', error);
    }
}

// تحديث واجهة الضريبة
function updateTaxInterface() {
    const taxSection = document.getElementById('taxSection');
    const taxLabel = document.getElementById('taxLabel');
    const taxRateDisplay = document.getElementById('taxRateDisplay');
    const currentTaxRate = document.getElementById('currentTaxRate');

    if (taxSettings.tax_enabled) {
        // إظهار قسم الضريبة
        taxSection.style.display = 'flex';

        // تحديث التسمية
        if (taxLabel) {
            taxLabel.textContent = taxSettings.tax_name || 'الضريبة:';
        }

        // إظهار معدل الضريبة
        if (taxSettings.tax_rate > 0) {
            taxRateDisplay.style.display = 'block';
            currentTaxRate.textContent = taxSettings.tax_rate;
        } else {
            taxRateDisplay.style.display = 'none';
        }

        // تعيين القيمة التلقائية إذا لم تكن محددة
        const taxInput = document.getElementById('taxInput');
        if (!taxInput.value || taxInput.value === '0') {
            // حساب الضريبة تلقائياً
            calculateAndSetTax();
        }
    } else {
        // إخفاء قسم الضريبة
        taxSection.style.display = 'none';
        document.getElementById('taxInput').value = '0';
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث في المنتجات
    document.getElementById('productSearch').addEventListener('input', debounce(searchProducts, 300));

    // فلترة التصنيفات
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            selectedCategoryId = this.dataset.category;
            console.log('تم اختيار التصنيف:', selectedCategoryId);
            filterByCategory(selectedCategoryId);
        });
    });

    // طرق الدفع
    document.querySelectorAll('.payment-method').forEach(btn => {
        btn.addEventListener('click', function() {
            const method = this.dataset.method;
            const customerSelect = document.getElementById('customerSelect');

            // منع الدفع الآجل إذا لم يتم تحديد عميل أو كان عميل عادي
            if (method === 'آجل' && (!customerSelect.value || customerSelect.value === '' || customerSelect.value === 'عادي')) {
                showNotification('يجب تحديد عميل محدد للدفع الآجل (ليس عميل عادي)', 'warning');
                return;
            }

            document.querySelectorAll('.payment-method').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            selectedPaymentMethod = method;

            // تحديث واجهة الدفع حسب الطريقة
            updatePaymentInterface();
            saveCartToStorage(); // حفظ عند تغيير طريقة الدفع
        });
    });

    // حساب المجاميع عند تغيير الخصم أو الضريبة
    document.getElementById('discountInput').addEventListener('input', function() {
        calculateTotals(); // سيحسب الضريبة تلقائياً إذا كانت مفعلة
        saveCartToStorage(); // حفظ عند تغيير الخصم
    });

    // السماح بتعديل الضريبة يدوياً إذا أراد المستخدم ذلك
    document.getElementById('taxInput').addEventListener('input', function() {
        // إذا كانت الضريبة مفعلة، إظهار تحذير عند التعديل اليدوي
        if (taxSettings.tax_enabled && taxSettings.tax_rate > 0) {
            console.log('تم تعديل الضريبة يدوياً - لن يتم الحساب التلقائي');
        }
        calculateTotals();
        saveCartToStorage(); // حفظ عند تغيير الضريبة
    });

    // حفظ عند تغيير العميل
    document.getElementById('customerSelect').addEventListener('change', function() {
        // إذا تم إلغاء تحديد العميل وكانت طريقة الدفع آجل، تغيير إلى نقدي
        if ((!this.value || this.value === '') && selectedPaymentMethod === 'آجل') {
            document.querySelectorAll('.payment-method').forEach(b => b.classList.remove('active'));
            document.querySelector('.payment-method[data-method="نقدي"]').classList.add('active');
            selectedPaymentMethod = 'نقدي';
            showNotification('تم تغيير طريقة الدفع إلى نقدي', 'info');
        }

        togglePaymentAmountSection();
        updatePaymentInterface();
        saveCartToStorage();
    });

    // تحديث المبلغ المتبقي عند تغيير المبلغ المدفوع
    document.getElementById('paidAmountInput').addEventListener('input', function() {
        updateRemainingAmount();
        saveCartToStorage();
    });

    // مسح الباركود عند الضغط على Enter
    document.getElementById('barcodeInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            scanBarcode();
        }
    });
}

// تحميل المنتجات
async function loadProducts(search = '', categoryId = '') {
    showLoading(true);
    console.log('تحميل المنتجات - البحث:', search, 'التصنيف:', categoryId);

    try {
        const formData = new FormData();
        formData.append('search', search);
        formData.append('category_id', categoryId);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        const response = await fetch('/pos/search-products', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        products = data;
        console.log('تم تحميل المنتجات:', products.length, 'منتج');
        displayProducts(products);
    } catch (error) {
        console.error('خطأ في تحميل المنتجات:', error);
        showNotification('خطأ في تحميل المنتجات: ' + error.message, 'error');
        // عرض رسالة خطأ في الشبكة
        document.getElementById('productsGrid').innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h5 class="text-danger">خطأ في تحميل المنتجات</h5>
                <p class="text-muted">${error.message}</p>
                <button class="btn btn-primary" onclick="loadProducts('${search}', '${categoryId}')">
                    <i class="fas fa-refresh me-2"></i>
                    إعادة المحاولة
                </button>
            </div>
        `;
    } finally {
        showLoading(false);
    }
}

// عرض المنتجات
function displayProducts(products) {
    const grid = document.getElementById('productsGrid');

    if (products.length === 0) {
        grid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد منتجات</h5>
                <p class="text-muted">جرب البحث بكلمات أخرى</p>
            </div>
        `;
        return;
    }

    grid.innerHTML = products.map(product => `
        <div class="product-card" onclick="addToCart(${product.id})">
            <div class="product-image">
                ${getProductImage(product)}
            </div>
            <div class="product-info">
                <div class="product-name">${product.name_ar}</div>
                <div class="product-price">${parseFloat(product.selling_price).toFixed(2)} ر.ي</div>
                <div class="product-stock">
                    <span class="stock-indicator ${getStockClass(product.stock_quantity)}"></span>
                    ${product.stock_quantity}
                </div>
            </div>
        </div>
    `).join('');
}

// الحصول على صورة المنتج
function getProductImage(product) {
    // صور وهمية حسب التصنيف
    const categoryImages = {
        'مشروبات': 'https://via.placeholder.com/50x50/4CAF50/white?text=🥤',
        'مواد غذائية': 'https://via.placeholder.com/50x50/FF9800/white?text=🍞',
        'منظفات': 'https://via.placeholder.com/50x50/2196F3/white?text=🧽',
        'أدوات شخصية': 'https://via.placeholder.com/50x50/9C27B0/white?text=🧴',
        'حلويات': 'https://via.placeholder.com/50x50/E91E63/white?text=🍬',
        'أدوات مكتبية': 'https://via.placeholder.com/50x50/607D8B/white?text=📝'
    };

    const defaultImage = 'https://via.placeholder.com/50x50/9E9E9E/white?text=📦';

    if (product.image) {
        return `<img src="/storage/${product.image}" alt="${product.name_ar}" onerror="this.src='${defaultImage}'">`;
    }

    return `<img src="${defaultImage}" alt="${product.name_ar}">`;
}



// تحديد حالة المخزون
function getStockClass(quantity) {
    if (quantity <= 0) return 'out';
    if (quantity <= 10) return 'low';
    return '';
}

// البحث في المنتجات
function searchProducts() {
    const search = document.getElementById('productSearch').value;
    console.log('البحث:', search, 'التصنيف المختار:', selectedCategoryId);
    loadProducts(search, selectedCategoryId);
}

// فلترة حسب التصنيف
function filterByCategory(categoryId) {
    const search = document.getElementById('productSearch').value;
    console.log('فلترة التصنيف:', categoryId, 'البحث:', search);
    loadProducts(search, categoryId);
}

// مسح الباركود
async function scanBarcode() {
    const barcode = document.getElementById('barcodeInput').value.trim();

    if (!barcode) {
        showNotification('يرجى إدخال الباركود', 'warning');
        return;
    }

    try {
        const response = await fetch('/pos/search-barcode', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ barcode: barcode })
        });

        const result = await response.json();

        if (result.success) {
            addToCart(result.product.id);
            document.getElementById('barcodeInput').value = '';
            showNotification('تم إضافة المنتج للسلة', 'success');
        } else {
            showNotification(result.message, 'error');
        }
    } catch (error) {
        console.error('خطأ في مسح الباركود:', error);
        showNotification('خطأ في مسح الباركود', 'error');
    }
}

// إضافة منتج للسلة
function addToCart(productId) {
    const product = products.find(p => p.id === productId);

    if (!product) {
        showNotification('المنتج غير موجود', 'error');
        return;
    }

    if (product.stock_quantity <= 0) {
        showNotification('المنتج غير متوفر في المخزون', 'error');
        return;
    }

    const existingItem = cart.find(item => item.product_id === productId);

    if (existingItem) {
        if (existingItem.quantity >= product.stock_quantity) {
            showNotification('الكمية المطلوبة غير متوفرة', 'warning');
            return;
        }
        existingItem.quantity++;
    } else {
        cart.push({
            product_id: productId,
            name: product.name_ar,
            price: parseFloat(product.selling_price),
            quantity: 1,
            stock: product.stock_quantity,
            unit: product.unit
        });
    }

    updateCartDisplay();

    // حساب الضريبة تلقائياً عند إضافة منتج
    if (taxSettings.tax_enabled) {
        calculateAndSetTax();
    }

    showNotification('تم إضافة المنتج للسلة', 'success');
}

// حفظ السلة في localStorage
function saveCartToStorage() {
    try {
        const cartData = {
            cart: cart,
            selectedPaymentMethod: selectedPaymentMethod,
            customerId: document.getElementById('customerSelect').value,
            discount: document.getElementById('discountInput').value,
            tax: document.getElementById('taxInput').value,
            timestamp: Date.now()
        };
        localStorage.setItem('pos_cart', JSON.stringify(cartData));
        console.log('تم حفظ السلة في localStorage');
    } catch (error) {
        console.error('خطأ في حفظ السلة:', error);
    }
}

// تحميل السلة من localStorage
function loadCartFromStorage() {
    try {
        const savedCart = localStorage.getItem('pos_cart');
        if (savedCart) {
            const cartData = JSON.parse(savedCart);

            // التحقق من أن البيانات ليست قديمة جداً (أكثر من 24 ساعة)
            const hoursPassed = (Date.now() - cartData.timestamp) / (1000 * 60 * 60);
            if (hoursPassed > 24) {
                localStorage.removeItem('pos_cart');
                console.log('تم حذف السلة القديمة');
                return;
            }

            // استعادة البيانات
            cart = cartData.cart || [];
            selectedPaymentMethod = cartData.selectedPaymentMethod || 'نقدي';

            // استعادة العميل المختار
            if (cartData.customerId) {
                setTimeout(() => {
                    document.getElementById('customerSelect').value = cartData.customerId;
                    if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                        $('.selectpicker').selectpicker('refresh');
                    }
                }, 100);
            }

            // استعادة الخصم والضريبة
            setTimeout(() => {
                if (cartData.discount) {
                    document.getElementById('discountInput').value = cartData.discount;
                }
                if (cartData.tax) {
                    document.getElementById('taxInput').value = cartData.tax;
                }

                // استعادة طريقة الدفع
                document.querySelectorAll('.payment-method').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.method === selectedPaymentMethod) {
                        btn.classList.add('active');
                    }
                });

                updateCartDisplay();
                togglePaymentAmountSection(); // تحديث قسم الدفع الجزئي
            }, 200);

            console.log('تم تحميل السلة من localStorage:', cart.length, 'منتج');
        }
    } catch (error) {
        console.error('خطأ في تحميل السلة:', error);
        localStorage.removeItem('pos_cart');
    }
}

// مسح السلة من localStorage
function clearCartFromStorage() {
    localStorage.removeItem('pos_cart');
    console.log('تم مسح السلة من localStorage');
}

// تصفير جميع المجاميع
function resetTotals() {
    document.getElementById('subtotal').textContent = '0.00 ر.ي';
    document.getElementById('totalAmount').textContent = '0.00 ر.ي';

    // إخفاء قسم الدفع الجزئي
    const paymentAmountSection = document.getElementById('paymentAmountSection');
    if (paymentAmountSection) {
        paymentAmountSection.style.display = 'none';
    }

    // تحديث زر الدفع
    const checkoutBtn = document.getElementById('checkoutBtn');
    if (checkoutBtn) {
        checkoutBtn.innerHTML = `
            <i class="fas fa-check me-2"></i>
            إتمام البيع
        `;
        checkoutBtn.className = 'checkout-btn';
    }
}

// التحقق من وجود فاتورة مكتملة (تم إلغاؤها)
function checkCompletedSale() {
    // تم إلغاء هذه الميزة - لا نريد إشعارات مزعجة
    try {
        const completedSale = localStorage.getItem('completed_sale');
        if (completedSale) {
            const saleData = JSON.parse(completedSale);

            // التحقق من أن البيانات ليست قديمة جداً (أكثر من ساعة)
            const hoursPassed = (Date.now() - saleData.timestamp) / (1000 * 60 * 60);
            if (hoursPassed > 1) {
                localStorage.removeItem('completed_sale');
                return;
            }

            // فقط استعادة معرف الفاتورة بدون إشعار
            currentSaleId = saleData.sale_id;
        }
    } catch (error) {
        console.error('خطأ في تحميل الفاتورة المكتملة:', error);
        localStorage.removeItem('completed_sale');
    }
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');

    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <h6>السلة فارغة</h6>
                <p>أضف منتجات لبدء البيع</p>
            </div>
        `;

        // تصفير المجاميع عند السلة الفارغة
        resetTotals();

        clearCartFromStorage(); // مسح السلة الفارغة
        return;
    }

    cartItems.innerHTML = cart.map((item, index) => `
        <div class="cart-item">
            <div class="cart-item-header">
                <div class="cart-item-name">${item.name}</div>
                <button class="cart-item-remove" onclick="removeFromCart(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="cart-item-controls">
                <div class="quantity-controls">
                    <button class="qty-btn" onclick="updateQuantity(${index}, -1)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" class="qty-input" value="${item.quantity}"
                           onchange="setQuantity(${index}, this.value)" min="1" max="${item.stock}">
                    <button class="qty-btn" onclick="updateQuantity(${index}, 1)">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="cart-item-total">${(item.price * item.quantity).toFixed(2)} ر.ي</div>
            </div>
        </div>
    `).join('');

    calculateTotals();

    // حساب الضريبة تلقائياً عند تحديث السلة
    if (taxSettings.tax_enabled) {
        calculateAndSetTax();
    }

    saveCartToStorage(); // حفظ السلة عند كل تحديث
}

// حذف من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
    showNotification('تم حذف المنتج من السلة', 'success');
}

// تحديث الكمية
function updateQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;

    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }

    if (newQuantity > item.stock) {
        showNotification('الكمية المطلوبة غير متوفرة', 'warning');
        return;
    }

    item.quantity = newQuantity;
    updateCartDisplay();
}

// تعيين الكمية
function setQuantity(index, quantity) {
    const item = cart[index];
    const newQuantity = parseInt(quantity);

    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }

    if (newQuantity > item.stock) {
        showNotification('الكمية المطلوبة غير متوفرة', 'warning');
        document.querySelector(`input[onchange="setQuantity(${index}, this.value)"]`).value = item.quantity;
        return;
    }

    item.quantity = newQuantity;
    updateCartDisplay();
}

// حساب وتعيين الضريبة تلقائياً
function calculateAndSetTax() {
    if (taxSettings.tax_enabled && taxSettings.tax_rate > 0) {
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const discount = parseFloat(document.getElementById('discountInput').value) || 0;
        const taxableAmount = subtotal - discount;
        const taxAmount = (taxableAmount * taxSettings.tax_rate) / 100;

        document.getElementById('taxInput').value = taxAmount.toFixed(2);
        console.log('تم حساب الضريبة تلقائياً:', taxAmount.toFixed(2));
    }
}

// حساب المجاميع
function calculateTotals() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = parseFloat(document.getElementById('discountInput').value) || 0;

    // حساب الضريبة تلقائياً إذا كانت مفعلة
    if (taxSettings.tax_enabled && taxSettings.tax_rate > 0) {
        const taxableAmount = subtotal - discount;
        const autoTax = (taxableAmount * taxSettings.tax_rate) / 100;
        document.getElementById('taxInput').value = autoTax.toFixed(2);
    }

    const tax = parseFloat(document.getElementById('taxInput').value) || 0;
    const total = subtotal - discount + tax;

    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.ي';
    document.getElementById('totalAmount').textContent = total.toFixed(2) + ' ر.ي';

    // تحديث قسم الدفع الجزئي والمبلغ المتبقي
    togglePaymentAmountSection();
    updateRemainingAmount();
}

// تحديث واجهة الدفع حسب الطريقة المختارة
function updatePaymentInterface() {
    const customerSelect = document.getElementById('customerSelect');
    const paymentAmountSection = document.getElementById('paymentAmountSection');

    // إذا كان الدفع آجل، إخفاء قسم المبلغ المدفوع
    if (selectedPaymentMethod === 'آجل') {
        paymentAmountSection.style.display = 'none';
        updateCheckoutButtonForCredit();
    } else {
        // للطرق الأخرى، إظهار قسم المبلغ إذا كان هناك عميل محدد
        togglePaymentAmountSection();
    }
}

// تحديث زر الدفع للدفع الآجل
function updateCheckoutButtonForCredit() {
    const checkoutBtn = document.getElementById('checkoutBtn');
    const customerSelect = document.getElementById('customerSelect');

    if (selectedPaymentMethod === 'آجل' && customerSelect.value) {
        const total = getTotalAmount();
        checkoutBtn.innerHTML = `
            <i class="fas fa-calendar-alt me-2"></i>
            دفع آجل (${total.toFixed(2)} ر.ي)
        `;
        checkoutBtn.className = 'checkout-btn partial-payment';
    }
}

// إظهار/إخفاء قسم المبلغ المدفوع
function togglePaymentAmountSection() {
    const customerSelect = document.getElementById('customerSelect');
    const paymentAmountSection = document.getElementById('paymentAmountSection');
    const paidAmountInput = document.getElementById('paidAmountInput');

    console.log('تغيير العميل:', customerSelect.value);

    if (customerSelect.value && customerSelect.value !== '' && customerSelect.value !== 'عميل عادي') {
        // عميل محدد - إظهار قسم الدفع الجزئي
        console.log('عميل محدد - إظهار قسم الدفع الجزئي');
        paymentAmountSection.style.display = 'block';

        // تعيين القيمة الافتراضية للمبلغ المدفوع (المبلغ الكامل)
        const total = getTotalAmount();
        paidAmountInput.value = total.toFixed(2);
        updateRemainingAmount();
        updateCheckoutButtonText(0); // لا يوجد متبقي في البداية
    } else {
        // عميل عادي - إخفاء قسم الدفع الجزئي
        console.log('عميل عادي - إخفاء قسم الدفع الجزئي');
        paymentAmountSection.style.display = 'none';
        paidAmountInput.value = '';

        // تحديث زر الدفع للعميل العادي
        const checkoutBtn = document.getElementById('checkoutBtn');
        checkoutBtn.innerHTML = `
            <i class="fas fa-check me-2"></i>
            إتمام البيع
        `;
        checkoutBtn.className = 'checkout-btn';
    }
}

// تحديث المبلغ المتبقي
function updateRemainingAmount() {
    const paymentAmountSection = document.getElementById('paymentAmountSection');
    const customerSelect = document.getElementById('customerSelect');

    // التحقق من أن قسم الدفع مرئي وأن هناك عميل محدد
    if (paymentAmountSection.style.display === 'none' || !customerSelect.value || customerSelect.value === '') {
        return;
    }

    const total = getTotalAmount();
    const paidAmount = parseFloat(document.getElementById('paidAmountInput').value) || 0;
    const remaining = total - paidAmount;

    const remainingElement = document.getElementById('remainingAmountValue');
    const remainingContainer = document.getElementById('remainingAmount');

    if (remaining > 0) {
        remainingContainer.className = 'remaining-amount';
        remainingContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>💰 مديونية على العميل:</span>
                <span style="font-weight: bold; font-size: 16px;">${remaining.toFixed(2)} ر.ي</span>
            </div>
            <small style="opacity: 0.8;">سيتم حفظ الفاتورة كـ "دفع جزئي"</small>
        `;
    } else if (remaining < 0) {
        remainingContainer.className = 'remaining-amount positive';
        remainingContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>💵 الباقي للعميل:</span>
                <span style="font-weight: bold; font-size: 16px;">${Math.abs(remaining).toFixed(2)} ر.ي</span>
            </div>
        `;
    } else {
        remainingContainer.className = 'remaining-amount positive';
        remainingContainer.innerHTML = `
            <div style="text-align: center;">
                <span style="font-size: 18px;">✅ تم الدفع بالكامل</span>
            </div>
        `;
    }

    // تحديث نص زر الدفع
    updateCheckoutButtonText(remaining);
}

// تحديث نص زر الدفع
function updateCheckoutButtonText(remaining) {
    const checkoutBtn = document.getElementById('checkoutBtn');
    const customerSelect = document.getElementById('customerSelect');

    if (customerSelect.value && customerSelect.value !== '') {
        // عميل محدد
        if (remaining > 0) {
            checkoutBtn.innerHTML = `
                <i class="fas fa-clock me-2"></i>
                دفع جزئي (${remaining.toFixed(2)} ر.ي متبقي)
            `;
            checkoutBtn.className = 'checkout-btn partial-payment';
        } else if (remaining < 0) {
            checkoutBtn.innerHTML = `
                <i class="fas fa-money-bill-wave me-2"></i>
                إتمام الدفع (باقي ${Math.abs(remaining).toFixed(2)} ر.ي)
            `;
            checkoutBtn.className = 'checkout-btn';
        } else {
            checkoutBtn.innerHTML = `
                <i class="fas fa-check me-2"></i>
                إتمام الدفع الكامل
            `;
            checkoutBtn.className = 'checkout-btn';
        }
    } else {
        // عميل عادي
        checkoutBtn.innerHTML = `
            <i class="fas fa-check me-2"></i>
            إتمام البيع
        `;
        checkoutBtn.className = 'checkout-btn';
    }
}

// تحديث حالة الفاتورة في الواجهة
function updateInvoiceStatus(status, remainingAmount) {
    const successPaymentStatus = document.getElementById('successPaymentStatus');

    // تحويل remainingAmount إلى رقم للتأكد
    const remaining = parseFloat(remainingAmount) || 0;

    if (status === 'جزئي' && remaining > 0) {
        // عرض حالة الدفع الجزئي
        successPaymentStatus.innerHTML = `
            <div class="alert alert-warning mb-3">
                <i class="fas fa-clock me-2"></i>
                <strong>دفع جزئي</strong>
                <br>
                <small>المبلغ المتبقي: <strong>${remaining.toFixed(2)} ر.ي</strong></small>
                <br>
                <small class="text-muted">يمكن إكمال الدفع لاحقاً من صفحة المديونيات</small>
            </div>
        `;
    } else {
        // عرض حالة الدفع الكامل
        successPaymentStatus.innerHTML = `
            <div class="alert alert-success mb-3">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم الدفع بالكامل</strong>
                <br>
                <small class="text-muted">الفاتورة مكتملة</small>
            </div>
        `;
    }
}

// الحصول على المبلغ الإجمالي
function getTotalAmount() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = parseFloat(document.getElementById('discountInput').value) || 0;
    const tax = parseFloat(document.getElementById('taxInput').value) || 0;
    return subtotal - discount + tax;
}

// معالجة الدفع
async function processCheckout() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'warning');
        return;
    }

    const checkoutBtn = document.getElementById('checkoutBtn');
    checkoutBtn.disabled = true;
    checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';

    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = parseFloat(document.getElementById('discountInput').value) || 0;
    const tax = parseFloat(document.getElementById('taxInput').value) || 0;

    // حساب المبلغ المدفوع والمتبقي
    const total = subtotal - discount + tax;
    const customerId = document.getElementById('customerSelect').value || null;

    // تحديد المبلغ المدفوع بناءً على طريقة الدفع والعميل
    let paidAmount;
    if (selectedPaymentMethod === 'آجل') {
        // الدفع الآجل = لا يدفع شيء
        paidAmount = 0;
    } else if (customerId && customerId !== '') {
        // عميل محدد - استخدام المبلغ المدخل أو الإجمالي كافتراضي
        const inputAmount = parseFloat(document.getElementById('paidAmountInput').value);
        paidAmount = isNaN(inputAmount) ? total : inputAmount;
    } else {
        // عميل عادي - دفع كامل
        paidAmount = total;
    }

    const remainingAmount = total - paidAmount;

    console.log('تفاصيل الدفع:', {
        total: total,
        customerId: customerId,
        paymentMethod: selectedPaymentMethod,
        inputAmount: document.getElementById('paidAmountInput').value,
        paidAmount: paidAmount,
        remainingAmount: remainingAmount
    });

    const saleData = {
        customer_id: customerId,
        items: cart.map(item => ({
            product_id: item.product_id,
            quantity: item.quantity,
            price: item.price
        })),
        payment_method: selectedPaymentMethod,
        discount_amount: discount,
        tax_amount: tax,
        paid_amount: paidAmount,
        remaining_amount: remainingAmount,
        notes: ''
    };

    try {
        const response = await fetch('/pos/create-sale', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(saleData)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('HTTP Error:', response.status, errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const result = await response.json();

        if (result.success) {
            currentSaleId = result.sale_id;
            document.getElementById('successInvoiceNumber').textContent = result.invoice_number;

            // حفظ معلومات الفاتورة المكتملة مع جميع البيانات
            const completedSale = {
                sale_id: result.sale_id,
                invoice_number: result.invoice_number,
                cart: [...cart],
                customer_id: document.getElementById('customerSelect').value,
                payment_method: selectedPaymentMethod,
                discount: document.getElementById('discountInput').value,
                tax: document.getElementById('taxInput').value,
                paid_amount: document.getElementById('paidAmountInput').value,
                remaining_amount: result.remaining_amount || 0,
                status: result.status || 'مكتملة',
                timestamp: Date.now()
            };
            localStorage.setItem('completed_sale', JSON.stringify(completedSale));

            // تحديث حالة الفاتورة في الواجهة
            updateInvoiceStatus(result.status, result.remaining_amount);

            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();

            // إشعار مخصص حسب حالة الدفع
            const remaining = parseFloat(result.remaining_amount) || 0;
            if (result.status === 'جزئي' && remaining > 0) {
                showNotification(`تم إتمام الدفع الجزئي - متبقي: ${remaining.toFixed(2)} ر.ي`, 'warning');
            } else {
                showNotification('تم إتمام البيع بنجاح', 'success');
            }

            // تهيئة الحقول بعد إتمام الدفع
            setTimeout(() => {
                resetFieldsAfterSale();
            }, 2000); // تأخير قصير للسماح بالطباعة
        } else {
            console.error('Sale Error:', result);
            showNotification(result.message || 'خطأ في إتمام البيع', 'error');
        }
    } catch (error) {
        console.error('خطأ في معالجة البيع:', error);
        showNotification('خطأ في معالجة البيع', 'error');
    } finally {
        checkoutBtn.disabled = false;
        checkoutBtn.innerHTML = '<i class="fas fa-check me-2"></i>إتمام البيع';
    }
}

// الاستمرار في نفس الفاتورة (إغلاق النافذة فقط)
function continueCurrentSale() {
    const successModal = bootstrap.Modal.getInstance(document.getElementById('successModal'));
    if (successModal) {
        successModal.hide();
    }

    // السلة والقيم تبقى كما هي
    showNotification('يمكنك الآن تعديل الفاتورة أو طباعتها مرة أخرى', 'info');
}

// بيع جديد
function newSale() {
    // تأكيد من المستخدم
    if (cart.length > 0) {
        if (!confirm('هل أنت متأكد من بدء بيع جديد؟ سيتم مسح السلة الحالية.')) {
            return;
        }
    }

    cart = [];
    currentSaleId = null;
    document.getElementById('customerSelect').value = '';
    document.getElementById('discountInput').value = 0;
    document.getElementById('taxInput').value = 0;
    document.getElementById('productSearch').value = '';
    document.getElementById('barcodeInput').value = '';
    document.getElementById('paidAmountInput').value = '';

    // تصفير المجاميع
    resetTotals();

    // إعادة تعيين طريقة الدفع
    document.querySelectorAll('.payment-method').forEach(btn => btn.classList.remove('active'));
    document.querySelector('.payment-method[data-method="نقدي"]').classList.add('active');
    selectedPaymentMethod = 'نقدي';

    // إخفاء قسم الدفع الجزئي
    document.getElementById('paymentAmountSection').style.display = 'none';

    // مسح السلة والفاتورة المكتملة من localStorage
    clearCartFromStorage();
    localStorage.removeItem('completed_sale');

    updateCartDisplay();
    updateInvoiceNumber();
    loadProducts();

    // تم إزالة Bootstrap Select - نستخدم select عادي الآن

    const successModal = bootstrap.Modal.getInstance(document.getElementById('successModal'));
    if (successModal) {
        successModal.hide();
    }

    showNotification('تم بدء بيع جديد', 'success');
}

// تهيئة الحقول بعد إتمام الدفع
function resetFieldsAfterSale() {
    // مسح السلة والحقول
    cart = [];

    // إعادة تعيين الحقول
    document.getElementById('customerSelect').value = '';
    document.getElementById('discountInput').value = '0';
    document.getElementById('taxInput').value = '0';
    document.getElementById('paidAmountInput').value = '';
    document.getElementById('productSearch').value = '';

    // تصفير المجاميع
    resetTotals();

    // إعادة تعيين طريقة الدفع
    document.querySelectorAll('.payment-method').forEach(btn => btn.classList.remove('active'));
    document.querySelector('.payment-method[data-method="نقدي"]').classList.add('active');
    selectedPaymentMethod = 'نقدي';

    // مسح localStorage
    clearCartFromStorage();

    // تحديث العرض
    updateCartDisplay();
    updateInvoiceNumber();

    console.log('تم تهيئة الحقول بعد إتمام الدفع');
}

// طباعة الفاتورة
function printInvoice() {
    if (currentSaleId) {
        window.open(`/sales/${currentSaleId}/print`, '_blank');
    }
}

// تحديث رقم الفاتورة
function updateInvoiceNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const nextNumber = Math.floor(Math.random() * 1000) + 1; // رقم عشوائي للعرض
    const invoiceNumber = `INV-${year}-${String(nextNumber).padStart(6, '0')}`;
    document.getElementById('invoiceNumber').textContent = `فاتورة: ${invoiceNumber}`;
}

// إظهار/إخفاء التحميل
function showLoading(show) {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = show ? 'block' : 'none';
    }
}

// إظهار الإشعارات
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'exclamation'} me-2"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    setTimeout(() => notification.classList.add('show'), 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// تأخير التنفيذ (للبحث)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // F1 - تركيز على البحث
    if (e.key === 'F1') {
        e.preventDefault();
        document.getElementById('productSearch').focus();
    }

    // F2 - تركيز على الباركود
    if (e.key === 'F2') {
        e.preventDefault();
        document.getElementById('barcodeInput').focus();
    }

    // F9 - إتمام البيع
    if (e.key === 'F9') {
        e.preventDefault();
        if (cart.length > 0) {
            processCheckout();
        }
    }

    // F12 - بيع جديد
    if (e.key === 'F12') {
        e.preventDefault();
        newSale();
    }
});
</script>
@endpush
