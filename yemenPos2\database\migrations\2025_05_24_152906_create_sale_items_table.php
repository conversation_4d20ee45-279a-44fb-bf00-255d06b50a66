<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sale_id')->constrained()->onDelete('cascade'); // الفاتورة
            $table->foreignId('product_id')->constrained()->onDelete('cascade'); // المنتج
            $table->string('product_name'); // اسم المنتج (نسخة للحفظ)
            $table->string('product_sku'); // رمز المنتج (نسخة للحفظ)
            $table->integer('quantity'); // الكمية
            $table->decimal('unit_price', 10, 2); // سعر الوحدة
            $table->decimal('discount_amount', 10, 2)->default(0); // خصم على الصنف
            $table->decimal('total_price', 10, 2); // السعر الإجمالي
            $table->string('unit')->default('قطعة'); // الوحدة
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_items');
    }
};
