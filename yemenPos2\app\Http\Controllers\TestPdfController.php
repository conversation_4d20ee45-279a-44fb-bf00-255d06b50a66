<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\PdfService;

class TestPdfController extends Controller
{
    public function test()
    {
        try {
            $html = '<h1>Test PDF</h1><p>This is a test PDF file.</p>';
            $pdfService = new PdfService();
            $pdf = $pdfService->loadHTML($html);
            return $pdf->download('test.pdf');
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
