# دليل نظام المشتريات والدفعات الجديد

## 🎯 **التصميم الصحيح المطبق:**

### **📦 1. المنتجات (معلومات أساسية فقط):**

#### **✅ ما يحتويه المنتج:**
```
📋 المعلومات الأساسية:
✅ اسم المنتج (عربي/إنجليزي)
✅ الكود/الباركود
✅ التصنيف والوحدة
✅ نسبة الربح (لحساب سعر البيع تلقائ<|im_start|>)
✅ الحد الأدنى للمخزون
✅ أيام التنبيه لانتهاء الصلاحية
✅ الوصف والصورة

❌ ما لا يحتويه:
❌ مورد افتراضي (يُحدد في كل مشترى)
❌ سعر شراء ثابت (يختلف حسب الدفعة)
❌ سعر بيع ثابت (يُحسب من آخر دفعة + نسبة الربح)
❌ كمية مخزونة (تُحسب من مجموع الدفعات)
```

### **🛒 2. المشتريات (إنشاء الدفعات):**

#### **✅ معلومات المشترى:**
```
📊 بيانات المشترى:
✅ رقم المشترى (تلقائي)
✅ المورد
✅ تاريخ المشترى
✅ المستخدم المسؤول
✅ حالة المشترى (معلق، مستلم، جزئي، ملغي)
✅ معلومات الدفع
✅ ملاحظات
```

#### **✅ عناصر المشترى (الدفعات):**
```
📦 لكل منتج في المشترى:
✅ المنتج المختار
✅ الكمية المشتراة
✅ سعر الشراء لهذه الدفعة
✅ تاريخ انتهاء الصلاحية (اختياري)
✅ تاريخ الإنتاج (اختياري)
✅ رقم الدفعة (تلقائي)
✅ الكمية المتبقية (تبدأ = الكمية المشتراة)
✅ حالة الدفعة (نشطة، منتهية، نفدت، مسحوبة)
```

### **📊 3. عرض المنتجات (البيانات المحسوبة):**

#### **✅ ما يُعرض للمستخدم:**
```
📋 في قائمة المنتجات:
✅ المعلومات الأساسية
✅ الكمية الإجمالية (من مجموع الدفعات النشطة)
✅ عدد الدفعات المتاحة
✅ سعر البيع المحسوب (من آخر دفعة + نسبة الربح)
✅ حالة انتهاء الصلاحية (من أقرب دفعة للانتهاء)
✅ تنبيهات المخزون المنخفض
```

## 🔄 **آلية العمل:**

### **1. إضافة منتج جديد:**
```
👤 المستخدم → صفحة المنتجات → إنشاء منتج
📝 يدخل: الاسم، التصنيف، الوحدة، نسبة الربح
⚙️ يدخل: الحد الأدنى، أيام التنبيه
✅ النظام: ينشئ المنتج بدون كمية أو أسعار
📊 النتيجة: منتج جاهز لاستقبال الدفعات
```

### **2. شراء دفعة جديدة:**
```
👤 المستخدم → صفحة المشتريات → إنشاء مشترى
🏪 يختار: المورد وتاريخ المشترى
📦 لكل منتج يدخل:
   - المنتج من القائمة
   - الكمية المشتراة
   - سعر الشراء لهذه الدفعة
   - تاريخ انتهاء الصلاحية (اختياري)
   - تاريخ الإنتاج (اختياري)

✅ النظام تلقائ<|im_start|>:
   - ينشئ رقم مشترى فريد
   - ينشئ دفعة منفصلة لكل منتج
   - ينشئ رقم دفعة فريد لكل منتج
   - يحدث الكمية الإجمالية للمنتج
   - يحسب سعر البيع الجديد
```

### **3. عرض المنتجات:**
```
👤 المستخدم → صفحة المنتجات → قائمة محدثة
📊 النظام يعرض:
   - الكمية الإجمالية = مجموع الدفعات النشطة
   - عدد الدفعات = عدد الدفعات المتاحة
   - سعر البيع = آخر سعر شراء + نسبة الربح
   - حالة الصلاحية = من أقرب دفعة للانتهاء
```

### **4. البيع (نظام FIFO):**
```
👤 المستخدم → نقطة البيع → بيع منتج
🤖 النظام تلقائ<|im_start|>:
   - يبحث عن الدفعات النشطة للمنتج
   - يرتبها حسب تاريخ انتهاء الصلاحية (الأقرب أولاً)
   - يبيع من الدفعة الأقرب للانتهاء
   - يحدث الكمية المتبقية في الدفعة
   - ينتقل للدفعة التالية إذا نفدت الأولى
   - يحدث حالة الدفعة (نفدت/منتهية/نشطة)
```

## 📊 **مثال عملي شامل:**

### **منتج: حليب نيدو**

#### **1. إنشاء المنتج:**
```
📦 في صفحة المنتجات:
- الاسم: حليب نيدو
- الكود: NIDO001
- التصنيف: منتجات الألبان
- الوحدة: علبة
- نسبة الربح: 25%
- الحد الأدنى: 10 علب
- أيام التنبيه: 30 يوم

✅ النتيجة: منتج بدون كمية أو أسعار
```

#### **2. المشترى الأول:**
```
🛒 في صفحة المشتريات:
- المورد: شركة نستله
- التاريخ: 2025-05-29
- المنتج: حليب نيدو
- الكمية: 100 علبة
- سعر الشراء: 18 ر.ي
- تاريخ الانتهاء: 2025-11-01

✅ النظام ينشئ:
- رقم مشترى: PUR-20250529-0001
- رقم دفعة: NID-20250529-001
- الكمية المتبقية: 100 علبة
- سعر البيع المحسوب: 18 × 1.25 = 22.5 ر.ي
```

#### **3. المشترى الثاني:**
```
🛒 مشترى جديد:
- المورد: مورد آخر
- التاريخ: 2025-06-15
- المنتج: حليب نيدو
- الكمية: 50 علبة
- سعر الشراء: 17 ر.ي
- تاريخ الانتهاء: 2025-12-15

✅ النظام ينشئ:
- رقم مشترى: PUR-20250615-0002
- رقم دفعة: NID-20250615-001
- الكمية المتبقية: 50 علبة
- سعر البيع الجديد: 17 × 1.25 = 21.25 ر.ي (محدث)
```

#### **4. عرض المنتج:**
```
📊 في قائمة المنتجات:
- الاسم: حليب نيدو
- الكمية الإجمالية: 150 علبة (100 + 50)
- عدد الدفعات: 2 دفعة
- سعر البيع: 21.25 ر.ي (من آخر دفعة)
- حالة الصلاحية: صالح (أقرب انتهاء: 2025-11-01)
```

#### **5. البيع:**
```
💰 عند بيع 120 علبة:
🤖 النظام تلقائ<|im_start|>:
- يبيع 100 علبة من الدفعة الأولى (تنتهي 2025-11-01)
- يبيع 20 علبة من الدفعة الثانية (تنتهي 2025-12-15)
- الدفعة الأولى: نفدت (0 علبة متبقية)
- الدفعة الثانية: 30 علبة متبقية
- الكمية الإجمالية: 30 علبة
```

## 🎯 **الفوائد المحققة:**

### **✅ للمخزون:**
- **تتبع دقيق** لكل دفعة منفصلة
- **أسعار مختلفة** حسب المورد والوقت
- **تواريخ انتهاء مختلفة** لنفس المنتج
- **نظام FIFO** تلقائي للبيع
- **تقليل الفاقد** من انتهاء الصلاحية

### **✅ للمحاسبة:**
- **تتبع التكلفة** لكل دفعة
- **حساب الربحية** الدقيق
- **تقارير مفصلة** عن الموردين
- **تحليل الأداء** حسب الدفعة

### **✅ للإدارة:**
- **قرارات مدروسة** للمشتريات
- **تحكم أفضل** في المخزون
- **تنبيهات مبكرة** لانتهاء الصلاحية
- **تقارير شاملة** ودقيقة

## 🚀 **الخطوات التالية:**

### **📋 ما تم إنجازه:**
```
✅ تصحيح فهم النظام
✅ إنشاء جداول المشتريات والدفعات
✅ تطوير النماذج والعلاقات
✅ إنشاء كنترولر المشتريات
✅ بداية تطوير واجهة المشتريات
```

### **🔄 ما يحتاج إكمال:**
```
🔲 إكمال واجهة إنشاء المشتريات
🔲 تطوير دالة حفظ المشترى وإنشاء الدفعات
🔲 تحديث نموذج Product لحساب البيانات من الدفعات
🔲 تحديث واجهات المنتجات لعرض البيانات المحسوبة
🔲 تطوير نقطة البيع لتطبيق نظام FIFO
🔲 إنشاء تقارير الدفعات وانتهاء الصلاحية
```

---

**🎉 الآن النظام مصمم بالطريقة الصحيحة والمهنية!**

**المنتجات = معلومات أساسية | المشتريات = إنشاء دفعات | العرض = بيانات محسوبة** 📦⚡✨
