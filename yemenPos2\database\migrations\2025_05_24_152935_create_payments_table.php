<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_number')->unique(); // رقم الدفعة
            $table->morphs('payable'); // يمكن أن يكون للمبيعات أو المشتريات
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null'); // العميل (للمبيعات)
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null'); // المورد (للمشتريات)
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // المستخدم
            $table->decimal('amount', 10, 2); // مبلغ الدفعة
            $table->enum('payment_method', ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة ائتمان']); // طريقة الدفع
            $table->enum('payment_type', ['دفعة', 'استرداد'])->default('دفعة'); // نوع الدفعة
            $table->date('payment_date'); // تاريخ الدفعة
            $table->string('reference_number')->nullable(); // رقم المرجع (للتحويلات البنكية)
            $table->string('bank_name')->nullable(); // اسم البنك
            $table->string('check_number')->nullable(); // رقم الشيك
            $table->date('check_date')->nullable(); // تاريخ الشيك
            $table->string('currency', 3)->default('YER'); // العملة
            $table->decimal('exchange_rate', 8, 4)->default(1); // سعر الصرف
            $table->text('notes')->nullable(); // ملاحظات
            $table->enum('status', ['مؤكد', 'معلق', 'ملغي'])->default('مؤكد'); // حالة الدفعة
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
