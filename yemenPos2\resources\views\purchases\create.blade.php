@extends('layouts.app')

@section('title', 'مشترى جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-plus me-2"></i>
                        مشترى جديد
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('purchases.index') }}">المشتريات</a></li>
                            <li class="breadcrumb-item active">مشترى جديد</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('purchases.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <form action="{{ route('purchases.store') }}" method="POST" id="purchaseForm">
                @csrf

                <div class="row">
                    <!-- Purchase Info -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات المشترى
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">المورد <span class="text-danger">*</span></label>
                                    <select name="supplier_id" class="form-select" required>
                                        <option value="">اختر المورد</option>
                                        @foreach($suppliers as $supplier)
                                            <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ المشترى <span class="text-danger">*</span></label>
                                    <input type="date" name="purchase_date" class="form-control"
                                           value="{{ date('Y-m-d') }}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3"
                                              placeholder="ملاحظات إضافية..."></textarea>
                                </div>

                                <!-- Totals -->
                                <div class="border-top pt-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المجموع الفرعي:</span>
                                        <span id="subtotal">0.00 ر.ي</span>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الخصم</label>
                                        <input type="number" name="discount_amount" class="form-control"
                                               value="0" min="0" step="0.01" onchange="calculateTotals()">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الضريبة</label>
                                        <input type="number" name="tax_amount" class="form-control"
                                               value="0" min="0" step="0.01" onchange="calculateTotals()">
                                    </div>

                                    <div class="d-flex justify-content-between border-top pt-2">
                                        <strong>الإجمالي:</strong>
                                        <strong id="total">0.00 ر.ي</strong>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>حفظ المشترى
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="addProduct()">
                                        <i class="fas fa-plus me-2"></i>إضافة منتج
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>
                                    المنتجات
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Add Product Section -->
                                <div class="row mb-4 p-3 bg-light rounded">
                                    <div class="col-md-3">
                                        <label class="form-label">المنتج <span class="text-danger">*</span></label>
                                        <select id="productSelect" class="form-select">
                                            <option value="">اختر المنتج</option>
                                            @foreach($products as $product)
                                                <option value="{{ $product->id }}"
                                                        data-name="{{ $product->name_ar }}"
                                                        data-sku="{{ $product->sku }}"
                                                        data-unit="{{ $product->unit }}"
                                                        data-category="{{ $product->category->name_ar ?? '' }}"
                                                        data-profit-margin="{{ $product->profit_margin ?? 20 }}">
                                                    {{ $product->name_ar }}
                                                    @if($product->category)
                                                        ({{ $product->category->name_ar }})
                                                    @endif
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">الكمية <span class="text-danger">*</span></label>
                                        <input type="number" id="quantityInput" class="form-control"
                                               value="1" min="0.01" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">سعر الشراء <span class="text-danger">*</span></label>
                                        <input type="number" id="costInput" class="form-control"
                                               value="" min="0" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">تاريخ الانتهاء</label>
                                        <input type="date" id="expiryInput" class="form-control">
                                        <small class="text-muted">اختياري</small>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">تاريخ الإنتاج</label>
                                        <input type="date" id="productionInput" class="form-control">
                                        <small class="text-muted">اختياري</small>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-primary d-block w-100" onclick="addProduct()">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Products Table -->
                                <div class="table-responsive">
                                    <table class="table table-striped" id="productsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th width="5%">#</th>
                                                <th width="20%">المنتج</th>
                                                <th width="10%">الكود</th>
                                                <th width="10%">الكمية</th>
                                                <th width="10%">سعر الشراء</th>
                                                <th width="12%">تاريخ الانتهاء</th>
                                                <th width="12%">تاريخ الإنتاج</th>
                                                <th width="10%">الإجمالي</th>
                                                <th width="8%">سعر البيع المقترح</th>
                                                <th width="3%">حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productsTableBody">
                                            <tr id="emptyRow">
                                                <td colspan="10" class="text-center text-muted py-4">
                                                    <i class="fas fa-shopping-cart fa-3x mb-3 text-secondary"></i>
                                                    <br><strong>لم يتم إضافة منتجات بعد</strong>
                                                    <br><small>استخدم النموذج أعلاه لإضافة المنتجات</small>
                                                </td>
                                            </tr>
                                        </tbody>
                                        <tfoot class="table-light">
                                            <tr>
                                                <td colspan="7" class="text-end"><strong>الإجمالي:</strong></td>
                                                <td><strong id="tableTotal">0.00 ر.ي</strong></td>
                                                <td colspan="2"></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                <!-- معلومات إضافية -->
                                <div class="alert alert-info mt-3">
                                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                                    <ul class="mb-0">
                                        <li><strong>الدفعات:</strong> سيتم إنشاء دفعة منفصلة لكل منتج مع رقم دفعة فريد</li>
                                        <li><strong>سعر البيع:</strong> سيتم حساب سعر البيع تلقائ<|im_start|> بناءً على سعر الشراء + نسبة الربح</li>
                                        <li><strong>انتهاء الصلاحية:</strong> تاريخ انتهاء الصلاحية اختياري ويمكن تركه فارغ<|im_start|></li>
                                        <li><strong>المخزون:</strong> سيتم تحديث مخزون المنتج تلقائ<|im_start|> بعد حفظ المشترى</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let products = [];
let productIndex = 0;

// Add product to table
function addProduct() {
    const productSelect = document.getElementById('productSelect');
    const quantityInput = document.getElementById('quantityInput');
    const costInput = document.getElementById('costInput');
    const expiryInput = document.getElementById('expiryInput');
    const productionInput = document.getElementById('productionInput');

    // التحقق من الحقول المطلوبة
    if (!productSelect.value) {
        alert('يرجى اختيار المنتج');
        productSelect.focus();
        return;
    }

    if (!quantityInput.value || parseFloat(quantityInput.value) <= 0) {
        alert('يرجى إدخال كمية صحيحة');
        quantityInput.focus();
        return;
    }

    if (!costInput.value || parseFloat(costInput.value) <= 0) {
        alert('يرجى إدخال سعر شراء صحيح');
        costInput.focus();
        return;
    }

    // التحقق من عدم تكرار المنتج
    const existingProduct = products.find(p => p.id === productSelect.value);
    if (existingProduct) {
        alert('هذا المنتج موجود مسبق<|im_start|>. يرجى حذفه أولاً إذا كنت تريد تعديله.');
        return;
    }

    const selectedOption = productSelect.options[productSelect.selectedIndex];
    const quantity = parseFloat(quantityInput.value);
    const cost = parseFloat(costInput.value);
    const profitMargin = parseFloat(selectedOption.dataset.profitMargin) || 20;
    const suggestedPrice = cost * (1 + (profitMargin / 100));

    const product = {
        id: productSelect.value,
        name: selectedOption.dataset.name,
        sku: selectedOption.dataset.sku,
        unit: selectedOption.dataset.unit,
        category: selectedOption.dataset.category,
        quantity: quantity,
        cost: cost,
        total: quantity * cost,
        expiry_date: expiryInput.value || null,
        production_date: productionInput.value || null,
        profit_margin: profitMargin,
        suggested_price: suggestedPrice
    };

    products.push(product);
    updateProductsTable();
    calculateTotals();

    // Reset form
    productSelect.value = '';
    quantityInput.value = '1';
    costInput.value = '';
    expiryInput.value = '';
    productionInput.value = '';

    // Focus on product select for next item
    productSelect.focus();
}

// Update products table
function updateProductsTable() {
    const tbody = document.getElementById('productsTableBody');
    const emptyRow = document.getElementById('emptyRow');

    if (products.length === 0) {
        emptyRow.style.display = '';
        document.getElementById('tableTotal').textContent = '0.00 ر.ي';
        return;
    }

    emptyRow.style.display = 'none';

    let html = '';
    products.forEach((product, index) => {
        const expiryDisplay = product.expiry_date ?
            new Date(product.expiry_date).toLocaleDateString('ar-SA') :
            '<span class="text-muted">غير محدد</span>';

        const productionDisplay = product.production_date ?
            new Date(product.production_date).toLocaleDateString('ar-SA') :
            '<span class="text-muted">غير محدد</span>';

        html += `
            <tr>
                <td>${index + 1}</td>
                <td>
                    <strong>${product.name}</strong>
                    ${product.category ? `<br><small class="text-muted">${product.category}</small>` : ''}
                </td>
                <td><code>${product.sku}</code></td>
                <td>${product.quantity} ${product.unit}</td>
                <td>${product.cost.toFixed(2)} ر.ي</td>
                <td>${expiryDisplay}</td>
                <td>${productionDisplay}</td>
                <td><strong>${product.total.toFixed(2)} ر.ي</strong></td>
                <td>
                    <span class="badge bg-success">${product.suggested_price.toFixed(2)} ر.ي</span>
                    <br><small class="text-muted">${product.profit_margin}% ربح</small>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeProduct(${index})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        // إضافة الحقول المخفية
        html += `
            <input type="hidden" name="items[${index}][product_id]" value="${product.id}">
            <input type="hidden" name="items[${index}][quantity]" value="${product.quantity}">
            <input type="hidden" name="items[${index}][purchase_price]" value="${product.cost}">
            <input type="hidden" name="items[${index}][expiry_date]" value="${product.expiry_date || ''}">
            <input type="hidden" name="items[${index}][production_date]" value="${product.production_date || ''}">
        `;
    });

    tbody.innerHTML = html;

    // تحديث الإجمالي في الجدول
    const total = products.reduce((sum, product) => sum + product.total, 0);
    document.getElementById('tableTotal').textContent = total.toFixed(2) + ' ر.ي';
}

// Remove product
function removeProduct(index) {
    products.splice(index, 1);
    updateProductsTable();
    calculateTotals();
}

// Calculate totals
function calculateTotals() {
    const subtotal = products.reduce((sum, product) => sum + product.total, 0);
    const discount = parseFloat(document.querySelector('input[name="discount_amount"]').value) || 0;
    const tax = parseFloat(document.querySelector('input[name="tax_amount"]').value) || 0;
    const total = subtotal - discount + tax;

    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.ي';
    document.getElementById('total').textContent = total.toFixed(2) + ' ر.ي';
}

// إضافة منتج بالضغط على Enter
document.getElementById('costInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        addProduct();
    }
});

// إضافة منتج بالضغط على Enter في حقل الكمية
document.getElementById('quantityInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('costInput').focus();
    }
});

// التركيز على حقل الكمية عند اختيار المنتج
document.getElementById('productSelect').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('quantityInput').focus();
        document.getElementById('quantityInput').select();
    }
});

// Form validation
document.getElementById('purchaseForm').addEventListener('submit', function(e) {
    if (products.length === 0) {
        e.preventDefault();
        alert('يرجى إضافة منتج واحد على الأقل');
        return false;
    }

    // التحقق من صحة البيانات
    let hasError = false;
    products.forEach((product, index) => {
        if (!product.id || !product.quantity || !product.cost) {
            hasError = true;
        }
    });

    if (hasError) {
        e.preventDefault();
        alert('يرجى التأكد من صحة بيانات جميع المنتجات');
        return false;
    }

    // تأكيد الحفظ
    const total = products.reduce((sum, product) => sum + product.total, 0);
    const confirmMessage = `هل أنت متأكد من حفظ المشترى؟\n\nعدد المنتجات: ${products.length}\nالإجمالي: ${total.toFixed(2)} ر.ي`;

    if (!confirm(confirmMessage)) {
        e.preventDefault();
        return false;
    }
});
</script>
@endsection
