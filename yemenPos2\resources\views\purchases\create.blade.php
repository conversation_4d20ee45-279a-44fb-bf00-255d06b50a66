@extends('layouts.app')

@section('title', 'مشترى جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-plus me-2"></i>
                        مشترى جديد
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('purchases.index') }}">المشتريات</a></li>
                            <li class="breadcrumb-item active">مشترى جديد</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('purchases.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <form action="{{ route('purchases.store') }}" method="POST" id="purchaseForm">
                @csrf
                
                <div class="row">
                    <!-- Purchase Info -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات المشترى
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">المورد <span class="text-danger">*</span></label>
                                    <select name="supplier_id" class="form-select" required>
                                        <option value="">اختر المورد</option>
                                        @foreach($suppliers as $supplier)
                                            <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ المشترى <span class="text-danger">*</span></label>
                                    <input type="date" name="purchase_date" class="form-control" 
                                           value="{{ date('Y-m-d') }}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="ملاحظات إضافية..."></textarea>
                                </div>

                                <!-- Totals -->
                                <div class="border-top pt-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المجموع الفرعي:</span>
                                        <span id="subtotal">0.00 ر.ي</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">الخصم</label>
                                        <input type="number" name="discount_amount" class="form-control" 
                                               value="0" min="0" step="0.01" onchange="calculateTotals()">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">الضريبة</label>
                                        <input type="number" name="tax_amount" class="form-control" 
                                               value="0" min="0" step="0.01" onchange="calculateTotals()">
                                    </div>
                                    
                                    <div class="d-flex justify-content-between border-top pt-2">
                                        <strong>الإجمالي:</strong>
                                        <strong id="total">0.00 ر.ي</strong>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>حفظ المشترى
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="addProduct()">
                                        <i class="fas fa-plus me-2"></i>إضافة منتج
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>
                                    المنتجات
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Add Product Section -->
                                <div class="row mb-4 p-3 bg-light rounded">
                                    <div class="col-md-4">
                                        <label class="form-label">المنتج</label>
                                        <select id="productSelect" class="form-select">
                                            <option value="">اختر المنتج</option>
                                            @foreach($products as $product)
                                                <option value="{{ $product->id }}" 
                                                        data-name="{{ $product->name }}"
                                                        data-sku="{{ $product->sku }}"
                                                        data-unit="{{ $product->unit->name ?? 'قطعة' }}"
                                                        data-cost="{{ $product->cost_price ?? 0 }}">
                                                    {{ $product->name }} ({{ $product->sku }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">الكمية</label>
                                        <input type="number" id="quantityInput" class="form-control" 
                                               value="1" min="1" step="1">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">سعر الشراء</label>
                                        <input type="number" id="costInput" class="form-control" 
                                               value="0" min="0" step="0.01">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-primary d-block w-100" onclick="addProduct()">
                                            <i class="fas fa-plus me-2"></i>إضافة
                                        </button>
                                    </div>
                                </div>

                                <!-- Products Table -->
                                <div class="table-responsive">
                                    <table class="table table-striped" id="productsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>#</th>
                                                <th>المنتج</th>
                                                <th>الكود</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>سعر الشراء</th>
                                                <th>الإجمالي</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productsTableBody">
                                            <tr id="emptyRow">
                                                <td colspan="8" class="text-center text-muted">
                                                    <i class="fas fa-boxes fa-2x mb-2"></i>
                                                    <br>لم يتم إضافة منتجات بعد
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let products = [];
let productIndex = 0;

// Add product to table
function addProduct() {
    const productSelect = document.getElementById('productSelect');
    const quantityInput = document.getElementById('quantityInput');
    const costInput = document.getElementById('costInput');
    
    if (!productSelect.value || !quantityInput.value || !costInput.value) {
        alert('يرجى ملء جميع الحقول');
        return;
    }
    
    const selectedOption = productSelect.options[productSelect.selectedIndex];
    const product = {
        id: productSelect.value,
        name: selectedOption.dataset.name,
        sku: selectedOption.dataset.sku,
        unit: selectedOption.dataset.unit,
        quantity: parseFloat(quantityInput.value),
        cost: parseFloat(costInput.value),
        total: parseFloat(quantityInput.value) * parseFloat(costInput.value)
    };
    
    products.push(product);
    updateProductsTable();
    calculateTotals();
    
    // Reset form
    productSelect.value = '';
    quantityInput.value = '1';
    costInput.value = '0';
}

// Update products table
function updateProductsTable() {
    const tbody = document.getElementById('productsTableBody');
    const emptyRow = document.getElementById('emptyRow');
    
    if (products.length === 0) {
        emptyRow.style.display = '';
        return;
    }
    
    emptyRow.style.display = 'none';
    
    let html = '';
    products.forEach((product, index) => {
        html += `
            <tr>
                <td>${index + 1}</td>
                <td>${product.name}</td>
                <td>${product.sku}</td>
                <td>${product.quantity}</td>
                <td>${product.unit}</td>
                <td>${product.cost.toFixed(2)} ر.ي</td>
                <td>${product.total.toFixed(2)} ر.ي</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeProduct(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            <input type="hidden" name="items[${index}][product_id]" value="${product.id}">
            <input type="hidden" name="items[${index}][quantity]" value="${product.quantity}">
            <input type="hidden" name="items[${index}][unit_cost]" value="${product.cost}">
        `;
    });
    
    tbody.innerHTML = html;
}

// Remove product
function removeProduct(index) {
    products.splice(index, 1);
    updateProductsTable();
    calculateTotals();
}

// Calculate totals
function calculateTotals() {
    const subtotal = products.reduce((sum, product) => sum + product.total, 0);
    const discount = parseFloat(document.querySelector('input[name="discount_amount"]').value) || 0;
    const tax = parseFloat(document.querySelector('input[name="tax_amount"]').value) || 0;
    const total = subtotal - discount + tax;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.ي';
    document.getElementById('total').textContent = total.toFixed(2) + ' ر.ي';
}

// Auto-fill cost when product is selected
document.getElementById('productSelect').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        document.getElementById('costInput').value = selectedOption.dataset.cost || 0;
    }
});

// Form validation
document.getElementById('purchaseForm').addEventListener('submit', function(e) {
    if (products.length === 0) {
        e.preventDefault();
        alert('يرجى إضافة منتج واحد على الأقل');
        return false;
    }
});
</script>
@endsection
