<?php $__env->startSection('title', 'إدارة المشتريات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-shopping-bag me-2"></i>
                        إدارة المشتريات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المشتريات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?php echo e(route('purchases.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>مشترى جديد
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('purchases.index')); ?>">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search"
                                       value="<?php echo e(request('search')); ?>" placeholder="رقم المشترى أو المورد...">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from"
                                       value="<?php echo e(request('date_from')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to"
                                       value="<?php echo e(request('date_to')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                    <a href="<?php echo e(route('purchases.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المشتريات</h6>
                                    <h4><?php echo e($purchases->total()); ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shopping-bag fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">مشتريات اليوم</h6>
                                    <h4><?php echo e($purchases->where('purchase_date', today())->count()); ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">قيمة المشتريات</h6>
                                    <h4><?php echo e(number_format($purchases->sum('total_amount'), 2)); ?> ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">متوسط المشترى</h6>
                                    <h4><?php echo e(number_format($purchases->avg('total_amount'), 2)); ?> ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchases Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المشتريات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($purchases->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم المشترى</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>عدد الأصناف</th>
                                    <th>الإجمالي</th>
                                    <th>الحالة</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $purchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($purchase->purchase_number); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($purchase->supplier->name); ?></strong>
                                            <?php if($purchase->supplier->phone): ?>
                                            <br><small class="text-muted"><?php echo e($purchase->supplier->phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php echo e($purchase->purchase_date->format('Y-m-d')); ?>

                                        <br><small class="text-muted"><?php echo e($purchase->created_at->format('H:i')); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo e($purchase->purchaseItems->count()); ?> صنف</span>
                                    </td>
                                    <td>
                                        <strong><?php echo e(number_format($purchase->total_amount, 2)); ?> ر.ي</strong>
                                    </td>
                                    <td>
                                        <?php if($purchase->status === 'مكتملة'): ?>
                                            <span class="badge bg-success">مكتملة</span>
                                        <?php elseif($purchase->status === 'معلقة'): ?>
                                            <span class="badge bg-warning">معلقة</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo e($purchase->status); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($purchase->user->name ?? 'غير محدد'); ?>

                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('purchases.show', $purchase)); ?>"
                                               class="btn btn-sm btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('purchases.edit', $purchase)); ?>"
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-danger"
                                                    onclick="deletePurchase(<?php echo e($purchase->id); ?>)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="صفحات المشتريات">
                            <?php echo e($purchases->links('pagination::bootstrap-4')); ?>

                        </nav>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مشتريات</h5>
                        <p class="text-muted">ابدأ بإضافة مشترى جديد</p>
                        <a href="<?php echo e(route('purchases.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>مشترى جديد
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المشترى؟</p>
                <p class="text-danger"><strong>تحذير:</strong> سيتم إرجاع الكميات من المخزون</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deletePurchase(id) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/purchases/${id}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<?php $__env->startPush('styles'); ?>
<style>
/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/purchases/index.blade.php ENDPATH**/ ?>