<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Unit;

class UnitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $perPage = request('per_page', 20);
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 20;

        $units = Unit::withCount('products')
            ->when(request('search'), function($query, $search) {
                $query->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('symbol', 'like', "%{$search}%");
            })
            ->when(request('status'), function($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->latest()
            ->paginate($perPage)
            ->appends(request()->query());

        return view('units.index', compact('units'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('units.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name_ar' => 'required|string|max:255|unique:units,name_ar',
            'name_en' => 'nullable|string|max:255|unique:units,name_en',
            'symbol' => 'nullable|string|max:10',
            'description' => 'nullable|string',
        ]);

        Unit::create($request->all());

        return redirect()->route('units.index')
            ->with('success', 'تم إضافة الوحدة بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Unit $unit)
    {
        $unit->load(['products' => function($query) {
            $query->latest()->take(10);
        }]);

        return view('units.show', compact('unit'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Unit $unit)
    {
        return view('units.edit', compact('unit'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Unit $unit)
    {
        $request->validate([
            'name_ar' => 'required|string|max:255|unique:units,name_ar,' . $unit->id,
            'name_en' => 'nullable|string|max:255|unique:units,name_en,' . $unit->id,
            'symbol' => 'nullable|string|max:10',
            'description' => 'nullable|string',
        ]);

        $unit->update($request->all());

        return redirect()->route('units.index')
            ->with('success', 'تم تحديث الوحدة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Unit $unit)
    {
        if ($unit->products()->exists()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف الوحدة لأنها مستخدمة في منتجات');
        }

        $unit->delete();

        return redirect()->route('units.index')
            ->with('success', 'تم حذف الوحدة بنجاح');
    }
}
