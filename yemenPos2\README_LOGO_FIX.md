# حل مشكلة رفع الشعار في إعدادات الشركة

## المشكلة
- حقل شعار الشركة يظهر علامة استفهام
- لا يمكن رفع الصورة أو لا تُحفظ
- الشعار لا يظهر في الفواتير

## الحل

### 1. تشغيل الأوامر المطلوبة

#### الطريقة الأولى: استخدام ملف batch (الأسهل)
```bash
# تشغيل الملف التالي من مجلد المشروع
fix_logo.bat
```

#### الطريقة الثانية: تشغيل الأوامر يدوياً
```bash
# 1. تشغيل migration لتحديث نوع الحقل
php artisan migrate --force

# 2. إنشاء symbolic link للتخزين
php artisan storage:link

# 3. مسح الكاش
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

### 2. التحقق من المجلدات

تأكد من وجود المجلدات التالية:
- `storage/app/public/`
- `storage/app/public/settings/`
- `public/storage/` (symbolic link)

### 3. التحقق من الصلاحيات

تأكد من أن المجلدات لها صلاحيات الكتابة:
```bash
chmod 755 storage/app/public
chmod 755 storage/app/public/settings
```

## التحسينات المطبقة

### 1. تصحيح نوع الحقل
- تم تغيير نوع حقل `company_logo` من `string` إلى `file`
- تم إنشاء migration لتحديث قاعدة البيانات

### 2. تحسين واجهة رفع الشعار
- إضافة معاينة للصورة قبل الرفع
- التحقق من نوع وحجم الملف
- تحسين التصميم والمظهر
- إضافة رسائل توضيحية

### 3. عرض الشعار في الفواتير
- تم تحديث قالب طباعة الفاتورة
- الشعار يظهر الآن في رأس الفاتورة
- استخدام بيانات الشركة من الإعدادات

## كيفية الاستخدام

1. اذهب إلى **الإعدادات** من القائمة الجانبية
2. ابحث عن قسم **إعدادات الشركة**
3. في حقل **شعار الشركة**:
   - اضغط على "اختيار ملف"
   - اختر صورة الشعار (JPG, PNG, GIF, WebP)
   - ستظهر معاينة للصورة
4. اضغط **حفظ الإعدادات**
5. الشعار سيظهر الآن في جميع الفواتير

## المتطلبات التقنية

- حجم الصورة: أقل من 2 ميجابايت
- الصيغ المدعومة: JPG, PNG, GIF, WebP
- الأبعاد المقترحة: 200x100 بكسل أو أقل

## استكشاف الأخطاء

### إذا لم تظهر الصورة:
1. تأكد من تشغيل `php artisan storage:link`
2. تحقق من وجود الملف في `storage/app/public/settings/`
3. تأكد من صلاحيات المجلدات

### إذا ظهرت رسالة خطأ:
1. تحقق من حجم الصورة (أقل من 2MB)
2. تأكد من صيغة الصورة المدعومة
3. تحقق من مساحة التخزين المتاحة

## الملفات المُحدثة

- `database/seeders/SettingSeeder.php` - تصحيح نوع الحقل
- `database/migrations/2024_01_15_000000_update_company_logo_setting_type.php` - Migration جديد
- `resources/views/settings/index.blade.php` - تحسين واجهة رفع الشعار
- `resources/views/sales/print.blade.php` - عرض الشعار في الفواتير
- `fix_logo.bat` - ملف تشغيل الأوامر

## الدعم

إذا واجهت أي مشاكل، تأكد من:
1. تشغيل جميع الأوامر المطلوبة
2. التحقق من صلاحيات المجلدات
3. مراجعة سجلات الأخطاء في `storage/logs/`
