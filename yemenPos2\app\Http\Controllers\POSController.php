<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\SaleItem;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class POSController extends Controller
{
    /**
     * عرض شاشة نقطة البيع
     */
    public function index()
    {
        $categories = Category::where('is_active', true)->get();
        $customers = Customer::where('is_active', true)->get();

        return view('pos.index', compact('categories', 'customers'));
    }

    /**
     * البحث عن المنتجات
     */
    public function searchProducts(Request $request)
    {
        try {
            $search = $request->input('search', '');
            $category_id = $request->input('category_id', '');

            $query = Product::where('is_active', true);

            // البحث بالنص
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%")
                      ->orWhere('barcode', 'like', "%{$search}%");
                });
            }

            // فلترة حسب التصنيف
            if (!empty($category_id) && $category_id !== 'all') {
                $query->where('category_id', $category_id);
            }

            $products = $query->orderBy('name_ar')->get();

            return response()->json($products);

        } catch (\Exception $e) {
            Log::error('POS Search Error: ' . $e->getMessage());
            return response()->json(['error' => 'خطأ في تحميل المنتجات'], 500);
        }
    }

    /**
     * البحث عن منتج بالباركود
     */
    public function searchByBarcode(Request $request)
    {
        $barcode = $request->get('barcode');

        $product = Product::with('category')
            ->where('barcode', $barcode)
            ->where('is_active', true)
            ->where('stock_quantity', '>', 0)
            ->first();

        if ($product) {
            return response()->json([
                'success' => true,
                'product' => $product
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'المنتج غير موجود أو نفد من المخزون'
        ]);
    }

    /**
     * إنشاء فاتورة جديدة
     */
    public function createSale(Request $request)
    {
        // التحقق من صحة البيانات مع قواعد خاصة للدفع الآجل
        $rules = [
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'payment_method' => 'required|in:نقدي,بطاقة ائتمان,تحويل بنكي,آجل',
            'discount_amount' => 'nullable|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'nullable|numeric|min:0',
            'remaining_amount' => 'nullable|numeric',
        ];

        // إذا كان الدفع آجل، يجب تحديد عميل
        if ($request->payment_method === 'آجل') {
            $rules['customer_id'] = 'required|exists:customers,id';
        }

        $request->validate($rules);

        try {
            DB::beginTransaction();

            // حساب المجاميع
            $subtotal = 0;
            foreach ($request->items as $item) {
                $subtotal += $item['quantity'] * $item['price'];
            }

            $discount = $request->discount_amount ?? 0;
            $tax = $request->tax_amount ?? 0;
            $total = $subtotal - $discount + $tax;

            // تحديد المبالغ حسب طريقة الدفع
            if ($request->payment_method === 'آجل') {
                // دفع آجل = لم يدفع شيء
                $paidAmount = 0;
                $remainingAmount = $total;
                $status = 'غير مدفوعة'; // آجل = غير مدفوعة
            } else {
                // دفع فوري - التحقق من المبلغ المدفوع
                $paidAmount = $request->paid_amount ?? $total;

                // التأكد من أن المبلغ المدفوع لا يتجاوز الإجمالي
                if ($paidAmount > $total) {
                    $paidAmount = $total;
                }

                $remainingAmount = $total - $paidAmount;

                // تحديد الحالة حسب المبلغ المدفوع
                if ($remainingAmount <= 0) {
                    $status = 'مكتملة'; // مدفوع بالكامل
                } elseif ($paidAmount > 0) {
                    $status = 'دفع جزئي'; // دفع جزئي
                } else {
                    $status = 'غير مدفوعة'; // لم يدفع شيء
                }
            }

            // إنشاء الفاتورة (غير مرحلة افتراضياً)
            $sale = Sale::create([
                'invoice_number' => $this->generateInvoiceNumber(),
                'customer_id' => $request->customer_id,
                'user_id' => Auth::id() ?? 1,
                'sale_date' => now()->format('Y-m-d'),
                'sale_time' => now(), // حفظ الوقت كـ datetime
                'subtotal' => $subtotal,
                'discount_amount' => $discount,
                'tax_amount' => $tax,
                'total_amount' => $total,
                'paid_amount' => $paidAmount,
                'remaining_amount' => $remainingAmount,
                'payment_method' => $request->payment_method,
                'status' => $status,
                'is_posted' => false, // غير مرحلة افتراضياً
                'posted_at' => null,
                'posted_by' => null,
                'notes' => $request->notes,
            ]);

            // إضافة عناصر الفاتورة
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);

                // التحقق من المخزون
                if ($product->stock_quantity < $item['quantity']) {
                    throw new \Exception("المنتج {$product->name_ar} غير متوفر بالكمية المطلوبة");
                }

                // إضافة العنصر
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $product->name_ar,
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['price'],
                    'total_price' => $item['quantity'] * $item['price'],
                    'unit' => 'قطعة',
                ]);

                // تحديث المخزون
                $product->decrement('stock_quantity', $item['quantity']);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'sale_id' => $sale->id,
                'invoice_number' => $sale->invoice_number,
                'status' => $sale->status,
                'total_amount' => $sale->total_amount,
                'paid_amount' => $sale->paid_amount,
                'remaining_amount' => $sale->remaining_amount,
                'message' => 'تم إنشاء الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            Log::error('خطأ في إنشاء الفاتورة: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'error_trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الفاتورة: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * توليد رقم فاتورة جديد
     */
    private function generateInvoiceNumber()
    {
        $lastSale = Sale::latest('id')->first();
        $nextNumber = $lastSale ? $lastSale->id + 1 : 1;

        return 'INV-' . date('Y') . '-' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }
}
