<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Unit;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = [
            [
                'name_ar' => 'قطعة',
                'name_en' => 'Piece',
                'symbol' => 'ق',
                'description' => 'وحدة لعد القطع الفردية',
                'is_active' => true,
            ],
            [
                'name_ar' => 'كيلوجرام',
                'name_en' => 'Kilogram',
                'symbol' => 'كجم',
                'description' => 'وحدة قياس الوزن',
                'is_active' => true,
            ],
            [
                'name_ar' => 'جرام',
                'name_en' => 'Gram',
                'symbol' => 'جم',
                'description' => 'وحدة قياس الوزن الصغيرة',
                'is_active' => true,
            ],
            [
                'name_ar' => 'لتر',
                'name_en' => 'Liter',
                'symbol' => 'لتر',
                'description' => 'وحدة قياس السوائل',
                'is_active' => true,
            ],
            [
                'name_ar' => 'مليلتر',
                'name_en' => 'Milliliter',
                'symbol' => 'مل',
                'description' => 'وحدة قياس السوائل الصغيرة',
                'is_active' => true,
            ],
            [
                'name_ar' => 'علبة',
                'name_en' => 'Box',
                'symbol' => 'علبة',
                'description' => 'وحدة للمنتجات المعلبة',
                'is_active' => true,
            ],
            [
                'name_ar' => 'زجاجة',
                'name_en' => 'Bottle',
                'symbol' => 'زجاجة',
                'description' => 'وحدة للمنتجات في زجاجات',
                'is_active' => true,
            ],
            [
                'name_ar' => 'كيس',
                'name_en' => 'Bag',
                'symbol' => 'كيس',
                'description' => 'وحدة للمنتجات المعبأة في أكياس',
                'is_active' => true,
            ],
            [
                'name_ar' => 'أنبوب',
                'name_en' => 'Tube',
                'symbol' => 'أنبوب',
                'description' => 'وحدة للمنتجات في أنابيب',
                'is_active' => true,
            ],
            [
                'name_ar' => 'دفتر',
                'name_en' => 'Notebook',
                'symbol' => 'دفتر',
                'description' => 'وحدة للدفاتر والكتب',
                'is_active' => true,
            ],
            [
                'name_ar' => 'متر',
                'name_en' => 'Meter',
                'symbol' => 'م',
                'description' => 'وحدة قياس الطول',
                'is_active' => true,
            ],
            [
                'name_ar' => 'سنتيمتر',
                'name_en' => 'Centimeter',
                'symbol' => 'سم',
                'description' => 'وحدة قياس الطول الصغيرة',
                'is_active' => true,
            ],
        ];

        foreach ($units as $unit) {
            Unit::create($unit);
        }
    }
}
