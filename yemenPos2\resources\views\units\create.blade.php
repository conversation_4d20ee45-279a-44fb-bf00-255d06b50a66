@extends('layouts.app')

@section('title', 'إضافة وحدة جديدة - نظام نقطة المبيعات')
@section('page-title', 'إضافة وحدة جديدة')

@push('styles')
<style>
    .unit-form-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 200px);
        padding: 30px 0;
    }
    
    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }
    
    .form-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }
    
    .form-header::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }
    
    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-section:hover {
        border-color: #6f42c1;
        box-shadow: 0 5px 15px rgba(111, 66, 193, 0.1);
    }
    
    .section-title {
        color: #6f42c1;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }
    
    .section-title i {
        margin-left: 10px;
        font-size: 1.2rem;
    }
    
    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        border: none;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(111, 66, 193, 0.3);
    }
    
    .btn-back {
        border: 2px solid #6c757d;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .btn-back:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }
    
    .required-asterisk {
        color: #dc3545;
        font-weight: bold;
    }
    
    .form-check-input:checked {
        background-color: #6f42c1;
        border-color: #6f42c1;
    }
    
    .symbol-preview {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
        min-height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endpush

@section('content')
<div class="unit-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card">
                    <div class="form-header">
                        <i class="fas fa-balance-scale fa-3x mb-3"></i>
                        <h2 class="mb-2">إضافة وحدة جديدة</h2>
                        <p class="mb-0 opacity-75">أضف وحدة قياس جديدة للمنتجات</p>
                    </div>
                    <div class="p-4">
                        <form action="{{ route('units.store') }}" method="POST" id="unitForm">
                            @csrf
                            
                            <!-- المعلومات الأساسية -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    المعلومات الأساسية
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_ar" class="form-label">
                                                اسم الوحدة (عربي) <span class="required-asterisk">*</span>
                                            </label>
                                            <input type="text" class="form-control @error('name_ar') is-invalid @enderror"
                                                   id="name_ar" name="name_ar" value="{{ old('name_ar') }}"
                                                   placeholder="مثال: كيلوجرام، لتر، قطعة" required>
                                            @error('name_ar')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_en" class="form-label">اسم الوحدة (إنجليزي)</label>
                                            <input type="text" class="form-control @error('name_en') is-invalid @enderror"
                                                   id="name_en" name="name_en" value="{{ old('name_en') }}"
                                                   placeholder="Example: Kilogram, Liter, Piece">
                                            @error('name_en')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="symbol" class="form-label">رمز الوحدة</label>
                                            <input type="text" class="form-control @error('symbol') is-invalid @enderror"
                                                   id="symbol" name="symbol" value="{{ old('symbol') }}"
                                                   placeholder="مثال: كجم، لتر، ق" maxlength="10">
                                            @error('symbol')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">رمز مختصر للوحدة (اختياري)</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">معاينة الرمز</label>
                                            <div class="symbol-preview" id="symbolPreview">
                                                لا يوجد رمز
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الوصف -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-align-left"></i>
                                    الوصف
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">وصف الوحدة</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description" name="description" rows="4"
                                              placeholder="أدخل وصف مفصل للوحدة وكيفية استخدامها...">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <!-- الإعدادات -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-cogs"></i>
                                    إعدادات الوحدة
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                <strong>وحدة نشطة</strong>
                                                <br><small class="text-muted">تظهر الوحدة في قوائم المنتجات</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- أزرار الإجراءات -->
                            <div class="d-flex justify-content-between align-items-center pt-4">
                                <a href="{{ route('units.index') }}" class="btn btn-back">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    العودة للقائمة
                                </a>
                                
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-save">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الوحدة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// معاينة الرمز
function updateSymbolPreview() {
    const symbol = document.getElementById('symbol').value;
    const preview = document.getElementById('symbolPreview');
    
    if (symbol.trim()) {
        preview.textContent = symbol;
    } else {
        preview.textContent = 'لا يوجد رمز';
    }
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('unitForm').reset();
        updateSymbolPreview();
        showToast('تم إعادة تعيين النموذج', 'info');
    }
}

// إظهار رسائل التنبيه
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// التحقق من صحة النموذج قبل الإرسال
function validateForm() {
    const form = document.getElementById('unitForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        showToast('يرجى ملء جميع الحقول المطلوبة', 'danger');
        return false;
    }
    
    return true;
}

// إعداد الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الرمز عند الكتابة
    document.getElementById('symbol').addEventListener('input', updateSymbolPreview);
    
    // التحقق من النموذج عند الإرسال
    document.getElementById('unitForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // معاينة الرمز الأولية
    updateSymbolPreview();
});
</script>
@endpush
