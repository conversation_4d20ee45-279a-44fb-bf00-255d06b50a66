<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة الحدود المنقطة للفاتورة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            padding: 20px;
        }

        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 40px;
            background: white;
            border: 3px dashed #2c3e50;
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* زخرفة الحدود الخارجية */
        .invoice-container::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border: 2px dashed #3498db;
            border-radius: 18px;
            z-index: -1;
        }

        /* حدود داخلية منقطة */
        .invoice-container::after {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 1px dotted #bdc3c7;
            border-radius: 10px;
            z-index: -1;
        }

        .invoice-header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .company-info {
            color: #666;
            font-size: 12px;
        }

        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            border: 2px solid #2c3e50;
            border-radius: 12px;
            overflow: hidden;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .customer-info {
            flex: 1;
            padding: 20px;
            text-align: right;
            background: #f8f9fa;
            position: relative;
        }

        .customer-info::after {
            content: '';
            position: absolute;
            top: 10px;
            bottom: 10px;
            left: 0;
            width: 3px;
            background: linear-gradient(to bottom, #2c3e50, #3498db, #2c3e50);
            border-radius: 2px;
        }

        .invoice-info {
            flex: 1;
            padding: 20px;
            text-align: left;
            background: #ffffff;
        }

        .info-title {
            background: linear-gradient(135deg, #3498db, #2c3e50);
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 15px -20px;
            text-align: center;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px dotted #dee2e6;
            font-size: 14px;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .sample-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 2px solid #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .sample-table th,
        .sample-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .sample-table th {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .totals-section {
            margin-left: auto;
            width: 350px;
            border: 2px solid #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background: #ffffff;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }

        .total-row:last-child {
            border-bottom: none;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            font-weight: bold;
            font-size: 18px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 3px solid #2c3e50;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 10px 10px 0 0;
        }

        .test-info {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .test-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .border-demo {
            margin: 20px 0;
            padding: 15px;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }

        .border-demo.outer {
            border: 3px dashed #2c3e50;
            border-radius: 15px;
            background: #f8f9fa;
            color: #2c3e50;
        }

        .border-demo.middle {
            border: 2px dashed #3498db;
            border-radius: 12px;
            background: #e3f2fd;
            color: #1976d2;
            margin: 10px;
        }

        .border-demo.inner {
            border: 1px dotted #bdc3c7;
            border-radius: 8px;
            background: #ffffff;
            color: #666;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h3>🎨 معاينة الحدود المنقطة للفاتورة</h3>
        <p>هذا مثال على التصميم الجديد مع الحدود المنقطة الثلاثية</p>
    </div>

    <!-- عرض أنواع الحدود -->
    <div class="border-demo outer">
        الحدود الخارجية - خط منقط سميك (#2c3e50)
        <div class="border-demo middle">
            الحدود الوسطى - خط منقط متوسط (#3498db)
            <div class="border-demo inner">
                الحدود الداخلية - خط منقط رفيع (#bdc3c7)
            </div>
        </div>
    </div>

    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="company-name">شركة التجارة المتقدمة</div>
            <div class="company-info">
                العنوان: صنعاء، اليمن | الهاتف: +*********** 789 | البريد: <EMAIL>
            </div>
            
            <!-- معلومات الضريبة -->
            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                <div style="display: flex; justify-content: center; gap: 30px; font-size: 12px; color: #666;">
                    <span><strong>الرقم الضريبي:</strong> 053551252022</span>
                    <span><strong>ضريبة القيمة المضافة:</strong> 15%</span>
                </div>
            </div>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="customer-info">
                <div class="info-title">بيانات العميل</div>
                <div class="info-row">
                    <span>العميل:</span>
                    <span>أحمد محمد علي</span>
                </div>
                <div class="info-row">
                    <span>الهاتف:</span>
                    <span>777123456</span>
                </div>
                <div class="info-row">
                    <span>العنوان:</span>
                    <span>صنعاء، اليمن</span>
                </div>
            </div>

            <div class="invoice-info">
                <div class="info-title">بيانات الفاتورة</div>
                <div class="info-row">
                    <span>رقم الفاتورة:</span>
                    <span>INV-2025-000001</span>
                </div>
                <div class="info-row">
                    <span>التاريخ:</span>
                    <span>2025-05-28</span>
                </div>
                <div class="info-row">
                    <span>الوقت:</span>
                    <span>2:30 مساءً</span>
                </div>
                <div class="info-row">
                    <span>الكاشير:</span>
                    <span>محمد أحمد</span>
                </div>
            </div>
        </div>

        <!-- Sample Table -->
        <table class="sample-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>منتج تجريبي</td>
                    <td>2</td>
                    <td>500.00 ر.ي</td>
                    <td>1000.00 ر.ي</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>منتج آخر</td>
                    <td>1</td>
                    <td>300.00 ر.ي</td>
                    <td>300.00 ر.ي</td>
                </tr>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>1300.00 ر.ي</span>
            </div>
            <div class="total-row">
                <span>الخصم:</span>
                <span>100.00 ر.ي</span>
            </div>
            <div class="total-row">
                <span>ضريبة القيمة المضافة (15%):</span>
                <span>180.00 ر.ي</span>
            </div>
            <div class="total-row">
                <span>الإجمالي:</span>
                <span>1380.00 ر.ي</span>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>شكراً لتعاملكم معنا</strong></p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام نقطة البيع</p>
        </div>
    </div>

    <div class="test-info" style="margin-top: 30px;">
        <h3>✅ الميزات المطبقة</h3>
        <p><strong>حدود خارجية منقطة:</strong> خط منقط سميك باللون الأزرق الداكن</p>
        <p><strong>حدود وسطى:</strong> خط منقط متوسط باللون الأزرق الفاتح</p>
        <p><strong>حدود داخلية:</strong> خط منقط رفيع باللون الرمادي</p>
        <p><strong>تأثيرات بصرية:</strong> ظلال وتدرجات لونية جميلة</p>
    </div>
</body>
</html>
