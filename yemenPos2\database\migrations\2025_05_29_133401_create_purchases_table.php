<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchases', function (Blueprint $table) {
            $table->id();

            // رقم المشترى
            $table->string('purchase_number')->unique();

            // ربط بالمورد
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');

            // ربط بالمستخدم الذي أنشأ المشترى
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // تاريخ المشترى
            $table->date('purchase_date');

            // إجمالي المبلغ
            $table->decimal('total_amount', 12, 2)->default(0);

            // حالة المشترى
            $table->enum('status', ['pending', 'received', 'partial', 'cancelled'])
                  ->default('pending');

            // ملاحظات
            $table->text('notes')->nullable();

            // معلومات الدفع
            $table->decimal('paid_amount', 12, 2)->default(0);
            $table->decimal('remaining_amount', 12, 2)->default(0);
            $table->enum('payment_status', ['unpaid', 'partial', 'paid'])
                  ->default('unpaid');

            $table->timestamps();

            // فهارس للبحث السريع
            $table->index(['purchase_date', 'status']);
            $table->index(['supplier_id', 'purchase_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchases');
    }
};
