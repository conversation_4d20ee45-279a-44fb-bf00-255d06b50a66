<?php $__env->startSection('title', 'إضافة دفعة جديدة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('payments.index')); ?>">المدفوعات</a></li>
                            <li class="breadcrumb-item active">إضافة دفعة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?php echo e(route('payments.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                بيانات الدفعة الجديدة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo e(route('payments.store')); ?>" method="POST">
                                <?php echo csrf_field(); ?>

                                <!-- نوع المعاملة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">نوع المعاملة <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['payable_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                name="payable_type" id="payable_type" required>
                                            <option value="">اختر نوع المعاملة</option>
                                            <option value="App\Models\Sale" <?php echo e(old('payable_type') == 'App\Models\Sale' ? 'selected' : ''); ?>>
                                                مبيعات (دفع من عميل)
                                            </option>
                                            <option value="App\Models\Purchase" <?php echo e(old('payable_type') == 'App\Models\Purchase' ? 'selected' : ''); ?>>
                                                مشتريات (دفع لمورد)
                                            </option>
                                        </select>
                                        <?php $__errorArgs = ['payable_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الفاتورة <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['payable_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                name="payable_id" id="payable_id" required>
                                            <option value="">اختر الفاتورة</option>
                                        </select>
                                        <?php $__errorArgs = ['payable_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <!-- العميل/المورد -->
                                <div class="row mb-3">
                                    <div class="col-md-6" id="customer_section" style="display: none;">
                                        <label class="form-label">العميل</label>
                                        <select class="form-select" name="customer_id" id="customer_id">
                                            <option value="">اختر العميل</option>
                                            <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($customer->id); ?>"><?php echo e($customer->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6" id="supplier_section" style="display: none;">
                                        <label class="form-label">المورد</label>
                                        <select class="form-select" name="supplier_id" id="supplier_id">
                                            <option value="">اختر المورد</option>
                                            <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($supplier->id); ?>"><?php echo e($supplier->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- معلومات الدفعة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   name="amount" value="<?php echo e(old('amount')); ?>" 
                                                   step="0.01" min="0" required>
                                            <span class="input-group-text">ر.ي</span>
                                        </div>
                                        <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">نوع الدفعة <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['payment_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                name="payment_type" required>
                                            <option value="دفعة" <?php echo e(old('payment_type') == 'دفعة' ? 'selected' : ''); ?>>دفعة</option>
                                            <option value="استرداد" <?php echo e(old('payment_type') == 'استرداد' ? 'selected' : ''); ?>>استرداد</option>
                                        </select>
                                        <?php $__errorArgs = ['payment_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <!-- طريقة الدفع والتاريخ -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                name="payment_method" id="payment_method" required>
                                            <option value="نقدي" <?php echo e(old('payment_method') == 'نقدي' ? 'selected' : ''); ?>>نقدي</option>
                                            <option value="تحويل بنكي" <?php echo e(old('payment_method') == 'تحويل بنكي' ? 'selected' : ''); ?>>تحويل بنكي</option>
                                            <option value="شيك" <?php echo e(old('payment_method') == 'شيك' ? 'selected' : ''); ?>>شيك</option>
                                            <option value="بطاقة ائتمان" <?php echo e(old('payment_method') == 'بطاقة ائتمان' ? 'selected' : ''); ?>>بطاقة ائتمان</option>
                                        </select>
                                        <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الدفعة <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control <?php $__errorArgs = ['payment_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               name="payment_date" value="<?php echo e(old('payment_date', date('Y-m-d'))); ?>" required>
                                        <?php $__errorArgs = ['payment_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <!-- تفاصيل إضافية حسب طريقة الدفع -->
                                <div id="bank_details" style="display: none;">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">رقم المرجع</label>
                                            <input type="text" class="form-control" name="reference_number" 
                                                   value="<?php echo e(old('reference_number')); ?>" placeholder="رقم التحويل">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">اسم البنك</label>
                                            <input type="text" class="form-control" name="bank_name" 
                                                   value="<?php echo e(old('bank_name')); ?>" placeholder="البنك الأهلي">
                                        </div>
                                    </div>
                                </div>

                                <div id="check_details" style="display: none;">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">رقم الشيك</label>
                                            <input type="text" class="form-control" name="check_number" 
                                                   value="<?php echo e(old('check_number')); ?>" placeholder="123456">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">تاريخ الشيك</label>
                                            <input type="date" class="form-control" name="check_date" 
                                                   value="<?php echo e(old('check_date')); ?>">
                                        </div>
                                    </div>
                                </div>

                                <!-- الحالة والملاحظات -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">حالة الدفعة <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                name="status" required>
                                            <option value="مؤكد" <?php echo e(old('status') == 'مؤكد' ? 'selected' : ''); ?>>مؤكد</option>
                                            <option value="معلق" <?php echo e(old('status') == 'معلق' ? 'selected' : ''); ?>>معلق</option>
                                            <option value="ملغي" <?php echo e(old('status') == 'ملغي' ? 'selected' : ''); ?>>ملغي</option>
                                        </select>
                                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                              name="notes" rows="3" placeholder="ملاحظات إضافية..."><?php echo e(old('notes')); ?></textarea>
                                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- أزرار الحفظ -->
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?php echo e(route('payments.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ الدفعة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const payableType = document.getElementById('payable_type');
    const payableId = document.getElementById('payable_id');
    const customerSection = document.getElementById('customer_section');
    const supplierSection = document.getElementById('supplier_section');
    const paymentMethod = document.getElementById('payment_method');
    const bankDetails = document.getElementById('bank_details');
    const checkDetails = document.getElementById('check_details');

    // تغيير نوع المعاملة
    payableType.addEventListener('change', function() {
        const type = this.value;
        payableId.innerHTML = '<option value="">اختر الفاتورة</option>';
        
        if (type === 'App\\Models\\Sale') {
            customerSection.style.display = 'block';
            supplierSection.style.display = 'none';
            loadSales();
        } else if (type === 'App\\Models\\Purchase') {
            customerSection.style.display = 'none';
            supplierSection.style.display = 'block';
            loadPurchases();
        } else {
            customerSection.style.display = 'none';
            supplierSection.style.display = 'none';
        }
    });

    // تغيير طريقة الدفع
    paymentMethod.addEventListener('change', function() {
        const method = this.value;
        
        bankDetails.style.display = method === 'تحويل بنكي' ? 'block' : 'none';
        checkDetails.style.display = method === 'شيك' ? 'block' : 'none';
    });

    // تحميل المبيعات
    function loadSales() {
        fetch('/api/sales/unpaid')
            .then(response => response.json())
            .then(data => {
                data.forEach(sale => {
                    const option = document.createElement('option');
                    option.value = sale.id;
                    option.textContent = `${sale.invoice_number} - ${sale.customer?.name || 'عميل عادي'} - ${sale.remaining_amount} ر.ي`;
                    payableId.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading sales:', error));
    }

    // تحميل المشتريات
    function loadPurchases() {
        fetch('/api/purchases/unpaid')
            .then(response => response.json())
            .then(data => {
                data.forEach(purchase => {
                    const option = document.createElement('option');
                    option.value = purchase.id;
                    option.textContent = `${purchase.purchase_number} - ${purchase.supplier?.name || 'مورد'} - ${purchase.remaining_amount} ر.ي`;
                    payableId.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading purchases:', error));
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/payments/create.blade.php ENDPATH**/ ?>