<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'admin',
                'display_name' => 'مدير النظام',
                'description' => 'صلاحيات كاملة لإدارة النظام',
                'permissions' => json_encode([
                    'users.view', 'users.create', 'users.edit', 'users.delete',
                    'roles.view', 'roles.create', 'roles.edit', 'roles.delete',
                    'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
                    'products.view', 'products.create', 'products.edit', 'products.delete',
                    'customers.view', 'customers.create', 'customers.edit', 'customers.delete',
                    'suppliers.view', 'suppliers.create', 'suppliers.edit', 'suppliers.delete',
                    'sales.view', 'sales.create', 'sales.edit', 'sales.delete',
                    'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
                    'payments.view', 'payments.create', 'payments.edit', 'payments.delete',
                    'reports.view', 'settings.view', 'settings.edit'
                ])
            ],
            [
                'name' => 'manager',
                'display_name' => 'مدير',
                'description' => 'صلاحيات إدارية محدودة',
                'permissions' => json_encode([
                    'categories.view', 'categories.create', 'categories.edit',
                    'products.view', 'products.create', 'products.edit',
                    'customers.view', 'customers.create', 'customers.edit',
                    'suppliers.view', 'suppliers.create', 'suppliers.edit',
                    'sales.view', 'sales.create', 'sales.edit',
                    'purchases.view', 'purchases.create', 'purchases.edit',
                    'payments.view', 'payments.create', 'payments.edit',
                    'reports.view'
                ])
            ],
            [
                'name' => 'cashier',
                'display_name' => 'كاشير',
                'description' => 'صلاحيات المبيعات والعملاء',
                'permissions' => json_encode([
                    'products.view',
                    'customers.view', 'customers.create', 'customers.edit',
                    'sales.view', 'sales.create',
                    'payments.view', 'payments.create'
                ])
            ],
            [
                'name' => 'accountant',
                'display_name' => 'محاسب',
                'description' => 'صلاحيات المحاسبة والتقارير',
                'permissions' => json_encode([
                    'customers.view', 'suppliers.view',
                    'sales.view', 'purchases.view',
                    'payments.view', 'payments.create', 'payments.edit',
                    'reports.view'
                ])
            ]
        ];

        foreach ($roles as $role) {
            Role::create($role);
        }
    }
}
