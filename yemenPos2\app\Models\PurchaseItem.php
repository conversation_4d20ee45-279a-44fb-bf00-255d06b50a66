<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PurchaseItem extends Model
{
    protected $fillable = [
        'purchase_id',
        'product_id',
        'quantity',
        'purchase_price',
        'total_price',
        'expiry_date',
        'production_date',
        'batch_number',
        'remaining_quantity',
        'batch_status',
        'notes'
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'purchase_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'remaining_quantity' => 'decimal:2',
        'expiry_date' => 'date',
        'production_date' => 'date',
    ];

    /**
     * العلاقة مع المشترى
     */
    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    /**
     * العلاقة مع المنتج
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * إنشاء رقم دفعة تلقائي
     */
    public static function generateBatchNumber(int $productId): string
    {
        $product = Product::find($productId);
        $prefix = $product ? strtoupper(substr($product->sku, 0, 3)) : 'PRD';
        $date = Carbon::now()->format('Ymd');
        $sequence = static::where('batch_number', 'like', "{$prefix}-{$date}-%")->count() + 1;

        return "{$prefix}-{$date}-" . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    /**
     * التحقق من انتهاء الصلاحية
     */
    public function isExpired(): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return Carbon::now()->gt($this->expiry_date);
    }

    /**
     * التحقق من قرب انتهاء الصلاحية
     */
    public function isNearExpiry(int $days = 30): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return Carbon::now()->addDays($days)->gte($this->expiry_date) && !$this->isExpired();
    }

    /**
     * الحصول على الأيام المتبقية لانتهاء الصلاحية
     */
    public function getDaysToExpiry(): ?int
    {
        if (!$this->expiry_date) {
            return null;
        }

        return Carbon::now()->diffInDays($this->expiry_date, false);
    }

    /**
     * تحديث حالة الدفعة بناءً على تاريخ انتهاء الصلاحية
     */
    public function updateBatchStatus(): void
    {
        if ($this->remaining_quantity <= 0) {
            $this->batch_status = 'depleted';
        } elseif ($this->isExpired()) {
            $this->batch_status = 'expired';
        } else {
            $this->batch_status = 'active';
        }

        $this->save();
    }

    /**
     * تقليل الكمية المتبقية
     */
    public function reduceQuantity(float $quantity): bool
    {
        if ($this->remaining_quantity >= $quantity) {
            $this->remaining_quantity -= $quantity;
            $this->updateBatchStatus();
            return true;
        }

        return false;
    }

    /**
     * حساب سعر البيع المقترح بناءً على نسبة الربح
     */
    public function calculateSuggestedSellingPrice(float $profitMargin = 20): float
    {
        return $this->purchase_price * (1 + ($profitMargin / 100));
    }

    /**
     * الحصول على نسبة الكمية المتبقية
     */
    public function getRemainingPercentageAttribute(): float
    {
        if ($this->quantity <= 0) {
            return 0;
        }

        return ($this->remaining_quantity / $this->quantity) * 100;
    }
}
