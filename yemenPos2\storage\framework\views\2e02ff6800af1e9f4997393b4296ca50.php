<?php $__env->startSection('title', 'تفاصيل الفاتورة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-receipt me-2"></i>
                        تفاصيل الفاتورة <?php echo e($sale->invoice_number); ?>

                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('sales.index')); ?>">المبيعات</a></li>
                            <li class="breadcrumb-item active"><?php echo e($sale->invoice_number); ?></li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('sales.print', $sale)); ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-print me-2"></i>طباعة
                    </a>
                    <?php if($sale->customer && $sale->customer->whatsapp): ?>
                    <button class="btn btn-success" onclick="sendWhatsApp(<?php echo e($sale->id); ?>)">
                        <i class="fab fa-whatsapp me-2"></i>واتساب
                    </button>
                    <?php endif; ?>
                    <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم الفاتورة:</strong></td>
                                            <td><?php echo e($sale->invoice_number); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>التاريخ:</strong></td>
                                            <td><?php echo e($sale->sale_date->format('Y-m-d')); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>الوقت:</strong></td>
                                            <td>
                                                <?php echo e($sale->sale_time->format('g:i')); ?>

                                                <?php echo e($sale->sale_time->format('A') == 'AM' ? 'ص' : 'م'); ?>

                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الكاشير:</strong></td>
                                            <td><?php echo e($sale->user->name ?? 'غير محدد'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>العميل:</strong></td>
                                            <td><?php echo e($sale->customer->name ?? 'عميل عادي'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>طريقة الدفع:</strong></td>
                                            <td><?php echo e($sale->payment_method); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                <?php if($sale->status === 'مكتملة'): ?>
                                                    <span class="badge bg-success">مكتملة</span>
                                                <?php elseif($sale->status === 'غير مدفوعة'): ?>
                                                    <span class="badge bg-danger">غير مدفوعة</span>
                                                <?php elseif($sale->status === 'جزئي'): ?>
                                                    <span class="badge bg-warning">دفع جزئي</span>
                                                <?php else: ?>
                                                    <span class="badge bg-info"><?php echo e($sale->status); ?></span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php if($sale->notes): ?>
                                        <tr>
                                            <td><strong>ملاحظات:</strong></td>
                                            <td><?php echo e($sale->notes); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر الفاتورة -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                عناصر الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>#</th>
                                            <th>المنتج</th>
                                            <th>الكود</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الخصم</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $sale->saleItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($index + 1); ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo e($item->product_name); ?></strong>
                                                    <?php if($item->product): ?>
                                                        <br><small class="text-muted"><?php echo e($item->product->name_en ?? ''); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td><?php echo e($item->product_sku); ?></td>
                                            <td><?php echo e($item->quantity); ?> <?php echo e($item->unit ?? 'قطعة'); ?></td>
                                            <td><?php echo e(number_format($item->unit_price, 2)); ?> ر.ي</td>
                                            <td><?php echo e(number_format($item->discount_amount ?? 0, 2)); ?> ر.ي</td>
                                            <td>
                                                <strong><?php echo e(number_format($item->total_price, 2)); ?> ر.ي</strong>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>
                                ملخص الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td>المجموع الفرعي:</td>
                                    <td class="text-end"><?php echo e(number_format($sale->subtotal ?? 0, 2)); ?> ر.ي</td>
                                </tr>
                                <tr>
                                    <td>الخصم:</td>
                                    <td class="text-end text-danger">- <?php echo e(number_format($sale->discount_amount ?? 0, 2)); ?> ر.ي</td>
                                </tr>
                                <tr>
                                    <td>الضريبة:</td>
                                    <td class="text-end"><?php echo e(number_format($sale->tax_amount ?? 0, 2)); ?> ر.ي</td>
                                </tr>
                                <tr class="table-dark">
                                    <td><strong>الإجمالي:</strong></td>
                                    <td class="text-end"><strong><?php echo e(number_format($sale->total_amount, 2)); ?> ر.ي</strong></td>
                                </tr>
                                <?php if($sale->paid_amount): ?>
                                <tr>
                                    <td>المبلغ المدفوع:</td>
                                    <td class="text-end text-success"><?php echo e(number_format($sale->paid_amount, 2)); ?> ر.ي</td>
                                </tr>
                                <?php endif; ?>
                                <?php if($sale->remaining_amount && $sale->remaining_amount > 0): ?>
                                <tr>
                                    <td>المبلغ المتبقي:</td>
                                    <td class="text-end text-warning"><strong><?php echo e(number_format($sale->remaining_amount, 2)); ?> ر.ي</strong></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>

                    <?php if($sale->customer): ?>
                    <!-- معلومات العميل -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                معلومات العميل
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td><?php echo e($sale->customer->name); ?></td>
                                </tr>
                                <?php if($sale->customer->phone): ?>
                                <tr>
                                    <td><strong>الهاتف:</strong></td>
                                    <td><?php echo e($sale->customer->phone); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if($sale->customer->email): ?>
                                <tr>
                                    <td><strong>البريد:</strong></td>
                                    <td><?php echo e($sale->customer->email); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if($sale->customer->address): ?>
                                <tr>
                                    <td><strong>العنوان:</strong></td>
                                    <td><?php echo e($sale->customer->address); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                            <div class="d-grid">
                                <a href="<?php echo e(route('customers.show', $sale->customer)); ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-2"></i>عرض ملف العميل
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- إجراءات سريعة -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <?php if(($sale->status === 'جزئي' || $sale->status === 'غير مدفوعة') && $sale->remaining_amount > 0): ?>
                                <button class="btn btn-warning" onclick="payDebt(<?php echo e($sale->id); ?>, <?php echo e($sale->remaining_amount); ?>)">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    دفع المتبقي
                                </button>
                                <?php endif; ?>

                                <a href="<?php echo e(route('sales.print', $sale)); ?>" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة الفاتورة
                                </a>

                                <?php if($sale->customer && $sale->customer->whatsapp): ?>
                                <button class="btn btn-success" onclick="sendWhatsApp(<?php echo e($sale->id); ?>)">
                                    <i class="fab fa-whatsapp me-2"></i>
                                    إرسال واتساب
                                </button>
                                <?php endif; ?>

                                <a href="<?php echo e(route('pos.index')); ?>" class="btn btn-info">
                                    <i class="fas fa-plus me-2"></i>
                                    فاتورة جديدة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendWhatsApp(saleId) {
    fetch(`/sales/${saleId}/send-whatsapp`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.whatsapp_url, '_blank');
        } else {
            alert('خطأ: ' + data.message);
        }
    });
}

function payDebt(saleId, remainingAmount) {
    if (confirm(`هل تريد دفع المبلغ المتبقي ${remainingAmount.toFixed(2)} ر.ي؟`)) {
        window.location.href = `/sales-debts?sale_id=${saleId}`;
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/sales/show.blade.php ENDPATH**/ ?>