@extends('layouts.app')

@section('title', 'الفواتير غير المرحلة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-clock me-2 text-warning"></i>
                        الفواتير غير المرحلة
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('sales.index') }}">المبيعات</a></li>
                            <li class="breadcrumb-item active">غير المرحلة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('sales.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>العودة للمبيعات
                    </a>
                    <a href="{{ route('pos.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>فاتورة جديدة
                    </a>
                </div>
            </div>

            <!-- Alert -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> هذه الفواتير لم يتم ترحيلها بعد ولا تحسب ضمن المبيعات النهائية.
                يمكنك تعديلها أو حذفها أو ترحيلها لتصبح نهائية.
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>{{ $countUnposted }}</h3>
                            <p class="mb-0">عدد الفواتير غير المرحلة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>{{ number_format($totalUnposted, 2) }}</h3>
                            <p class="mb-0">إجمالي القيمة (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3>{{ $sales->count() }}</h3>
                            <p class="mb-0">الفواتير المعروضة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('sales.unposted') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search"
                                       value="{{ request('search') }}" placeholder="رقم الفاتورة أو اسم العميل...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from"
                                       value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to"
                                       value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="customer_id">
                                    <option value="">جميع العملاء</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                                {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" name="payment_method">
                                    <option value="">جميع الطرق</option>
                                    <option value="نقدي" {{ request('payment_method') == 'نقدي' ? 'selected' : '' }}>نقدي</option>
                                    <option value="بطاقة ائتمان" {{ request('payment_method') == 'بطاقة ائتمان' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                    <option value="تحويل بنكي" {{ request('payment_method') == 'تحويل بنكي' ? 'selected' : '' }}>تحويل بنكي</option>
                                    <option value="آجل" {{ request('payment_method') == 'آجل' ? 'selected' : '' }}>آجل</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <a href="{{ route('sales.unposted') }}" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sales Table -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        الفواتير غير المرحلة ({{ $sales->total() }})
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="postAllSales()">
                            <i class="fas fa-check-double me-1"></i>
                            ترحيل الكل
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if($sales->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        </th>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>طريقة الدفع</th>
                                        <th>حالة الدفع</th>
                                        <th>الكاشير</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sales as $sale)
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="sale_ids[]" value="{{ $sale->id }}" class="sale-checkbox">
                                        </td>
                                        <td>
                                            <a href="{{ route('sales.show', $sale) }}" class="text-decoration-none fw-bold">
                                                {{ $sale->invoice_number }}
                                            </a>
                                            <br>
                                            <small class="text-warning">
                                                <i class="fas fa-clock me-1"></i>غير مرحلة
                                            </small>
                                        </td>
                                        <td>
                                            @if($sale->customer)
                                                <div>
                                                    <span class="fw-bold">{{ $sale->customer->name }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $sale->customer->phone }}</small>
                                                </div>
                                            @else
                                                <span class="text-muted">عميل عادي</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>
                                                {{ $sale->sale_date->format('Y-m-d') }}
                                                <br>
                                                <small class="text-muted">
                                                    {{ $sale->sale_time->format('g:i') }}
                                                    {{ $sale->sale_time->format('A') == 'AM' ? 'ص' : 'م' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">
                                                {{ number_format($sale->total_amount, 2) }} ر.ي
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $sale->payment_method }}</span>
                                        </td>
                                        <td>
                                            @if($sale->status === 'مكتملة')
                                                <span class="badge bg-success">مكتملة</span>
                                            @elseif($sale->status === 'غير مدفوعة')
                                                <span class="badge bg-danger">غير مدفوعة</span>
                                            @elseif($sale->status === 'جزئي')
                                                <span class="badge bg-warning">دفع جزئي</span>
                                            @elseif($sale->status === 'مسترجعة')
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-undo me-1"></i>مسترجعة
                                                </span>
                                            @else
                                                <span class="badge bg-info">{{ $sale->status }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $sale->user->name }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('sales.show', $sale) }}"
                                                   class="btn btn-sm btn-outline-info"
                                                   title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('sales.print', $sale) }}"
                                                   class="btn btn-sm btn-outline-secondary"
                                                   title="طباعة"
                                                   target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>

                                                @if($sale->status !== 'مسترجعة')
                                                    <!-- تعديل -->
                                                    <a href="{{ route('sales.edit', $sale) }}"
                                                       class="btn btn-sm btn-outline-warning"
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>

                                                    <!-- استرجاع -->
                                                    <form method="POST" action="{{ route('sales.refund', $sale) }}" style="display: inline;">
                                                        @csrf
                                                        <button type="submit"
                                                                class="btn btn-sm btn-outline-info"
                                                                title="استرجاع"
                                                                onclick="return confirm('هل تريد استرجاع هذه الفاتورة؟ سيتم إعادة المخزون.')">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    </form>

                                                    <!-- ترحيل (فقط للفواتير غير المرتجعة) -->
                                                    @if($sale->status !== 'مسترجعة')
                                                        <form method="POST" action="{{ route('sales.post', $sale) }}" style="display: inline;">
                                                            @csrf
                                                            <button type="submit"
                                                                    class="btn btn-sm btn-outline-primary"
                                                                    title="ترحيل الفاتورة"
                                                                    onclick="return confirm('هل تريد ترحيل هذه الفاتورة؟')">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                    @endif

                                                    <!-- حذف -->
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            title="حذف"
                                                            onclick="deleteSale({{ $sale->id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @else
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-undo me-1"></i>مسترجعة
                                                    </span>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="صفحات الفواتير غير المرحلة">
                                {{ $sales->links('pagination::bootstrap-4') }}
                            </nav>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-success">ممتاز! جميع الفواتير مرحلة</h5>
                            <p class="text-muted">لا توجد فواتير غير مرحلة</p>
                            <a href="{{ route('sales.index') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>
                                العودة للمبيعات
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.alert {
    border-left: 4px solid #ffc107;
}

.card h3 {
    font-size: 2rem;
    font-weight: bold;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
</style>
@endpush

@push('scripts')
<script>
function deleteSale(saleId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/sales/${saleId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.sale-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function postAllSales() {
    const selectedSales = document.querySelectorAll('.sale-checkbox:checked');

    if (selectedSales.length === 0) {
        alert('يرجى تحديد فاتورة واحدة على الأقل');
        return;
    }

    if (!confirm(`هل تريد ترحيل ${selectedSales.length} فاتورة؟`)) {
        return;
    }

    // إنشاء form لإرسال البيانات
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("sales.post-multiple") }}';

    // إضافة CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    // إضافة معرفات الفواتير
    selectedSales.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'sale_ids[]';
        input.value = checkbox.value;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
}

// إضافة دالة إرسال واتساب
function sendWhatsApp(saleId) {
    // يمكن إضافة منطق إرسال واتساب هنا
    alert('ميزة إرسال واتساب قيد التطوير');
}
</script>
@endpush
