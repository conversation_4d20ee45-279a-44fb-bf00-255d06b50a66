@if ($paginator->hasPages())
<div class="pagination-wrapper">
    <div class="pagination-card">
        <div class="pagination-info">
            <div class="pagination-stats">
                <span class="stats-text">
                    عرض {{ $paginator->firstItem() ?? 0 }} إلى {{ $paginator->lastItem() ?? 0 }} 
                    من أصل {{ $paginator->total() }} نتيجة
                </span>
            </div>
            <div class="pagination-controls">
                <div class="page-size-selector">
                    <label for="pageSize" class="page-size-label">عرض:</label>
                    <select id="pageSize" class="page-size-select" onchange="changePageSize(this.value)">
                        <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10</option>
                        <option value="20" {{ request('per_page', 20) == 20 ? 'selected' : '' }}>20</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
            </div>
        </div>
        
        <nav class="pagination-nav" aria-label="Pagination Navigation">
            <ul class="pagination-list">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-double-right"></i>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @foreach ($elements as $element)
                    {{-- "Three Dots" Separator --}}
                    @if (is_string($element))
                        <li class="page-item disabled">
                            <span class="page-link dots">{{ $element }}</span>
                        </li>
                    @endif

                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <li class="page-item active">
                                    <span class="page-link current">{{ $page }}</span>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                </li>
                            @endif
                        @endforeach
                    @endif
                @endforeach

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-double-left"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </nav>
        
        {{-- Quick Jump --}}
        <div class="quick-jump">
            <label for="jumpToPage" class="jump-label">الانتقال إلى:</label>
            <input type="number" id="jumpToPage" class="jump-input" 
                   min="1" max="{{ $paginator->lastPage() }}" 
                   placeholder="{{ $paginator->currentPage() }}">
            <button type="button" class="jump-btn" onclick="jumpToPage()">
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>
    </div>
</div>

<style>
.pagination-wrapper {
    margin: 30px 0;
}

.pagination-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-stats {
    color: #6c757d;
    font-size: 0.9rem;
}

.stats-text {
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-size-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

.page-size-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 5px 10px;
    font-size: 0.9rem;
    background: white;
    color: #495057;
    transition: all 0.3s ease;
}

.page-size-select:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.pagination-nav {
    display: flex;
    justify-content: center;
}

.pagination-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 5px;
    align-items: center;
}

.page-item {
    display: flex;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    text-decoration: none;
    color: #667eea;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.page-link:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.page-item.disabled .page-link {
    color: #adb5bd;
    background: #f8f9fa;
    border-color: #e9ecef;
    cursor: not-allowed;
}

.page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
    background: #f8f9fa;
    color: #adb5bd;
}

.page-link.dots {
    border: none;
    background: transparent;
    color: #adb5bd;
    cursor: default;
}

.page-link.dots:hover {
    background: transparent;
    color: #adb5bd;
    transform: none;
    box-shadow: none;
}

.quick-jump {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.jump-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

.jump-input {
    width: 80px;
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.jump-input:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.jump-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.jump-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

@media (max-width: 768px) {
    .pagination-info {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .pagination-controls {
        justify-content: center;
    }
    
    .pagination-list {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .page-link {
        min-width: 35px;
        height: 35px;
        padding: 6px 10px;
        font-size: 0.85rem;
    }
    
    .quick-jump {
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<script>
function changePageSize(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}

function jumpToPage() {
    const pageInput = document.getElementById('jumpToPage');
    const page = parseInt(pageInput.value);
    const maxPage = {{ $paginator->lastPage() }};
    
    if (page && page >= 1 && page <= maxPage) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    } else {
        alert(`يرجى إدخال رقم صفحة صحيح بين 1 و ${maxPage}`);
        pageInput.focus();
    }
}

// Handle Enter key in jump input
document.getElementById('jumpToPage').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        jumpToPage();
    }
});
</script>
@endif
