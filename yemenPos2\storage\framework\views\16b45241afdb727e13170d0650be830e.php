<?php $__env->startSection('title', 'إدارة المنتجات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-boxes me-2"></i>
                        إدارة المنتجات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المنتجات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المنتجات</h6>
                                    <h3 class="mb-0"><?php echo e($products->total()); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-boxes fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المنتجات المتاحة</h6>
                                    <h3 class="mb-0"><?php echo e($products->where('total_stock', '>', 0)->count()); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">مخزون منخفض</h6>
                                    <h3 class="mb-0"><?php echo e($products->filter(function($p) { return $p->isLowStock(); })->count()); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">نفد المخزون</h6>
                                    <h3 class="mb-0"><?php echo e($products->filter(function($p) { return $p->isOutOfStock(); })->count()); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('products.index')); ?>">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">البحث</label>
                                    <input type="text" name="search" class="form-control" 
                                           value="<?php echo e(request('search')); ?>" 
                                           placeholder="ابحث بالاسم، الكود، أو الباركود...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">التصنيف</label>
                                    <select name="category" class="form-select">
                                        <option value="">جميع التصنيفات</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>" 
                                                    <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->name_ar); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">الفلتر</label>
                                    <select name="filter" class="form-select">
                                        <option value="">جميع المنتجات</option>
                                        <option value="low_stock" <?php echo e(request('filter') == 'low_stock' ? 'selected' : ''); ?>>
                                            مخزون منخفض
                                        </option>
                                        <option value="out_of_stock" <?php echo e(request('filter') == 'out_of_stock' ? 'selected' : ''); ?>>
                                            نفد المخزون
                                        </option>
                                        <option value="expired" <?php echo e(request('filter') == 'expired' ? 'selected' : ''); ?>>
                                            منتهي الصلاحية
                                        </option>
                                        <option value="near_expiry" <?php echo e(request('filter') == 'near_expiry' ? 'selected' : ''); ?>>
                                            قريب الانتهاء
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المنتجات
                        <span class="badge bg-secondary ms-2"><?php echo e($products->total()); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($products->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="15%">المنتج</th>
                                        <th width="10%">الكود</th>
                                        <th width="10%">التصنيف</th>
                                        <th width="8%">الكمية الإجمالية</th>
                                        <th width="8%">عدد الدفعات</th>
                                        <th width="10%">سعر البيع</th>
                                        <th width="8%">نسبة الربح</th>
                                        <th width="10%">حالة الصلاحية</th>
                                        <th width="8%">حالة المخزون</th>
                                        <th width="8%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($products->firstItem() + $index); ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if($product->image): ?>
                                                        <img src="<?php echo e(asset('storage/' . $product->image)); ?>" 
                                                             alt="<?php echo e($product->name_ar); ?>" 
                                                             class="rounded me-2" 
                                                             style="width: 40px; height: 40px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-box text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo e($product->name_ar); ?></strong>
                                                        <?php if($product->name_en): ?>
                                                            <br><small class="text-muted"><?php echo e($product->name_en); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code><?php echo e($product->sku); ?></code>
                                                <?php if($product->barcode): ?>
                                                    <br><small class="text-muted"><?php echo e($product->barcode); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($product->category): ?>
                                                    <span class="badge bg-info"><?php echo e($product->category->name_ar); ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo e(number_format($product->total_stock, 2)); ?></strong>
                                                <br><small class="text-muted"><?php echo e($product->unit); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo e($product->available_batches_count); ?></span>
                                                <?php if($product->available_batches_count > 0): ?>
                                                    <br><small class="text-muted">دفعة متاحة</small>
                                                <?php else: ?>
                                                    <br><small class="text-muted">لا توجد دفعات</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($product->selling_price): ?>
                                                    <strong><?php echo e(number_format($product->selling_price, 2)); ?> ر.ي</strong>
                                                <?php else: ?>
                                                    <span class="text-muted">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?php echo e($product->profit_margin); ?>%</span>
                                            </td>
                                            <td>
                                                <?php
                                                    $expiryStatus = $product->expiry_status;
                                                ?>
                                                <?php if($expiryStatus === 'expired'): ?>
                                                    <span class="badge bg-danger">منتهي الصلاحية</span>
                                                <?php elseif($expiryStatus === 'near_expiry'): ?>
                                                    <span class="badge bg-warning">قريب الانتهاء</span>
                                                <?php elseif($expiryStatus === 'valid'): ?>
                                                    <span class="badge bg-success">صالح</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">لا ينتهي</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($product->isOutOfStock()): ?>
                                                    <span class="badge bg-danger">نفد المخزون</span>
                                                <?php elseif($product->isLowStock()): ?>
                                                    <span class="badge bg-warning">مخزون منخفض</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">متوفر</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('products.show', $product)); ?>" 
                                                       class="btn btn-sm btn-info" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('products.edit', $product)); ?>" 
                                                       class="btn btn-sm btn-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('products.destroy', $product)); ?>" 
                                                          method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                                title="حذف" 
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                عرض <?php echo e($products->firstItem()); ?> إلى <?php echo e($products->lastItem()); ?> 
                                من أصل <?php echo e($products->total()); ?> منتج
                            </div>
                            <div>
                                <?php echo e($products->links()); ?>

                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات</h5>
                            <p class="text-muted">لم يتم العثور على منتجات مطابقة لمعايير البحث</p>
                            <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    font-weight: 600;
    border-top: none;
}

.badge {
    font-size: 0.75em;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-left: 2px;
}

.table-responsive {
    border-radius: 0.375rem;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/products/index.blade.php ENDPATH**/ ?>