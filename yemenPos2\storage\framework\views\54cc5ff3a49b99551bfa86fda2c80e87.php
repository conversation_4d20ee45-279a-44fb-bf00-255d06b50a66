<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'نظام نقطة المبيعات'); ?></title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .top-navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1030;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }

        .user-dropdown .dropdown-toggle::after {
            display: none;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 76px;
            right: 0;
            height: calc(100vh - 76px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            z-index: 1020;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            width: 70px !important;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            margin: 2px 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.15);
            transform: translateX(-5px);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-left: 10px;
        }

        .sidebar.collapsed .nav-link span {
            display: none;
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: 15px 10px;
        }

        .sidebar.collapsed .nav-link i {
            margin-left: 0;
        }

        /* Main Content */
        .main-content {
            margin-top: 76px;
            margin-right: 250px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            margin-left: 20px;
            padding: 30px;
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 90px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 25px;
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
            border-radius: 10px;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            border-radius: 10px;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        /* Toggle Button */
        .sidebar-toggle {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }

        /* Notification Badge */
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Stats Cards */
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-card .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* أقسام القائمة الجانبية */
        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .nav-section .nav-link {
            padding-right: 2rem;
            font-size: 0.9rem;
        }

        .nav-section .nav-link i {
            width: 20px;
            text-align: center;
            margin-left: 0.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px !important;
            }

            .main-content {
                margin-right: 90px;
            }

            .sidebar .nav-link span {
                display: none;
            }

            .nav-section-title {
                display: none;
            }

            .nav-section .nav-link {
                padding-right: 1rem;
            }
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-expand-lg top-navbar fixed-top">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand" href="<?php echo e(route('dashboard')); ?>">
                <i class="fas fa-cash-register me-2"></i>
                نظام نقطة المبيعات
            </a>

            <!-- Sidebar Toggle -->
            <button class="sidebar-toggle d-lg-none" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>

            <!-- Navbar Items -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('pos.index')); ?>">
                            <i class="fas fa-plus me-1"></i>
                            فاتورة جديدة
                        </a>
                    </li>
                    <li class="nav-item position-relative">
                        <a class="nav-link" href="#">
                            <i class="fas fa-bell"></i>
                            <?php if($lowStockProducts ?? 0 > 0): ?>
                                <span class="notification-badge"><?php echo e($lowStockProducts ?? 0); ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>

                <!-- User Dropdown -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown user-dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2">
                                <?php echo e(substr(auth()->user()->name ?? 'U', 0, 1)); ?>

                            </div>
                            <span><?php echo e(auth()->user()->name ?? 'المستخدم'); ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar" style="width: 250px;">
        <div class="p-3 text-center">
            <button class="sidebar-toggle d-none d-lg-block" type="button" id="sidebarCollapseBtn">
                <i class="fas fa-angle-double-right" id="collapseIcon"></i>
            </button>
        </div>

        <nav class="nav flex-column">
            <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>

            <!-- قسم نقطة البيع والمبيعات -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="fas fa-cash-register me-2"></i>
                    نقطة البيع
                </div>

                <a class="nav-link <?php echo e(request()->routeIs('pos.*') ? 'active' : ''); ?>" href="<?php echo e(route('pos.index')); ?>">
                    <i class="fas fa-plus-circle text-primary"></i>
                    <span>إنشاء فاتورة</span>
                </a>

                <a class="nav-link <?php echo e(request()->routeIs('sales.index') ? 'active' : ''); ?>" href="<?php echo e(route('sales.index')); ?>">
                    <i class="fas fa-list"></i>
                    <span>جميع المبيعات</span>
                </a>

                <a class="nav-link <?php echo e(request()->routeIs('sales.unposted') ? 'active' : ''); ?>" href="<?php echo e(route('sales.unposted')); ?>">
                    <i class="fas fa-clock text-warning"></i>
                    <span>الفواتير غير المرحلة</span>
                </a>

                <a class="nav-link <?php echo e(request()->routeIs('sales.posted') ? 'active' : ''); ?>" href="<?php echo e(route('sales.posted')); ?>">
                    <i class="fas fa-check-circle text-success"></i>
                    <span>الفواتير المرحلة</span>
                </a>

                <a class="nav-link <?php echo e(request()->routeIs('sales.refunded') ? 'active' : ''); ?>" href="<?php echo e(route('sales.refunded')); ?>">
                    <i class="fas fa-undo text-secondary"></i>
                    <span>الفواتير المرتجعة</span>
                </a>

                <a class="nav-link <?php echo e(request()->routeIs('sales.debts') ? 'active' : ''); ?>" href="<?php echo e(route('sales.debts')); ?>">
                    <i class="fas fa-money-bill-wave text-danger"></i>
                    <span>المديونيات</span>
                </a>

                <a class="nav-link <?php echo e(request()->routeIs('debts.*') ? 'active' : ''); ?>" href="<?php echo e(route('debts.index')); ?>">
                    <i class="fas fa-credit-card text-warning"></i>
                    <span>إدارة المديونيات</span>
                </a>

                <a class="nav-link <?php echo e(request()->routeIs('statements.*') ? 'active' : ''); ?>" href="<?php echo e(route('statements.index')); ?>">
                    <i class="fas fa-file-invoice-dollar text-info"></i>
                    <span>كشف حساب العملاء</span>
                </a>
            </div>

            <a class="nav-link <?php echo e(request()->routeIs('categories.*') ? 'active' : ''); ?>" href="<?php echo e(route('categories.index')); ?>">
                <i class="fas fa-tags"></i>
                <span>التصنيفات</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('units.*') ? 'active' : ''); ?>" href="<?php echo e(route('units.index')); ?>">
                <i class="fas fa-balance-scale"></i>
                <span>الوحدات</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('products.*') ? 'active' : ''); ?>" href="<?php echo e(route('products.index')); ?>">
                <i class="fas fa-box"></i>
                <span>المنتجات</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('customers.*') ? 'active' : ''); ?>" href="<?php echo e(route('customers.index')); ?>">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('suppliers.*') ? 'active' : ''); ?>" href="<?php echo e(route('suppliers.index')); ?>">
                <i class="fas fa-truck"></i>
                <span>الموردين</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('purchases.*') ? 'active' : ''); ?>" href="<?php echo e(route('purchases.index')); ?>">
                <i class="fas fa-shopping-bag"></i>
                <span>المشتريات</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('payments.*') ? 'active' : ''); ?>" href="<?php echo e(route('payments.index')); ?>">
                <i class="fas fa-credit-card"></i>
                <span>المدفوعات</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('reports.*') ? 'active' : ''); ?>" href="<?php echo e(route('reports.index')); ?>">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('users.*') ? 'active' : ''); ?>" href="<?php echo e(route('users.index')); ?>">
                <i class="fas fa-users"></i>
                <span>المستخدمين</span>
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('settings.*') ? 'active' : ''); ?>" href="<?php echo e(route('settings.index')); ?>">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0"><?php echo $__env->yieldContent('page-title', 'لوحة التحكم'); ?></h2>
            <div class="d-flex align-items-center">
                <div class="text-end me-3">
                    <div class="fw-bold text-primary" id="hijri-date"><?php echo e(now()->format('Y-m-d')); ?></div>
                    <small class="text-muted" id="gregorian-date"><?php echo e(now()->format('l, F j, Y')); ?></small>
                </div>
                <div class="badge bg-success" id="live-clock"><?php echo e(now()->format('H:i:s')); ?></div>
            </div>
        </div>

                    <!-- Alerts -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

        <!-- Page Content -->
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Sidebar Toggle Script -->
    <script>
        $(document).ready(function() {
            // Sidebar collapse functionality
            $('#sidebarCollapseBtn').on('click', function() {
                const sidebar = $('#sidebar');
                const mainContent = $('#mainContent');
                const collapseIcon = $('#collapseIcon');

                sidebar.toggleClass('collapsed');
                mainContent.toggleClass('expanded');

                if (sidebar.hasClass('collapsed')) {
                    collapseIcon.removeClass('fa-angle-double-right').addClass('fa-angle-double-left');
                } else {
                    collapseIcon.removeClass('fa-angle-double-left').addClass('fa-angle-double-right');
                }

                // Save state in localStorage
                localStorage.setItem('sidebarCollapsed', sidebar.hasClass('collapsed'));
            });

            // Restore sidebar state from localStorage
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                $('#sidebar').addClass('collapsed');
                $('#mainContent').addClass('expanded');
                $('#collapseIcon').removeClass('fa-angle-double-right').addClass('fa-angle-double-left');
            }

            // حفظ واستعادة موضع السكرول في السايد بار
            function saveSidebarScrollPosition() {
                const scrollPosition = $('#sidebar').scrollTop();
                localStorage.setItem('sidebarScrollPosition', scrollPosition);
            }

            function restoreSidebarScrollPosition() {
                const scrollPosition = localStorage.getItem('sidebarScrollPosition');
                if (scrollPosition !== null) {
                    $('#sidebar').scrollTop(parseInt(scrollPosition));
                }
            }

            // استعادة موضع السكرول عند تحميل الصفحة
            restoreSidebarScrollPosition();

            // حفظ موضع السكرول عند التمرير في السايد بار
            $('#sidebar').on('scroll', function() {
                saveSidebarScrollPosition();
            });

            // حفظ موضع السكرول عند النقر على روابط السايد بار
            $('#sidebar a.nav-link').on('click', function() {
                saveSidebarScrollPosition();
            });

            // حفظ موضع السكرول عند النقر على أي رابط في الصفحة
            $('a[href]').not('[href^="#"]').not('[href^="javascript:"]').on('click', function() {
                saveSidebarScrollPosition();
            });

            // Mobile sidebar toggle
            $('#sidebarToggle').on('click', function() {
                $('#sidebar').toggleClass('show');
            });

            // Close sidebar when clicking outside on mobile
            $(document).on('click', function(e) {
                if ($(window).width() < 992) {
                    if (!$(e.target).closest('#sidebar, #sidebarToggle').length) {
                        $('#sidebar').removeClass('show');
                    }
                }
            });

            // Auto-hide alerts after 5 seconds
            $('.alert').delay(5000).fadeOut();

            // Add loading state to buttons
            $('form').on('submit', function() {
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
            });

            // Real-time clock update (only for header)
            function updateClock() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                // Update only the live clock in header
                $('#live-clock').text(timeString);

                // Update dates (static - no seconds)
                const hijriDate = now.toLocaleDateString('ar-SA-u-ca-islamic');
                const gregorianDate = now.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                $('#hijri-date').text(hijriDate);
                $('#gregorian-date').text(gregorianDate);
            }

            // Update clock every second (only for header)
            setInterval(updateClock, 1000);

            // Initial update
            updateClock();
        });

        // Add smooth scrolling to all links
        $('a[href^="#"]').on('click', function(event) {
            var target = $(this.getAttribute('href'));
            if( target.length ) {
                event.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 100
                }, 1000);
            }
        });

        // Add ripple effect to buttons
        $('.btn').on('click', function(e) {
            const button = $(this);
            const ripple = $('<span class="ripple"></span>');

            button.append(ripple);

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            }).addClass('animate');

            setTimeout(() => ripple.remove(), 600);
        });
    </script>

    <!-- Additional CSS for ripple effect -->
    <style>
        .btn {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            pointer-events: none;
        }

        .ripple.animate {
            animation: ripple-animation 0.6s linear;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Mobile sidebar styles */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }
        }
    </style>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/layouts/app.blade.php ENDPATH**/ ?>