<?php $__env->startSection('title', 'التقارير'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">التقارير</li>
                        </ol>
                    </nav>
                </div>
            </div>

            <!-- Reports Grid -->
            <div class="row">
                <!-- تقرير المبيعات -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 report-card">
                        <div class="card-body text-center">
                            <div class="report-icon mb-3">
                                <i class="fas fa-chart-line fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">تقرير المبيعات</h5>
                            <p class="card-text text-muted">
                                تقرير شامل عن المبيعات والإيرادات مع إمكانية الفلترة حسب التاريخ والعميل
                            </p>
                            <a href="<?php echo e(route('reports.sales')); ?>" class="btn btn-primary">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقرير المشتريات -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 report-card">
                        <div class="card-body text-center">
                            <div class="report-icon mb-3">
                                <i class="fas fa-shopping-bag fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">تقرير المشتريات</h5>
                            <p class="card-text text-muted">
                                تقرير تفصيلي عن المشتريات والموردين مع إحصائيات شاملة
                            </p>
                            <a href="<?php echo e(route('reports.purchases')); ?>" class="btn btn-success">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقرير المخزون -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 report-card">
                        <div class="card-body text-center">
                            <div class="report-icon mb-3">
                                <i class="fas fa-boxes fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">تقرير المخزون</h5>
                            <p class="card-text text-muted">
                                حالة المخزون الحالية والمنتجات منخفضة المخزون
                            </p>
                            <a href="<?php echo e(route('reports.inventory')); ?>" class="btn btn-info">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقرير الأرباح والخسائر -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 report-card">
                        <div class="card-body text-center">
                            <div class="report-icon mb-3">
                                <i class="fas fa-calculator fa-3x text-warning"></i>
                            </div>
                            <h5 class="card-title">الأرباح والخسائر</h5>
                            <p class="card-text text-muted">
                                تحليل مالي شامل للأرباح والخسائر وهامش الربح
                            </p>
                            <a href="<?php echo e(route('reports.profit-loss')); ?>" class="btn btn-warning">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقرير العملاء -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 report-card">
                        <div class="card-body text-center">
                            <div class="report-icon mb-3">
                                <i class="fas fa-users fa-3x text-purple"></i>
                            </div>
                            <h5 class="card-title">تقرير العملاء</h5>
                            <p class="card-text text-muted">
                                إحصائيات العملاء ومبيعاتهم والمديونيات
                            </p>
                            <a href="<?php echo e(route('reports.customers')); ?>" class="btn btn-purple">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقرير المديونيات -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 report-card">
                        <div class="card-body text-center">
                            <div class="report-icon mb-3">
                                <i class="fas fa-money-bill-wave fa-3x text-danger"></i>
                            </div>
                            <h5 class="card-title">تقرير المديونيات</h5>
                            <p class="card-text text-muted">
                                المديونيات المستحقة والمدفوعات الجزئية
                            </p>
                            <a href="<?php echo e(route('sales.debts')); ?>" class="btn btn-danger">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                إحصائيات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="quick-stat">
                                        <i class="fas fa-chart-line fa-2x text-primary mb-2"></i>
                                        <h4 class="text-primary"><?php echo e(number_format(\App\Models\Sale::whereDate('sale_date', today())->sum('total_amount'), 2)); ?></h4>
                                        <p class="text-muted mb-0">مبيعات اليوم (ر.ي)</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="quick-stat">
                                        <i class="fas fa-shopping-bag fa-2x text-success mb-2"></i>
                                        <h4 class="text-success"><?php echo e(number_format(\App\Models\Purchase::whereDate('purchase_date', today())->sum('total_amount'), 2)); ?></h4>
                                        <p class="text-muted mb-0">مشتريات اليوم (ر.ي)</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="quick-stat">
                                        <i class="fas fa-boxes fa-2x text-info mb-2"></i>
                                        <h4 class="text-info"><?php echo e(\App\Models\Product::sum('stock')); ?></h4>
                                        <p class="text-muted mb-0">إجمالي المخزون</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="quick-stat">
                                        <i class="fas fa-money-bill-wave fa-2x text-warning mb-2"></i>
                                        <h4 class="text-warning"><?php echo e(number_format(\App\Models\Sale::sum('remaining_amount'), 2)); ?></h4>
                                        <p class="text-muted mb-0">إجمالي المديونيات (ر.ي)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export Options -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-download me-2"></i>
                                تصدير التقارير
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="export-option">
                                        <i class="fas fa-file-excel fa-2x text-success mb-2"></i>
                                        <h6>تصدير Excel</h6>
                                        <p class="text-muted small">تصدير التقارير بصيغة Excel للتحليل المتقدم</p>
                                        <button class="btn btn-success btn-sm" onclick="exportExcel()">
                                            <i class="fas fa-download me-2"></i>تصدير
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="export-option">
                                        <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                        <h6>تصدير PDF</h6>
                                        <p class="text-muted small">تصدير التقارير بصيغة PDF للطباعة</p>
                                        <button class="btn btn-danger btn-sm" onclick="exportPDF()">
                                            <i class="fas fa-download me-2"></i>تصدير
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="export-option">
                                        <i class="fas fa-print fa-2x text-primary mb-2"></i>
                                        <h6>طباعة مباشرة</h6>
                                        <p class="text-muted small">طباعة التقارير مباشرة من المتصفح</p>
                                        <button class="btn btn-primary btn-sm" onclick="printReports()">
                                            <i class="fas fa-print me-2"></i>طباعة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.report-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.report-icon {
    transition: transform 0.3s ease;
}

.report-card:hover .report-icon {
    transform: scale(1.1);
}

.quick-stat {
    padding: 20px;
    border-radius: 10px;
    background: #f8f9fa;
    margin-bottom: 20px;
}

.export-option {
    text-align: center;
    padding: 20px;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    transition: border-color 0.3s ease;
}

.export-option:hover {
    border-color: #007bff;
}

.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-purple:hover {
    background-color: #5a32a3;
    border-color: #5a32a3;
    color: white;
}

.text-purple {
    color: #6f42c1 !important;
}
</style>

<script>
function exportExcel() {
    alert('ميزة تصدير Excel قيد التطوير');
}

function exportPDF() {
    alert('ميزة تصدير PDF قيد التطوير');
}

function printReports() {
    window.print();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/reports/index.blade.php ENDPATH**/ ?>