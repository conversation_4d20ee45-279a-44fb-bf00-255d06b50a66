<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\User;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // إحصائيات اليوم (فقط الفواتير المرحلة)
        $today = Carbon::today();
        $todaySales = Sale::whereDate('sale_date', $today)->where('is_posted', true)->sum('total_amount');
        $todayTransactions = Sale::whereDate('sale_date', $today)->where('is_posted', true)->count();

        // إحصائيات الشهر (فقط الفواتير المرحلة)
        $thisMonth = Carbon::now()->startOfMonth();
        $monthlySales = Sale::where('sale_date', '>=', $thisMonth)->where('is_posted', true)->sum('total_amount');
        $monthlyTransactions = Sale::where('sale_date', '>=', $thisMonth)->where('is_posted', true)->count();

        // إحصائيات الفواتير غير المرحلة (استبعاد المرتجعة)
        $unpostedSales = Sale::where('is_posted', false)->where('status', '!=', 'مسترجعة')->sum('total_amount');
        $unpostedCount = Sale::where('is_posted', false)->where('status', '!=', 'مسترجعة')->count();
        $todayUnposted = Sale::whereDate('sale_date', $today)->where('is_posted', false)->where('status', '!=', 'مسترجعة')->count();

        // إحصائيات الفواتير المرتجعة
        $refundedSales = Sale::where('status', 'مسترجعة')->sum('total_amount');
        $refundedCount = Sale::where('status', 'مسترجعة')->count();
        $todayRefunded = Sale::whereDate('updated_at', $today)->where('status', 'مسترجعة')->count();

        // إحصائيات طرق الدفع (بناءً على المبالغ الفعلية - فقط المرحلة)

        // النقدي: المبلغ المدفوع نقداً فقط (الذي دخل الصندوق)
        $cashSales = Sale::where('is_posted', true)
            ->where('payment_method', 'نقدي')
            ->sum('paid_amount');

        // البنك: المبلغ المدفوع عبر البنك فقط
        $bankSales = Sale::where('is_posted', true)
            ->whereIn('payment_method', ['بطاقة ائتمان', 'تحويل بنكي'])
            ->sum('paid_amount');

        // الآجل: مجموع المبالغ المتبقية من جميع الفواتير + الفواتير الآجلة الكاملة
        $creditSales = Sale::where('is_posted', true)
            ->where('payment_method', 'آجل')
            ->sum('total_amount') +
            Sale::where('is_posted', true)
            ->where('payment_method', '!=', 'آجل')
            ->sum('remaining_amount');

        // إحصائيات يومية لطرق الدفع
        $todayCashSales = Sale::whereDate('sale_date', $today)
            ->where('is_posted', true)
            ->where('payment_method', 'نقدي')
            ->sum('paid_amount');

        $todayBankSales = Sale::whereDate('sale_date', $today)
            ->where('is_posted', true)
            ->whereIn('payment_method', ['بطاقة ائتمان', 'تحويل بنكي'])
            ->sum('paid_amount');

        $todayCreditSales = Sale::whereDate('sale_date', $today)
            ->where('is_posted', true)
            ->where('payment_method', 'آجل')
            ->sum('total_amount') +
            Sale::whereDate('sale_date', $today)
            ->where('is_posted', true)
            ->where('payment_method', '!=', 'آجل')
            ->sum('remaining_amount');

        // إحصائيات عامة
        $totalProducts = Product::where('is_active', true)->count();
        $totalCustomers = Customer::where('is_active', true)->count();
        $lowStockProducts = Product::where('track_stock', true)
            ->whereRaw('stock_quantity <= min_stock_level')
            ->count();

        // أحدث المبيعات (جميع الفواتير مع حالة الترحيل)
        $recentSales = Sale::with(['customer', 'user'])
            ->latest('sale_date')
            ->latest('sale_time')
            ->take(5)
            ->get();

        // المنتجات الأكثر مبيعاً (فقط من الفواتير المرحلة)
        $topProducts = Product::withCount(['saleItems as total_sold' => function($query) {
                $query->selectRaw('sum(quantity)')
                      ->whereHas('sale', function($saleQuery) {
                          $saleQuery->where('is_posted', true);
                      });
            }])
            ->orderBy('total_sold', 'desc')
            ->take(5)
            ->get();

        // مبيعات آخر 7 أيام للرسم البياني (فقط المرحلة)
        $salesChart = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::today()->subDays($i);
            $salesChart[] = [
                'date' => $date->format('Y-m-d'),
                'amount' => Sale::whereDate('sale_date', $date)->where('is_posted', true)->sum('total_amount')
            ];
        }

        return view('dashboard', compact(
            'todaySales',
            'todayTransactions',
            'monthlySales',
            'monthlyTransactions',
            'unpostedSales',
            'unpostedCount',
            'todayUnposted',
            'refundedSales',
            'refundedCount',
            'todayRefunded',
            'cashSales',
            'creditSales',
            'bankSales',
            'todayCashSales',
            'todayCreditSales',
            'todayBankSales',
            'totalProducts',
            'totalCustomers',
            'lowStockProducts',
            'recentSales',
            'topProducts',
            'salesChart'
        ));
    }
}
