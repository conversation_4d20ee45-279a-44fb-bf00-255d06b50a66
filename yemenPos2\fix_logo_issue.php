<?php

/**
 * ملف لحل مشكلة رفع الشعار
 * يجب تشغيل هذا الملف من خلال سطر الأوامر
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Artisan;

echo "بدء حل مشكلة رفع الشعار...\n";

// 1. تشغيل migration لتحديث نوع الحقل
echo "1. تشغيل migration لتحديث نوع حقل الشعار...\n";
try {
    Artisan::call('migrate', ['--force' => true]);
    echo "✅ تم تشغيل migration بنجاح\n";
} catch (Exception $e) {
    echo "❌ خطأ في تشغيل migration: " . $e->getMessage() . "\n";
}

// 2. إنشاء symbolic link للتخزين
echo "2. إنشاء symbolic link للتخزين...\n";
try {
    Artisan::call('storage:link');
    echo "✅ تم إنشاء symbolic link بنجاح\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء symbolic link: " . $e->getMessage() . "\n";
}

// 3. التأكد من وجود مجلدات التخزين
echo "3. التأكد من وجود مجلدات التخزين...\n";
$directories = [
    storage_path('app/public'),
    storage_path('app/public/settings'),
    public_path('storage')
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ تم إنشاء مجلد: $dir\n";
    } else {
        echo "✅ المجلد موجود: $dir\n";
    }
}

// 4. تحديث صلاحيات المجلدات
echo "4. تحديث صلاحيات المجلدات...\n";
try {
    chmod(storage_path('app/public'), 0755);
    chmod(storage_path('app/public/settings'), 0755);
    echo "✅ تم تحديث صلاحيات المجلدات\n";
} catch (Exception $e) {
    echo "❌ خطأ في تحديث الصلاحيات: " . $e->getMessage() . "\n";
}

echo "\n🎉 تم الانتهاء من حل مشكلة رفع الشعار!\n";
echo "يمكنك الآن:\n";
echo "1. الذهاب إلى الإعدادات\n";
echo "2. اختيار شعار الشركة\n";
echo "3. رفع صورة الشعار\n";
echo "4. حفظ الإعدادات\n";
echo "5. سيظهر الشعار في جميع الفواتير\n";
