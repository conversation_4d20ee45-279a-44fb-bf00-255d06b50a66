<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تحديث الحالة من "جزئي" إلى "دفع جزئي"
        DB::table('sales')
            ->where('status', 'جزئي')
            ->update(['status' => 'دفع جزئي']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // العكس: تحديث من "دفع جزئي" إلى "جزئي"
        DB::table('sales')
            ->where('status', 'دفع جزئي')
            ->update(['status' => 'جزئي']);
    }
};
