<?php

namespace App\Services;

class PdfService
{
    protected $html;
    protected $filename;

    public function __construct()
    {
        // لا نحتاج مكتبات خارجية
    }

    public function loadHTML($html)
    {
        $this->html = $html;
        return $this;
    }

    public function loadView($view, $data = [])
    {
        $this->html = view($view, $data)->render();
        return $this;
    }

    public function setPaper($size = 'A4', $orientation = 'portrait')
    {
        // للتوافق مع الكود الموجود
        return $this;
    }

    public function render()
    {
        // للتوافق مع الكود الموجود
        return $this;
    }

    public function output()
    {
        return $this->html;
    }

    public function download($filename = 'document.pdf')
    {
        // إنشاء HTML منسق للطباعة
        $printableHtml = $this->generatePrintableHtml();

        return response($printableHtml, 200, [
            'Content-Type' => 'text/html; charset=utf-8',
            'Content-Disposition' => 'inline; filename="' . str_replace('.pdf', '.html', $filename) . '"',
        ]);
    }

    public function save($path)
    {
        // حفظ HTML بدلاً من PDF
        $printableHtml = $this->generatePrintableHtml();
        $htmlPath = str_replace('.pdf', '.html', $path);
        file_put_contents($htmlPath, $printableHtml);
        return $this;
    }

    public function stream($filename = 'document.pdf')
    {
        return $this->download($filename);
    }

    private function generatePrintableHtml()
    {
        return '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشف الحساب</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            font-size: 14px;
            line-height: 1.4;
        }
        .print-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 16px;
        }
        .print-button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .summary-cards {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 10px;
        }
        .summary-card {
            flex: 1;
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #ddd;
            text-align: center;
            border-radius: 5px;
        }
        .summary-card h4 {
            margin: 0 0 5px 0;
            color: #007bff;
        }
        .text-success { color: #28a745; }
        .text-danger { color: #dc3545; }
        .text-warning { color: #ffc107; }
    </style>
    <script>
        function printPage() {
            window.print();
        }
    </script>
</head>
<body>
    <div class="no-print">
        <button class="print-button" onclick="printPage()">🖨️ طباعة كشف الحساب</button>
    </div>
    ' . $this->html . '
</body>
</html>';
    }
}
