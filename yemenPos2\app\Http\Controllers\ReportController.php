<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Purchase;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * عرض صفحة التقارير الرئيسية
     */
    public function index()
    {
        return view('reports.index');
    }

    /**
     * تقرير المبيعات
     */
    public function sales(Request $request)
    {
        $dateFrom = $request->date_from ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? Carbon::now()->format('Y-m-d');
        $customerId = $request->customer_id;
        $status = $request->status;

        $query = Sale::with(['customer', 'user'])
            ->whereBetween('sale_date', [$dateFrom, $dateTo]);

        if ($customerId) {
            $query->where('customer_id', $customerId);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $sales = $query->orderBy('sale_date', 'desc')->get();

        // إحصائيات
        $totalSales = $sales->sum('total_amount');
        $totalTransactions = $sales->count();
        $averageTransaction = $totalTransactions > 0 ? $totalSales / $totalTransactions : 0;
        $totalPaid = $sales->sum('paid_amount');
        $totalRemaining = $sales->sum('remaining_amount');

        // مبيعات يومية
        $dailySales = $sales->groupBy(function($sale) {
            return $sale->sale_date->format('Y-m-d');
        })->map(function($daySales) {
            return [
                'date' => $daySales->first()->sale_date->format('Y-m-d'),
                'total' => $daySales->sum('total_amount'),
                'count' => $daySales->count()
            ];
        })->values();

        $customers = Customer::where('is_active', true)->get();

        return view('reports.sales', compact(
            'sales', 'totalSales', 'totalTransactions', 'averageTransaction',
            'totalPaid', 'totalRemaining', 'dailySales', 'customers',
            'dateFrom', 'dateTo', 'customerId', 'status'
        ));
    }

    /**
     * تقرير المشتريات
     */
    public function purchases(Request $request)
    {
        $dateFrom = $request->date_from ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? Carbon::now()->format('Y-m-d');
        $supplierId = $request->supplier_id;

        $query = Purchase::with(['supplier', 'user'])
            ->whereBetween('purchase_date', [$dateFrom, $dateTo]);

        if ($supplierId) {
            $query->where('supplier_id', $supplierId);
        }

        $purchases = $query->orderBy('purchase_date', 'desc')->get();

        // إحصائيات
        $totalPurchases = $purchases->sum('total_amount');
        $totalTransactions = $purchases->count();
        $averageTransaction = $totalTransactions > 0 ? $totalPurchases / $totalTransactions : 0;

        // مشتريات يومية
        $dailyPurchases = $purchases->groupBy(function($purchase) {
            return $purchase->purchase_date->format('Y-m-d');
        })->map(function($dayPurchases) {
            return [
                'date' => $dayPurchases->first()->purchase_date->format('Y-m-d'),
                'total' => $dayPurchases->sum('total_amount'),
                'count' => $dayPurchases->count()
            ];
        })->values();

        $suppliers = Supplier::where('is_active', true)->get();

        return view('reports.purchases', compact(
            'purchases', 'totalPurchases', 'totalTransactions', 'averageTransaction',
            'dailyPurchases', 'suppliers', 'dateFrom', 'dateTo', 'supplierId'
        ));
    }

    /**
     * تقرير المخزون
     */
    public function inventory(Request $request)
    {
        $categoryId = $request->category_id;
        $lowStock = $request->low_stock;

        $query = Product::with(['category', 'unit']);

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        if ($lowStock) {
            $query->where('stock', '<=', DB::raw('min_stock'));
        }

        $products = $query->orderBy('name')->get();

        // إحصائيات
        $totalProducts = $products->count();
        $totalStock = $products->sum('stock');
        $totalValue = $products->sum(function($product) {
            return $product->stock * $product->cost_price;
        });
        $lowStockCount = $products->where('stock', '<=', function($product) {
            return $product->min_stock;
        })->count();

        $categories = \App\Models\Category::where('is_active', true)->get();

        return view('reports.inventory', compact(
            'products', 'totalProducts', 'totalStock', 'totalValue',
            'lowStockCount', 'categories', 'categoryId', 'lowStock'
        ));
    }

    /**
     * تقرير الأرباح والخسائر
     */
    public function profitLoss(Request $request)
    {
        $dateFrom = $request->date_from ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? Carbon::now()->format('Y-m-d');

        // المبيعات
        $sales = Sale::whereBetween('sale_date', [$dateFrom, $dateTo])
            ->where('status', 'مكتملة')
            ->get();

        $totalRevenue = $sales->sum('total_amount');
        $totalDiscount = $sales->sum('discount_amount');

        // تكلفة البضاعة المباعة
        $costOfGoodsSold = 0;
        foreach ($sales as $sale) {
            foreach ($sale->saleItems as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $costOfGoodsSold += $item->quantity * $product->cost_price;
                }
            }
        }

        // المشتريات
        $purchases = Purchase::whereBetween('purchase_date', [$dateFrom, $dateTo])->get();
        $totalPurchases = $purchases->sum('total_amount');

        // الحسابات
        $grossProfit = $totalRevenue - $costOfGoodsSold;
        $netProfit = $grossProfit - $totalDiscount;
        $profitMargin = $totalRevenue > 0 ? ($netProfit / $totalRevenue) * 100 : 0;

        return view('reports.profit-loss', compact(
            'totalRevenue', 'totalDiscount', 'costOfGoodsSold', 'totalPurchases',
            'grossProfit', 'netProfit', 'profitMargin', 'dateFrom', 'dateTo'
        ));
    }

    /**
     * تقرير العملاء
     */
    public function customers(Request $request)
    {
        $dateFrom = $request->date_from ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? Carbon::now()->format('Y-m-d');

        $customers = Customer::with(['sales' => function($query) use ($dateFrom, $dateTo) {
            $query->whereBetween('sale_date', [$dateFrom, $dateTo]);
        }])->get();

        $customersData = $customers->map(function($customer) {
            $totalSales = $customer->sales->sum('total_amount');
            $totalTransactions = $customer->sales->count();
            $totalDebt = $customer->sales->sum('remaining_amount');

            return [
                'customer' => $customer,
                'total_sales' => $totalSales,
                'total_transactions' => $totalTransactions,
                'total_debt' => $totalDebt,
                'average_transaction' => $totalTransactions > 0 ? $totalSales / $totalTransactions : 0
            ];
        })->sortByDesc('total_sales');

        return view('reports.customers', compact('customersData', 'dateFrom', 'dateTo'));
    }
}
