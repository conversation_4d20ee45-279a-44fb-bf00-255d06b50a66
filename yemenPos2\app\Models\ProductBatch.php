<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ProductBatch extends Model
{
    protected $fillable = [
        'product_id',
        'supplier_id',
        'purchase_id',
        'batch_number',
        'original_quantity',
        'remaining_quantity',
        'expiry_date',
        'production_date',
        'cost_price',
        'status',
        'notes'
    ];

    protected $casts = [
        'expiry_date' => 'date',
        'production_date' => 'date',
        'original_quantity' => 'decimal:2',
        'remaining_quantity' => 'decimal:2',
        'cost_price' => 'decimal:2',
    ];

    /**
     * العلاقة مع المنتج
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * العلاقة مع المورد
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * العلاقة مع المشترى
     */
    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    /**
     * التحقق من انتهاء الصلاحية
     */
    public function isExpired(): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return Carbon::now()->gt($this->expiry_date);
    }

    /**
     * التحقق من قرب انتهاء الصلاحية
     */
    public function isNearExpiry(int $days = 30): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return Carbon::now()->addDays($days)->gte($this->expiry_date) && !$this->isExpired();
    }

    /**
     * الحصول على الأيام المتبقية لانتهاء الصلاحية
     */
    public function getDaysToExpiry(): ?int
    {
        if (!$this->expiry_date) {
            return null;
        }

        return Carbon::now()->diffInDays($this->expiry_date, false);
    }

    /**
     * تحديث حالة الدفعة بناءً على تاريخ انتهاء الصلاحية
     */
    public function updateExpiryStatus(): void
    {
        if (!$this->expiry_date) {
            return;
        }

        if ($this->isExpired()) {
            $this->update(['status' => 'expired']);
        } elseif ($this->remaining_quantity <= 0) {
            $this->update(['status' => 'depleted']);
        } else {
            $this->update(['status' => 'active']);
        }
    }

    /**
     * تقليل الكمية المتبقية
     */
    public function reduceQuantity(float $quantity): bool
    {
        if ($this->remaining_quantity >= $quantity) {
            $this->remaining_quantity -= $quantity;

            if ($this->remaining_quantity <= 0) {
                $this->status = 'depleted';
            }

            $this->save();
            return true;
        }

        return false;
    }

    /**
     * إنشاء رقم دفعة تلقائي
     */
    public static function generateBatchNumber(int $productId): string
    {
        $product = Product::find($productId);
        $prefix = $product ? strtoupper(substr($product->sku, 0, 3)) : 'PRD';
        $date = Carbon::now()->format('Ymd');
        $sequence = static::where('batch_number', 'like', "{$prefix}-{$date}-%")->count() + 1;

        return "{$prefix}-{$date}-" . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }
}
