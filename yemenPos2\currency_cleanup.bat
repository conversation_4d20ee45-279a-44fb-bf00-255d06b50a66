@echo off
echo Currency Settings Cleanup...
echo.

echo 1. Running migration to remove exchange rates...
php artisan migrate --force
if %errorlevel% neq 0 (
    echo Error running migration
    pause
    exit /b 1
)
echo Success: Exchange rates removed
echo.

echo 2. Clearing cache...
php artisan cache:clear
php artisan config:clear
php artisan view:clear
echo Success: Cache cleared
echo.

echo 3. Checking current settings...
php check_currency_settings.php
echo.

echo Cleanup completed successfully!
echo.
echo Current settings:
echo - Default currency: Yemeni Rial (YER)
echo - Currency symbol: ر.ي
echo - Currency position: after
echo - Removed: Egyptian Pound exchange rate
echo - Removed: US Dollar exchange rate
echo - Removed: Saudi Riyal exchange rate
echo - Removed: Auto update exchange rates
echo.
pause
