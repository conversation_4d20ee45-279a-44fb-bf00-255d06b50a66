@extends('layouts.app')

@section('title', 'إدارة المديونيات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-money-bill-wave text-warning me-2"></i>
                        إدارة المديونيات
                    </h2>
                    <p class="text-muted mb-0">دفع مديونيات العملاء بطرق مختلفة</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- قائمة العملاء -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        العملاء الذين لديهم مديونيات
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        @forelse($customers as $customer)
                        <a href="{{ route('debts.index', ['customer_id' => $customer->id]) }}" 
                           class="list-group-item list-group-item-action {{ $selectedCustomer && $selectedCustomer->id == $customer->id ? 'active' : '' }}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $customer->name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-phone me-1"></i>{{ $customer->phone ?? 'لا يوجد' }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <div class="badge bg-danger fs-6">
                                        {{ number_format($customer->total_remaining, 2) }} ر.ي
                                    </div>
                                    <small class="text-muted d-block">
                                        {{ $customer->total_debts_count }} فاتورة
                                    </small>
                                </div>
                            </div>
                        </a>
                        @empty
                        <div class="list-group-item text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                            <h5>لا توجد مديونيات</h5>
                            <p class="mb-0">جميع العملاء قاموا بسداد مديونياتهم</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل مديونيات العميل المختار -->
        <div class="col-md-8">
            @if($selectedCustomer)
            <div class="card">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            مديونيات العميل: {{ $selectedCustomer->name }}
                        </h5>
                        <div class="badge bg-light text-dark fs-6">
                            إجمالي المديونيات: {{ number_format($totalCustomerDebts, 2) }} ر.ي
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($customerDebts->count() > 0)
                    <!-- جدول المديونيات -->
                    <div class="table-responsive mb-4">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>الإجمالي</th>
                                    <th>المدفوع</th>
                                    <th>المتبقي</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($customerDebts as $debt)
                                <tr>
                                    <td>
                                        <strong>{{ $debt->invoice_number }}</strong>
                                    </td>
                                    <td>{{ $debt->sale_date->format('Y-m-d') }}</td>
                                    <td>{{ number_format($debt->total_amount, 2) }} ر.ي</td>
                                    <td class="text-success">{{ number_format($debt->paid_amount ?? 0, 2) }} ر.ي</td>
                                    <td class="text-danger fw-bold">{{ number_format($debt->remaining_amount, 2) }} ر.ي</td>
                                    <td>
                                        @if($debt->status === 'غير مدفوعة')
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        @elseif($debt->status === 'دفع جزئي')
                                            <span class="badge bg-warning">دفع جزئي</span>
                                        @else
                                            <span class="badge bg-success">مكتملة</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- نموذج الدفع -->
                    <div class="card bg-light">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                دفع المديونيات
                            </h6>
                        </div>
                        <div class="card-body">
                            <form id="paymentForm">
                                <input type="hidden" name="customer_id" value="{{ $selectedCustomer->id }}">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="payment_amount" 
                                                       step="0.01" min="0.01" max="{{ $totalCustomerDebts }}" 
                                                       placeholder="0.00" required>
                                                <span class="input-group-text">ر.ي</span>
                                            </div>
                                            <div class="form-text">
                                                <button type="button" class="btn btn-sm btn-outline-primary me-2" 
                                                        onclick="setAmount({{ $totalCustomerDebts }})">
                                                    <i class="fas fa-money-bill-wave me-1"></i>دفع كامل
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        onclick="setAmount({{ $totalCustomerDebts / 2 }})">
                                                    <i class="fas fa-divide me-1"></i>نصف المبلغ
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                            <select class="form-select" name="payment_method" required>
                                                <option value="">اختر طريقة الدفع</option>
                                                <option value="نقدي">💵 نقدي</option>
                                                <option value="بطاقة ائتمان">💳 بطاقة ائتمان</option>
                                                <option value="تحويل بنكي">🏦 تحويل بنكي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">طريقة التوزيع <span class="text-danger">*</span></label>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       value="oldest_first" id="oldest_first" checked>
                                                <label class="form-check-label" for="oldest_first">
                                                    <i class="fas fa-sort-numeric-down me-1"></i>الأقدم أولاً
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       value="newest_first" id="newest_first">
                                                <label class="form-check-label" for="newest_first">
                                                    <i class="fas fa-sort-numeric-up me-1"></i>الأحدث أولاً
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       value="proportional" id="proportional">
                                                <label class="form-check-label" for="proportional">
                                                    <i class="fas fa-percentage me-1"></i>توزيع نسبي
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       value="manual" id="manual">
                                                <label class="form-check-label" for="manual">
                                                    <i class="fas fa-hand-pointer me-1"></i>توزيع يدوي
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2" 
                                              placeholder="أضف أي ملاحظات حول الدفع..."></textarea>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-check me-2"></i>تأكيد الدفع
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    @else
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                        <h5>لا توجد مديونيات</h5>
                        <p class="mb-0">هذا العميل قام بسداد جميع مديونياته</p>
                    </div>
                    @endif
                </div>
            </div>
            @else
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-hand-pointer fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">اختر عميل من القائمة</h4>
                    <p class="text-muted mb-0">اختر عميل من القائمة الجانبية لعرض مديونياته وإمكانية دفعها</p>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
// تعيين مبلغ سريع
function setAmount(amount) {
    document.querySelector('input[name="payment_amount"]').value = amount.toFixed(2);
}

// معالجة نموذج الدفع
document.getElementById('paymentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // تعطيل الزر أثناء المعالجة
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
    
    fetch('{{ route("debts.pay") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح
            showAlert('success', data.message);
            
            // إعادة تحميل الصفحة بعد تأخير قصير
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('danger', 'حدث خطأ أثناء معالجة الدفع');
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// دالة إظهار التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
@endsection
