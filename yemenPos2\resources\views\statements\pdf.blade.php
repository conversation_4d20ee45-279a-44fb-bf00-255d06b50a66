<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>كشف حساب - {{ $customer->name }}</title>
    <style>
        body {
            font-family: 'Deja<PERSON>u Sans', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 10px;
            font-size: 11px;
            line-height: 1.3;
            color: #333;
            direction: rtl;
        }

        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .document-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-top: 10px;
        }

        .customer-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .customer-info h3 {
            margin: 0 0 10px 0;
            color: #007bff;
            font-size: 16px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .summary-cards {
            width: 100%;
            margin-bottom: 20px;
        }

        .summary-card {
            width: 18%;
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #dee2e6;
            text-align: center;
            display: inline-block;
            margin: 1%;
            vertical-align: top;
        }

        .summary-card h4 {
            margin: 0 0 5px 0;
            font-size: 18px;
            color: #007bff;
        }

        .summary-card p {
            margin: 0;
            font-size: 11px;
            color: #666;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .table th,
        .table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: center;
        }

        .table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            margin: 20px 0 10px 0;
            padding-bottom: 5px;
            border-bottom: 2px solid #007bff;
        }

        .text-success { color: #28a745; }
        .text-danger { color: #dc3545; }
        .text-warning { color: #ffc107; }
        .text-info { color: #17a2b8; }

        .badge {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 10px;
            color: white;
        }

        .badge-success { background-color: #28a745; }
        .badge-warning { background-color: #ffc107; color: #333; }
        .badge-danger { background-color: #dc3545; }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }

        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">نظام نقطة المبيعات</div>
        <div class="document-title">كشف حساب العميل</div>
        <div style="margin-top: 10px; font-size: 12px; color: #666;">
            تاريخ الطباعة: {{ now()->format('Y-m-d H:i:s') }}
        </div>
    </div>

    <!-- معلومات العميل -->
    <div class="customer-info">
        <h3>معلومات العميل</h3>
        <div class="info-row">
            <span><strong>الاسم:</strong> {{ $customer->name }}</span>
            <span><strong>الهاتف:</strong> {{ $customer->phone ?? 'غير محدد' }}</span>
        </div>
        <div class="info-row">
            <span><strong>العنوان:</strong> {{ $customer->address ?? 'غير محدد' }}</span>
            <span><strong>الفترة:</strong> من {{ $statement['date_from']->format('Y-m-d') }} إلى {{ $statement['date_to']->format('Y-m-d') }}</span>
        </div>
    </div>

    <!-- ملخص الحساب -->
    <div class="summary-cards">
        <div class="summary-card">
            <h4>{{ number_format($summary['opening_balance'], 2) }} ر.ي</h4>
            <p>الرصيد الافتتاحي</p>
        </div>
        <div class="summary-card">
            <h4>{{ number_format($summary['total_sales'], 2) }} ر.ي</h4>
            <p>إجمالي المبيعات</p>
            <p>({{ $summary['sales_count'] }} فاتورة)</p>
        </div>
        <div class="summary-card">
            <h4>{{ number_format($summary['total_paid'], 2) }} ر.ي</h4>
            <p>إجمالي المدفوع</p>
            <p>({{ $summary['payments_count'] }} دفعة)</p>
        </div>
        <div class="summary-card">
            <h4>{{ number_format($summary['total_remaining'], 2) }} ر.ي</h4>
            <p>إجمالي المتبقي</p>
            <p>من فواتير الفترة</p>
        </div>
        <div class="summary-card">
            <h4 class="{{ $summary['closing_balance'] > 0 ? 'text-danger' : 'text-success' }}">
                {{ number_format($summary['closing_balance'], 2) }} ر.ي
            </h4>
            <p>الرصيد الختامي</p>
            <p>{{ $summary['closing_balance'] > 0 ? 'مديونية' : 'مسدد' }}</p>
        </div>
    </div>

    <!-- تفاصيل الفواتير -->
    @if($statement['sales']->count() > 0)
    <div class="section-title">تفاصيل الفواتير ({{ $statement['sales']->count() }} فاتورة)</div>
    <table class="table">
        <thead>
            <tr>
                <th>رقم الفاتورة</th>
                <th>التاريخ</th>
                <th>الإجمالي</th>
                <th>المدفوع</th>
                <th>المتبقي</th>
                <th>الحالة</th>
                <th>تفاصيل الدفعات</th>
            </tr>
        </thead>
        <tbody>
            @foreach($statement['sales'] as $sale)
            <tr>
                <td><strong>{{ $sale->invoice_number }}</strong></td>
                <td>{{ $sale->sale_date->format('Y-m-d') }}</td>
                <td><strong>{{ number_format($sale->total_amount, 2) }} ر.ي</strong></td>
                <td class="text-success"><strong>{{ number_format($sale->paid_amount ?? 0, 2) }} ر.ي</strong></td>
                <td class="text-danger"><strong>{{ number_format($sale->remaining_amount, 2) }} ر.ي</strong></td>
                <td>
                    @if($sale->status === 'مكتملة')
                        <span class="badge badge-success">مكتملة</span>
                    @elseif($sale->status === 'دفع جزئي')
                        <span class="badge badge-warning">دفع جزئي</span>
                    @else
                        <span class="badge badge-danger">غير مدفوعة</span>
                    @endif
                </td>
                <td>
                    @if($sale->salePayments->count() > 0)
                        @foreach($sale->salePayments as $payment)
                            <div style="font-size: 10px; margin-bottom: 2px;">
                                • {{ number_format($payment->amount, 2) }} ر.ي
                                <span style="color: #666;">({{ $payment->created_at->format('m-d') }})</span>
                                @if($payment->payment_method)
                                    <span style="color: #666;">- {{ $payment->payment_method }}</span>
                                @endif
                            </div>
                        @endforeach
                    @else
                        <span style="color: #999; font-size: 10px;">لا توجد دفعات</span>
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @else
    <div class="section-title">تفاصيل الفواتير</div>
    <div class="no-data">لا توجد فواتير في الفترة المحددة</div>
    @endif

    <!-- ملاحظة توضيحية -->
    <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
        <h4 style="color: #007bff; margin: 0 0 10px 0;">ملاحظات مهمة:</h4>
        <ul style="margin: 0; padding-right: 20px;">
            <li>الرصيد الافتتاحي يمثل المديونيات المتراكمة من الفترات السابقة</li>
            <li>إجمالي المتبقي يمثل المديونيات من فواتير الفترة المحددة فقط</li>
            <li>الرصيد الختامي = الرصيد الافتتاحي + إجمالي المتبقي من الفترة</li>
            <li>تفاصيل الدفعات تظهر جميع المدفوعات لكل فاتورة مع التواريخ</li>
        </ul>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام نقطة المبيعات</p>
        <p>شكراً لتعاملكم معنا</p>
    </div>
</body>
</html>
