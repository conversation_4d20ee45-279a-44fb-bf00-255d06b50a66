@extends('layouts.app')

@section('title', 'إعدادات النظام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">الإعدادات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success" onclick="exportSettings()">
                            <i class="fas fa-download me-2"></i>تصدير
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="fas fa-upload me-2"></i>استيراد
                        </button>
                        <button type="button" class="btn btn-warning" onclick="resetSettings()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Form -->
            <form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="row">
                    <!-- Navigation Tabs -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    مجموعات الإعدادات
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="nav flex-column nav-pills" id="settings-tabs" role="tablist">
                                    @php
                                        $groupNames = [
                                            'general' => 'الإعدادات العامة',
                                            'invoice' => 'إعدادات الفواتير',
                                            'inventory' => 'إعدادات المخزون',
                                            'payment' => 'إعدادات الدفع',
                                            'notifications' => 'إعدادات الإشعارات',
                                            'backup' => 'إعدادات النسخ الاحتياطي'
                                        ];
                                        $isFirst = true;
                                    @endphp
                                    
                                    @foreach($settings as $group => $groupSettings)
                                        <button class="nav-link text-start {{ $isFirst ? 'active' : '' }}" 
                                                id="{{ $group }}-tab" data-bs-toggle="pill" 
                                                data-bs-target="#{{ $group }}" type="button" role="tab">
                                            <i class="fas fa-{{ $group === 'general' ? 'cog' : ($group === 'invoice' ? 'receipt' : ($group === 'inventory' ? 'boxes' : ($group === 'payment' ? 'credit-card' : ($group === 'notifications' ? 'bell' : 'save')))) }} me-2"></i>
                                            {{ $groupNames[$group] ?? ucfirst($group) }}
                                            <span class="badge bg-secondary ms-auto">{{ count($groupSettings) }}</span>
                                        </button>
                                        @php $isFirst = false; @endphp
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Content -->
                    <div class="col-md-9">
                        <div class="tab-content" id="settings-content">
                            @php $isFirst = true; @endphp
                            @foreach($settings as $group => $groupSettings)
                                <div class="tab-pane fade {{ $isFirst ? 'show active' : '' }}" 
                                     id="{{ $group }}" role="tabpanel">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">
                                                {{ $groupNames[$group] ?? ucfirst($group) }}
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @foreach($groupSettings as $setting)
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">
                                                            {{ $setting->label }}
                                                            @if($setting->description)
                                                                <i class="fas fa-info-circle text-muted ms-1" 
                                                                   title="{{ $setting->description }}" 
                                                                   data-bs-toggle="tooltip"></i>
                                                            @endif
                                                        </label>
                                                        
                                                        @if($setting->type === 'boolean')
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" 
                                                                       name="{{ $setting->key }}" value="1"
                                                                       id="{{ $setting->key }}"
                                                                       {{ $setting->value === 'true' ? 'checked' : '' }}>
                                                                <label class="form-check-label" for="{{ $setting->key }}">
                                                                    تفعيل
                                                                </label>
                                                            </div>
                                                        @elseif($setting->type === 'select')
                                                            <select class="form-select" name="{{ $setting->key }}">
                                                                @php
                                                                    $options = json_decode($setting->options ?? '[]', true);
                                                                @endphp
                                                                @foreach($options as $value => $label)
                                                                    <option value="{{ $value }}" 
                                                                            {{ $setting->value === $value ? 'selected' : '' }}>
                                                                        {{ $label }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        @elseif($setting->type === 'textarea')
                                                            <textarea class="form-control" name="{{ $setting->key }}" 
                                                                      rows="3">{{ $setting->value }}</textarea>
                                                        @elseif($setting->type === 'file')
                                                            <input type="file" class="form-control" 
                                                                   name="{{ $setting->key }}" accept="image/*">
                                                            @if($setting->value)
                                                                <div class="mt-2">
                                                                    <img src="{{ asset('storage/' . $setting->value) }}" 
                                                                         alt="Current image" class="img-thumbnail" 
                                                                         style="max-width: 100px; max-height: 100px;">
                                                                </div>
                                                            @endif
                                                        @elseif($setting->type === 'number')
                                                            <input type="number" class="form-control" 
                                                                   name="{{ $setting->key }}" 
                                                                   value="{{ $setting->value }}" 
                                                                   step="0.01" min="0">
                                                        @else
                                                            <input type="text" class="form-control" 
                                                                   name="{{ $setting->key }}" 
                                                                   value="{{ $setting->value }}">
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @php $isFirst = false; @endphp
                            @endforeach
                        </div>

                        <!-- Save Button -->
                        <div class="text-end mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد الإعدادات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('settings.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">ملف الإعدادات (JSON)</label>
                        <input type="file" class="form-control" name="settings_file" 
                               accept=".json" required>
                        <div class="form-text">
                            يرجى اختيار ملف JSON يحتوي على الإعدادات المصدرة مسبقاً
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> سيتم استبدال الإعدادات الحالية بالإعدادات الموجودة في الملف
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-upload me-2"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تصدير الإعدادات
function exportSettings() {
    window.location.href = '{{ route("settings.export") }}';
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("settings.reset") }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

// تفعيل tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection
