<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar'); // اسم المنتج بالعربية
            $table->string('name_en')->nullable(); // اسم المنتج بالإنجليزية
            $table->string('barcode')->unique()->nullable(); // الباركود
            $table->string('sku')->unique(); // رمز المنتج
            $table->text('description_ar')->nullable(); // الوصف بالعربية
            $table->text('description_en')->nullable(); // الوصف بالإنجليزية
            $table->foreignId('category_id')->constrained()->onDelete('cascade'); // التصنيف
            $table->decimal('purchase_price', 10, 2); // سعر الشراء
            $table->decimal('selling_price', 10, 2); // سعر البيع
            $table->decimal('wholesale_price', 10, 2)->nullable(); // سعر الجملة
            $table->integer('stock_quantity')->default(0); // الكمية المتوفرة
            $table->integer('min_stock_level')->default(0); // الحد الأدنى للمخزون
            $table->string('unit')->default('قطعة'); // الوحدة (قطعة، كيلو، لتر...)
            $table->string('image')->nullable(); // صورة المنتج
            $table->boolean('is_active')->default(true); // نشط/غير نشط
            $table->boolean('track_stock')->default(true); // تتبع المخزون
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
