<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Category;
use App\Models\Setting;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SaleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Sale::with(['customer', 'user'])
            ->where('status', '!=', 'مسترجعة'); // استبعاد الفواتير المرتجعة

        // فلتر البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // فلتر التاريخ من
        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        // فلتر التاريخ إلى
        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        // فلتر العميل
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // فلتر الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلتر طريقة الدفع
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // فلتر حالة الدفع
        if ($request->filled('payment_status')) {
            switch ($request->payment_status) {
                case 'مدفوعة':
                    $query->where('status', 'مكتملة');
                    break;
                case 'غير_مدفوعة':
                    $query->where('status', 'غير مدفوعة');
                    break;
                case 'جزئي':
                    $query->where('status', 'دفع جزئي');
                    break;
            }
        }

        // فلتر حالة الترحيل
        if ($request->filled('posting_status')) {
            if ($request->posting_status === 'مرحلة') {
                $query->where('is_posted', true);
            } elseif ($request->posting_status === 'غير_مرحلة') {
                $query->where('is_posted', false);
            }
        }

        $sales = $query->latest('sale_date')->latest('sale_time')->paginate(20);

        // الاحتفاظ بمعاملات البحث في الـ pagination
        $sales->appends($request->query());

        // إحصائيات (فقط الفواتير المرحلة)
        $totalSales = Sale::where('is_posted', true)->sum('total_amount');
        $postedSales = Sale::where('is_posted', true)->sum('total_amount');
        $unpostedSales = Sale::where('is_posted', false)->sum('total_amount');
        $cashSales = Sale::where('is_posted', true)->where('payment_method', 'نقدي')->sum('total_amount');
        $creditSales = Sale::where('is_posted', true)->where('payment_method', 'آجل')->sum('total_amount');

        // جلب العملاء للفلتر
        $customers = Customer::where('is_active', true)->orderBy('name')->get();

        return view('sales.index', compact(
            'sales', 'customers', 'totalSales', 'postedSales', 'unpostedSales',
            'cashSales', 'creditSales'
        ));
    }



    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'payment_method' => 'required|in:نقدي,آجل,تحويل بنكي,مختلط',
            'paid_amount' => 'required|numeric|min:0',
        ]);

        DB::beginTransaction();

        try {
            // حساب المجاميع
            $subtotal = 0;
            foreach ($request->items as $item) {
                $subtotal += $item['quantity'] * $item['unit_price'];
            }

            $taxRate = Setting::getValue('tax_rate', 0);
            $taxAmount = ($subtotal * $taxRate) / 100;
            $discountAmount = $request->discount_amount ?? 0;
            $totalAmount = $subtotal + $taxAmount - $discountAmount;
            $remainingAmount = $totalAmount - $request->paid_amount;

            // إنشاء الفاتورة
            $sale = Sale::create([
                'invoice_number' => Sale::generateInvoiceNumber(),
                'customer_id' => $request->customer_id,
                'user_id' => auth()->id(),
                'sale_date' => Carbon::today(),
                'sale_time' => Carbon::now(),
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'paid_amount' => $request->paid_amount,
                'remaining_amount' => $remainingAmount,
                'payment_method' => $request->payment_method,
                'status' => $request->payment_method === 'آجل' ? 'غير مدفوعة' : 'مكتملة',
                'currency' => Setting::getValue('default_currency', 'YER'),
                'notes' => $request->notes,
            ]);

            // إضافة عناصر الفاتورة
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);

                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name_ar,
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'total_price' => $item['quantity'] * $item['unit_price'] - ($item['discount_amount'] ?? 0),
                    'unit' => $product->unit,
                ]);

                // تحديث المخزون
                if ($product->track_stock) {
                    $product->decrement('stock_quantity', $item['quantity']);
                }
            }

            // تحديث رصيد العميل إذا كان الدفع آجل
            if ($request->customer_id && $remainingAmount > 0) {
                $customer = Customer::find($request->customer_id);
                $customer->increment('current_balance', $remainingAmount);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الفاتورة بنجاح',
                'sale_id' => $sale->id,
                'invoice_number' => $sale->invoice_number
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'user', 'saleItems.product']);

        // إذا كان الطلب AJAX، إرجاع JSON
        if (request()->wantsJson() || request()->ajax()) {
            return response()->json([
                'id' => $sale->id,
                'invoice_number' => $sale->invoice_number,
                'sale_date' => $sale->sale_date->format('Y-m-d'),
                'sale_time' => $sale->sale_time->format('H:i:s'),
                'total_amount' => $sale->total_amount,
                'paid_amount' => $sale->paid_amount,
                'remaining_amount' => $sale->remaining_amount,
                'payment_method' => $sale->payment_method,
                'status' => $sale->status,
                'customer' => $sale->customer ? [
                    'id' => $sale->customer->id,
                    'name' => $sale->customer->name,
                    'phone' => $sale->customer->phone,
                    'email' => $sale->customer->email,
                ] : null,
                'user' => [
                    'id' => $sale->user->id,
                    'name' => $sale->user->name,
                ],
                'items' => $sale->saleItems->map(function($item) {
                    return [
                        'product_name' => $item->product->name_ar,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'total' => $item->quantity * $item->price,
                    ];
                }),
            ]);
        }

        return view('sales.show', compact('sale'));
    }

    /**
     * Print invoice
     */
    public function print(Sale $sale)
    {
        $sale->load(['customer', 'user', 'saleItems.product']);
        $sale->update(['is_printed' => true]);

        return view('sales.print', compact('sale'));
    }





    /**
     * إدارة المديونيات
     */
    public function debts(Request $request)
    {
        $query = Sale::with(['customer', 'user'])
            ->where('remaining_amount', '>', 0) // أي فاتورة لها مبلغ متبقي
            ->where('is_posted', true) // فقط الفواتير المرحلة
            ->where('status', '!=', 'مسترجعة'); // استبعاد المرتجعة

        // فلتر البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // فلتر التاريخ من
        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        // فلتر التاريخ إلى
        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        // فلتر العميل
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // فلتر المبلغ المتبقي
        if ($request->filled('min_amount')) {
            $query->where('remaining_amount', '>=', $request->min_amount);
        }

        if ($request->filled('max_amount')) {
            $query->where('remaining_amount', '<=', $request->max_amount);
        }

        $partialSales = $query->latest('sale_date')->latest('sale_time')->paginate(20);

        // الاحتفاظ بمعاملات البحث في الـ pagination
        $partialSales->appends($request->query());

        // حساب إجمالي المديونيات (مع الفلاتر)
        $totalDebts = $query->sum('remaining_amount');

        // إجمالي المديونيات بدون فلاتر (فقط المرحلة)
        $allDebts = Sale::where('remaining_amount', '>', 0)
            ->where('is_posted', true)
            ->where('status', '!=', 'مسترجعة')
            ->sum('remaining_amount');

        // إحصائيات مفصلة (فقط المرحلة)
        $partialDebts = Sale::where('status', 'دفع جزئي')
            ->where('is_posted', true)
            ->sum('remaining_amount');
        $unpaidDebts = Sale::where('status', 'غير مدفوعة')
            ->where('is_posted', true)
            ->sum('remaining_amount');
        $customersWithDebts = Sale::where('remaining_amount', '>', 0)
            ->where('is_posted', true)
            ->whereNotNull('customer_id')
            ->distinct('customer_id')
            ->count();

        // جلب العملاء للفلتر
        $customers = Customer::where('is_active', true)->orderBy('name')->get();

        return view('sales.debts', compact(
            'partialSales',
            'totalDebts',
            'allDebts',
            'customers',
            'partialDebts',
            'unpaidDebts',
            'customersWithDebts'
        ));
    }

    /**
     * دفع مديونية
     */
    public function payDebt(Request $request)
    {
        $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'payment_amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:نقدي,بطاقة ائتمان,تحويل بنكي',
            'notes' => 'nullable|string'
        ]);

        try {
            $sale = Sale::findOrFail($request->sale_id);

            if ($sale->remaining_amount <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'هذه الفاتورة مدفوعة بالكامل'
                ]);
            }

            $paymentAmount = $request->payment_amount;

            if ($paymentAmount > $sale->remaining_amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'المبلغ المدفوع أكبر من المبلغ المتبقي'
                ]);
            }

            // تحديث المبالغ
            $sale->paid_amount = ($sale->paid_amount ?? 0) + $paymentAmount;
            $sale->remaining_amount = $sale->total_amount - $sale->paid_amount;

            // تحديث الحالة بناءً على المبلغ المتبقي
            if ($sale->remaining_amount <= 0) {
                $sale->status = 'مكتملة';
                $sale->remaining_amount = 0; // للتأكد من عدم وجود أرقام سالبة

                // إذا تم دفع المبلغ كاملاً، تحديث طريقة الدفع حسب الطريقة الفعلية
                $sale->payment_method = $request->payment_method;
            } elseif ($sale->paid_amount > 0) {
                $sale->status = 'دفع جزئي'; // دفع جزئي

                // في حالة الدفع الجزئي، إذا كانت الفاتورة آجلة أصلاً، نحتفظ بـ "آجل"
                // لكن إذا كانت نقدية أو بنكية ولم تدفع كاملة، نحتفظ بالطريقة الأصلية
                if ($sale->payment_method === 'آجل') {
                    // تبقى آجل حتى يتم الدفع كاملاً
                } else {
                    // للفواتير النقدية/البنكية التي لم تدفع كاملة، نحتفظ بالطريقة الأصلية
                }
            } else {
                $sale->status = 'غير مدفوعة'; // لم يدفع شيء
            }

            $sale->save();

            // حفظ سجل الدفعة
            \App\Models\SalePayment::create([
                'sale_id' => $sale->id,
                'amount' => $paymentAmount,
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الدفع بنجاح',
                'new_status' => $sale->status,
                'remaining_amount' => $sale->remaining_amount,
                'payment_method' => $sale->payment_method
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تسجيل الدفع: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * إرسال الفاتورة عبر واتساب
     */
    public function sendWhatsApp(Sale $sale)
    {
        try {
            if (!$sale->customer || !$sale->customer->phone) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد رقم هاتف للعميل'
                ]);
            }

            // تنسيق رقم الهاتف
            $phone = $sale->customer->phone;
            $phone = preg_replace('/[^0-9]/', '', $phone);

            if (substr($phone, 0, 1) === '0') {
                $phone = '967' . substr($phone, 1);
            } elseif (substr($phone, 0, 3) !== '967') {
                $phone = '967' . $phone;
            }

            // إنشاء رسالة الواتساب
            $message = "فاتورة رقم: {$sale->invoice_number}\n";
            $message .= "التاريخ: {$sale->sale_date->format('Y-m-d')}\n";
            $message .= "العميل: {$sale->customer->name}\n";
            $message .= "الإجمالي: " . number_format($sale->total_amount, 2) . " ر.ي\n";

            if ($sale->status === 'دفع جزئي' && $sale->remaining_amount > 0) {
                $message .= "المبلغ المدفوع: " . number_format($sale->paid_amount, 2) . " ر.ي\n";
                $message .= "المبلغ المتبقي: " . number_format($sale->remaining_amount, 2) . " ر.ي\n";
            }

            $message .= "\nشكراً لتعاملكم معنا";

            // إنشاء رابط الواتساب
            $whatsappUrl = "https://wa.me/{$phone}?text=" . urlencode($message);

            // تحديث حالة الإرسال
            $sale->update(['is_sent_whatsapp' => true]);

            return response()->json([
                'success' => true,
                'whatsapp_url' => $whatsappUrl,
                'message' => 'تم إنشاء رابط الواتساب بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إرسال الواتساب: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * ترحيل فاتورة
     */
    public function post(Sale $sale)
    {
        if (!$sale->canPost()) {
            return redirect()->back()->with('error', 'هذه الفاتورة مرحلة بالفعل');
        }

        $sale->post();

        return redirect()->back()->with('success', 'تم ترحيل الفاتورة بنجاح');
    }

    /**
     * إلغاء ترحيل فاتورة (للمدير فقط)
     */
    public function unpost(Sale $sale)
    {
        if (!auth()->user()->hasRole('admin')) {
            return redirect()->back()->with('error', 'غير مسموح لك بإلغاء الترحيل');
        }

        $sale->unpost();

        return redirect()->back()->with('success', 'تم إلغاء ترحيل الفاتورة بنجاح');
    }

    /**
     * صفحة الفواتير غير المرحلة
     */
    public function unposted(Request $request)
    {
        $query = Sale::with(['customer', 'user'])
            ->where('is_posted', false)
            ->where('status', '!=', 'مسترجعة'); // استبعاد الفواتير المرتجعة

        // فلاتر البحث (نفس فلاتر الصفحة الرئيسية)
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        $sales = $query->latest('sale_date')->latest('sale_time')->paginate(20);
        $sales->appends($request->query());

        // إحصائيات الفواتير غير المرحلة
        $totalUnposted = $query->sum('total_amount');
        $countUnposted = $query->count();

        $customers = Customer::where('is_active', true)->orderBy('name')->get();

        return view('sales.unposted', compact('sales', 'customers', 'totalUnposted', 'countUnposted'));
    }

    /**
     * صفحة الفواتير المرحلة
     */
    public function posted(Request $request)
    {
        $query = Sale::with(['customer', 'user', 'postedBy'])
            ->where('is_posted', true);

        // فلاتر البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        if ($request->filled('posted_from')) {
            $query->whereDate('posted_at', '>=', $request->posted_from);
        }

        if ($request->filled('posted_to')) {
            $query->whereDate('posted_at', '<=', $request->posted_to);
        }

        $sales = $query->latest('posted_at')->latest('sale_date')->latest('sale_time')->paginate(20);
        $sales->appends($request->query());

        // إحصائيات الفواتير المرحلة (مع الفلاتر)
        $totalPosted = $query->sum('total_amount');
        $countPosted = $query->count();

        // إنشاء نسخة من الاستعلام مع نفس الفلاتر لحساب الإحصائيات
        $statsQuery = Sale::where('is_posted', true);

        // تطبيق نفس الفلاتر على استعلام الإحصائيات
        if ($request->filled('search')) {
            $search = $request->search;
            $statsQuery->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $statsQuery->whereDate('sale_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $statsQuery->whereDate('sale_date', '<=', $request->date_to);
        }

        if ($request->filled('posted_from')) {
            $statsQuery->whereDate('posted_at', '>=', $request->posted_from);
        }

        if ($request->filled('posted_to')) {
            $statsQuery->whereDate('posted_at', '<=', $request->posted_to);
        }

        if ($request->filled('customer_id')) {
            $statsQuery->where('customer_id', $request->customer_id);
        }

        if ($request->filled('payment_method')) {
            $statsQuery->where('payment_method', $request->payment_method);
        }

        // إحصائيات طرق الدفع (مع الفلاتر - بناءً على المبالغ الفعلية)

        // النقدي: المبلغ المدفوع نقداً فقط
        $cashSales = (clone $statsQuery)
            ->where('payment_method', 'نقدي')
            ->sum('paid_amount');

        // البنك: المبلغ المدفوع عبر البنك فقط
        $bankSales = (clone $statsQuery)
            ->whereIn('payment_method', ['بطاقة ائتمان', 'تحويل بنكي'])
            ->sum('paid_amount');

        // الآجل: مجموع المبالغ المتبقية من جميع الفواتير + الفواتير الآجلة الكاملة
        $creditSales = (clone $statsQuery)
            ->where('payment_method', 'آجل')
            ->sum('total_amount') +
            (clone $statsQuery)
            ->where('payment_method', '!=', 'آجل')
            ->sum('remaining_amount');

        $customers = Customer::where('is_active', true)->orderBy('name')->get();

        return view('sales.posted', compact('sales', 'customers', 'totalPosted', 'countPosted', 'cashSales', 'creditSales', 'bankSales'));
    }

    /**
     * ترحيل متعدد للفواتير
     */
    public function postMultiple(Request $request)
    {
        $request->validate([
            'sale_ids' => 'required|array|min:1',
            'sale_ids.*' => 'exists:sales,id'
        ]);

        $postedCount = 0;
        $errors = [];

        foreach ($request->sale_ids as $saleId) {
            $sale = Sale::find($saleId);

            if ($sale && $sale->canPost()) {
                $sale->post();
                $postedCount++;
            } else {
                $errors[] = "الفاتورة {$sale->invoice_number} مرحلة بالفعل";
            }
        }

        $message = "تم ترحيل {$postedCount} فاتورة بنجاح";
        if (!empty($errors)) {
            $message .= ". " . implode(', ', $errors);
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * تعديل فاتورة (فقط غير المرحلة)
     */
    public function edit(Sale $sale)
    {
        // التحقق من إمكانية التعديل
        if (!$sale->canEdit()) {
            return redirect()->back()->with('error', 'لا يمكن تعديل فاتورة مرحلة');
        }

        $categories = Category::where('is_active', true)->get();
        $customers = Customer::where('is_active', true)->get();
        $products = Product::where('is_active', true)->get();

        return view('sales.edit', compact('sale', 'categories', 'customers', 'products'));
    }

    /**
     * تحديث فاتورة (فقط غير المرحلة)
     */
    public function update(Request $request, Sale $sale)
    {
        // التحقق من إمكانية التعديل
        if (!$sale->canEdit()) {
            return redirect()->back()->with('error', 'لا يمكن تعديل فاتورة مرحلة');
        }

        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'payment_method' => 'required|in:نقدي,بطاقة ائتمان,تحويل بنكي,آجل',
            'discount_amount' => 'nullable|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'nullable|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // إعادة المخزون للحالة السابقة
            foreach ($sale->saleItems as $oldItem) {
                $product = Product::find($oldItem->product_id);
                if ($product) {
                    $product->increment('stock_quantity', $oldItem->quantity);
                }
            }

            // حذف العناصر القديمة
            $sale->saleItems()->delete();

            // حساب المجاميع الجديدة
            $subtotal = 0;
            foreach ($request->items as $item) {
                $subtotal += $item['quantity'] * $item['price'];
            }

            $discount = $request->discount_amount ?? 0;
            $tax = $request->tax_amount ?? 0;
            $total = $subtotal - $discount + $tax;

            // تحديد المبالغ حسب طريقة الدفع
            if ($request->payment_method === 'آجل') {
                $paidAmount = 0;
                $remainingAmount = $total;
                $status = 'غير مدفوعة';
            } else {
                $paidAmount = $request->paid_amount ?? $total;
                $remainingAmount = $total - $paidAmount;
                $status = $remainingAmount > 0 ? 'دفع جزئي' : 'مكتملة';
            }

            // تحديث الفاتورة
            $sale->update([
                'customer_id' => $request->customer_id,
                'subtotal' => $subtotal,
                'discount_amount' => $discount,
                'tax_amount' => $tax,
                'total_amount' => $total,
                'paid_amount' => $paidAmount,
                'remaining_amount' => $remainingAmount,
                'payment_method' => $request->payment_method,
                'status' => $status,
                'notes' => $request->notes,
            ]);

            // إضافة العناصر الجديدة
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);

                // التحقق من المخزون
                if ($product->stock_quantity < $item['quantity']) {
                    throw new \Exception("المنتج {$product->name_ar} غير متوفر بالكمية المطلوبة");
                }

                // إضافة العنصر
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $product->name_ar,
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['price'],
                    'total_price' => $item['quantity'] * $item['price'],
                    'unit' => 'قطعة',
                ]);

                // تحديث المخزون
                $product->decrement('stock_quantity', $item['quantity']);
            }

            DB::commit();

            return redirect()->route('sales.show', $sale)
                ->with('success', 'تم تحديث الفاتورة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'خطأ في تحديث الفاتورة: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * حذف فاتورة (فقط غير المرحلة)
     */
    public function destroy(Sale $sale)
    {
        // التحقق من إمكانية الحذف
        if (!$sale->canDelete()) {
            return redirect()->back()->with('error', 'لا يمكن حذف فاتورة مرحلة');
        }

        try {
            DB::beginTransaction();

            // إعادة المخزون
            foreach ($sale->saleItems as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $product->increment('stock_quantity', $item->quantity);
                }
            }

            // حذف العناصر
            $sale->saleItems()->delete();

            // حذف الفاتورة
            $sale->delete();

            DB::commit();

            return redirect()->route('sales.index')
                ->with('success', 'تم حذف الفاتورة وإعادة المخزون بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'خطأ في حذف الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * استرجاع فاتورة (إعادة المخزون بدون حذف)
     */
    public function refund(Sale $sale)
    {
        // التحقق من إمكانية الاسترجاع
        if (!$sale->canRefund()) {
            if ($sale->is_posted) {
                return redirect()->back()->with('error', 'لا يمكن استرجاع فاتورة مرحلة');
            } elseif ($sale->status === 'مسترجعة') {
                return redirect()->back()->with('error', 'هذه الفاتورة مسترجعة بالفعل');
            } else {
                return redirect()->back()->with('error', 'لا يمكن استرجاع هذه الفاتورة');
            }
        }

        try {
            DB::beginTransaction();

            // إعادة المخزون فقط إذا لم تكن مسترجعة من قبل
            foreach ($sale->saleItems as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $product->increment('stock_quantity', $item->quantity);
                }
            }

            // تحديث حالة الفاتورة
            $sale->update([
                'status' => 'مسترجعة',
                'paid_amount' => 0,
                'remaining_amount' => 0,
                'notes' => ($sale->notes ?? '') . "\n[تم الاسترجاع في " . now()->format('Y-m-d H:i') . " بواسطة " . auth()->user()->name . "]"
            ]);

            DB::commit();

            return redirect()->back()
                ->with('success', 'تم استرجاع الفاتورة وإعادة المخزون بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'خطأ في استرجاع الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * صفحة الفواتير المرتجعة
     */
    public function refunded(Request $request)
    {
        $query = Sale::with(['customer', 'user'])
            ->where('status', 'مسترجعة');

        // فلاتر البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        $sales = $query->latest('updated_at')->latest('sale_date')->latest('sale_time')->paginate(20);
        $sales->appends($request->query());

        // إحصائيات الفواتير المرتجعة
        $totalRefunded = $query->sum('total_amount');
        $countRefunded = $query->count();

        $customers = Customer::where('is_active', true)->orderBy('name')->get();

        return view('sales.refunded', compact('sales', 'customers', 'totalRefunded', 'countRefunded'));
    }

    /**
     * حذف نهائي للفاتورة المرتجعة (للمدير فقط)
     */
    public function forceDelete(Sale $sale)
    {
        // التحقق من صلاحية المدير
        if (!auth()->user()->hasRole('admin')) {
            return redirect()->back()->with('error', 'غير مسموح لك بحذف الفواتير المرتجعة');
        }

        // التحقق من أن الفاتورة مرتجعة
        if ($sale->status !== 'مسترجعة') {
            return redirect()->back()->with('error', 'يمكن حذف الفواتير المرتجعة فقط');
        }

        try {
            DB::beginTransaction();

            // حذف العناصر
            $sale->saleItems()->delete();

            // حذف الفاتورة نهائياً
            $sale->delete();

            DB::commit();

            return redirect()->route('sales.refunded')
                ->with('success', 'تم حذف الفاتورة المرتجعة نهائياً');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'خطأ في حذف الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * API: الحصول على المبيعات غير المدفوعة
     */
    public function getUnpaidSales()
    {
        $sales = Sale::with(['customer'])
            ->where(function($q) {
                $q->where('status', 'دفع جزئي')
                  ->orWhere('remaining_amount', '>', 0);
            })
            ->select('id', 'invoice_number', 'customer_id', 'total_amount', 'paid_amount', 'remaining_amount')
            ->get();

        return response()->json($sales);
    }
}
