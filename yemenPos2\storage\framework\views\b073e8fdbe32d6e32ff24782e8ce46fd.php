<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة <?php echo e($sale->invoice_number); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: white;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        .invoice-header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .company-info {
            color: #666;
            font-size: 12px;
        }

        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            border: 2px solid #2c3e50;
            border-radius: 12px;
            overflow: hidden;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .customer-info {
            flex: 1;
            padding: 20px;
            text-align: right;
            background: #f8f9fa;
            position: relative;
        }

        .customer-info::after {
            content: '';
            position: absolute;
            top: 10px;
            bottom: 10px;
            left: 0;
            width: 3px;
            background: linear-gradient(to bottom, #2c3e50, #3498db, #2c3e50);
            border-radius: 2px;
        }

        .invoice-info {
            flex: 1;
            padding: 20px;
            text-align: left;
            background: #ffffff;
        }

        .info-title {
            font-weight: bold;
            color: #2c3e50;
            background: linear-gradient(135deg, #3498db, #2c3e50);
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 15px -20px;
            text-align: center;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px dotted #dee2e6;
            font-size: 14px;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-row span:first-child {
            font-weight: 600;
            color: #2c3e50;
        }

        .info-row span:last-child {
            color: #495057;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 2px solid #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .items-table th {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            font-size: 14px;
        }

        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .items-table tbody tr:hover {
            background: #e3f2fd;
        }

        .items-table td:first-child {
            font-weight: bold;
            color: #2c3e50;
        }

        .totals-section {
            margin-left: auto;
            width: 350px;
            border: 2px solid #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background: #ffffff;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .total-row:hover {
            background: #f8f9fa;
        }

        .total-row:last-child {
            border-bottom: none;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            font-weight: bold;
            font-size: 18px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .total-row:last-child:hover {
            background: linear-gradient(135deg, #34495e, #2980b9);
        }

        .payment-info {
            margin-top: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #2c3e50;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .payment-info .info-row {
            padding: 8px 0;
            border-bottom: 1px dotted #dee2e6;
            font-size: 15px;
        }

        .payment-info .info-row:last-child {
            border-bottom: none;
        }

        .payment-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }

        .status-complete {
            background: #d4edda;
            color: #155724;
        }

        .status-partial {
            background: #fff3cd;
            color: #856404;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 3px solid #2c3e50;
            padding-top: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 10px 10px 0 0;
            padding: 20px;
        }

        .footer p:first-child {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        @media print {
            body {
                font-size: 12px;
            }

            .invoice-container {
                padding: 0;
                max-width: none;
            }

            .no-print {
                display: none !important;
            }

            /* تحسينات للطباعة */
            .invoice-details,
            .items-table,
            .totals-section,
            .payment-info {
                box-shadow: none !important;
                border: 1px solid #000 !important;
            }

            .info-title,
            .items-table th,
            .total-row:last-child {
                background: #f0f0f0 !important;
                color: #000 !important;
                text-shadow: none !important;
            }

            .customer-info::after {
                background: #000 !important;
            }
        }

        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .print-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-btn no-print" onclick="window.print()">
        🖨️ طباعة
    </button>

    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <?php
                $companyLogo = \App\Models\Setting::getValue('company_logo');
                $companyName = \App\Models\Setting::getValue('company_name', 'نظام نقطة البيع');
                $companyAddress = \App\Models\Setting::getValue('company_address', 'صنعاء، اليمن');
                $companyPhone = \App\Models\Setting::getValue('company_phone', '+*********** 789');
                $companyEmail = \App\Models\Setting::getValue('company_email', '<EMAIL>');

                // إعدادات الضريبة
                $taxEnabled = \App\Models\Setting::getValue('tax_enabled', false);
                $taxNumber = \App\Models\Setting::getValue('tax_number', '');
                $taxRate = \App\Models\Setting::getValue('tax_rate', 0);
                $taxName = \App\Models\Setting::getValue('tax_name', 'ضريبة القيمة المضافة');
            ?>

            <div style="display: flex; align-items: center; justify-content: center; gap: 20px; margin-bottom: 15px;">
                <?php if($companyLogo): ?>
                    <img src="<?php echo e(asset('storage/' . $companyLogo)); ?>"
                         alt="شعار الشركة"
                         style="max-height: 80px; max-width: 120px; object-fit: contain;">
                <?php endif; ?>
                <div style="text-align: center;">
                    <div class="company-name"><?php echo e($companyName); ?></div>
                </div>
            </div>

            <div class="company-info">
                العنوان: <?php echo e($companyAddress); ?> | الهاتف: <?php echo e($companyPhone); ?> | البريد: <?php echo e($companyEmail); ?>

            </div>

            <!-- معلومات الضريبة -->
            <?php if($taxEnabled && ($taxNumber || $sale->tax_amount > 0)): ?>
                <div class="tax-info" style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                    <div style="display: flex; justify-content: center; gap: 30px; font-size: 12px; color: #666;">
                        <?php if($taxNumber): ?>
                            <span><strong>الرقم الضريبي:</strong> <?php echo e($taxNumber); ?></span>
                        <?php endif; ?>
                        <?php if($sale->tax_amount > 0 && $taxRate > 0): ?>
                            <span><strong><?php echo e($taxName); ?>:</strong> <?php echo e($taxRate); ?>%</span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="customer-info">
                <div class="info-title">بيانات العميل</div>
                <div class="info-row">
                    <span>العميل:</span>
                    <span><?php echo e($sale->customer ? $sale->customer->name : 'عميل عادي'); ?></span>
                </div>
                <?php if($sale->customer): ?>
                <div class="info-row">
                    <span>الهاتف:</span>
                    <span><?php echo e($sale->customer->phone ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span>العنوان:</span>
                    <span><?php echo e($sale->customer->address ?? 'غير محدد'); ?></span>
                </div>
                <?php endif; ?>
            </div>

            <div class="invoice-info">
                <div class="info-title">بيانات الفاتورة</div>
                <div class="info-row">
                    <span>رقم الفاتورة:</span>
                    <span><?php echo e($sale->invoice_number); ?></span>
                </div>
                <div class="info-row">
                    <span>التاريخ:</span>
                    <span><?php echo e($sale->sale_date->format('Y-m-d')); ?></span>
                </div>
                <div class="info-row">
                    <span>الوقت:</span>
                    <span>
                        <?php
                            $time = \Carbon\Carbon::parse($sale->sale_time);
                            $formattedTime = $time->format('g:i');
                            $period = $time->format('A') === 'AM' ? 'صباحاً' : 'مساءً';
                        ?>
                        <?php echo e($formattedTime); ?> <?php echo e($period); ?>

                    </span>
                </div>
                <div class="info-row">
                    <span>الكاشير:</span>
                    <span><?php echo e($sale->user->name ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span>الحالة:</span>
                    <span class="payment-status <?php echo e($sale->status === 'مكتملة' ? 'status-complete' : 'status-partial'); ?>">
                        <?php echo e($sale->status); ?>

                    </span>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكود</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $sale->saleItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($index + 1); ?></td>
                    <td><?php echo e($item->product_name); ?></td>
                    <td><?php echo e($item->product_sku); ?></td>
                    <td><?php echo e($item->quantity); ?></td>
                    <td><?php echo e($item->unit); ?></td>
                    <td><?php echo e(number_format($item->unit_price, 2)); ?> ر.ي</td>
                    <td><?php echo e(number_format($item->total_price, 2)); ?> ر.ي</td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span><?php echo e(number_format($sale->subtotal, 2)); ?> ر.ي</span>
            </div>
            <?php if($sale->discount_amount > 0): ?>
            <div class="total-row">
                <span>الخصم:</span>
                <span><?php echo e(number_format($sale->discount_amount, 2)); ?> ر.ي</span>
            </div>
            <?php endif; ?>
            <?php if($sale->tax_amount > 0): ?>
            <div class="total-row">
                <span><?php echo e($taxName); ?><?php if($taxRate > 0): ?> (<?php echo e($taxRate); ?>%)<?php endif; ?>:</span>
                <span><?php echo e(number_format($sale->tax_amount, 2)); ?> ر.ي</span>
            </div>
            <?php endif; ?>
            <div class="total-row">
                <span>الإجمالي:</span>
                <span><?php echo e(number_format($sale->total_amount, 2)); ?> ر.ي</span>
            </div>
        </div>

        <!-- Payment Info -->
        <div class="payment-info">
            <div class="info-row">
                <span><strong>طريقة الدفع:</strong></span>
                <span><?php echo e($sale->payment_method); ?></span>
            </div>
            <div class="info-row">
                <span><strong>المبلغ المدفوع:</strong></span>
                <span><?php echo e(number_format($sale->paid_amount ?? $sale->total_amount, 2)); ?> ر.ي</span>
            </div>
            <?php if($sale->remaining_amount > 0): ?>
            <div class="info-row">
                <span><strong>المبلغ المتبقي:</strong></span>
                <span style="color: #dc3545; font-weight: bold;"><?php echo e(number_format($sale->remaining_amount, 2)); ?> ر.ي</span>
            </div>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام نقطة البيع - <?php echo e(now()->format('Y-m-d H:i:s')); ?></p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            // تأخير قصير للسماح بتحميل الصفحة
            setTimeout(() => {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
<?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/sales/print.blade.php ENDPATH**/ ?>