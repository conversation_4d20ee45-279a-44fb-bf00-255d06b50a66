<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // الحصول على دور المدير
        $adminRole = Role::where('name', 'admin')->first();
        $cashierRole = Role::where('name', 'cashier')->first();

        // إنشاء المستخدم الإداري الأول
        User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'role_id' => $adminRole->id,
            'phone' => '+967-1-234567',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // إنشاء كاشير تجريبي
        User::create([
            'name' => 'كاشير تجريبي',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'role_id' => $cashierRole->id,
            'phone' => '+967-1-234568',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
    }
}
