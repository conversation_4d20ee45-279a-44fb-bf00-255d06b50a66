# عرض الضريبة في الفواتير

## ✅ **الميزات المضافة**

### 🧾 **الرقم الضريبي في header الفاتورة**
- يظهر الرقم الضريبي أعلى الفاتورة عند تفعيل الضريبة
- يظهر فقط إذا كان محدد في الإعدادات
- تنسيق جميل مع خلفية مميزة

### 📊 **نسبة الضريبة في الفاتورة**
- تظهر نسبة الضريبة في header الفاتورة
- تظهر نسبة الضريبة مع اسم الضريبة في قسم المجاميع
- تظهر فقط إذا كانت الفاتورة تحتوي على ضريبة

## 🎨 **التصميم الجديد**

### **Header الفاتورة:**
```
┌─────────────────────────────────────────┐
│              نظام نقطة البيع            │
│ العنوان: صنعاء، اليمن | الهاتف: 123456  │
├─────────────────────────────────────────┤
│ الرقم الضريبي: 053551252022             │
│ ضريبة القيمة المضافة: 15%              │
└─────────────────────────────────────────┘
```

### **قسم المجاميع:**
```
المجموع الفرعي: 1000.00 ر.ي
الخصم: 100.00 ر.ي
ضريبة القيمة المضافة (15%): 135.00 ر.ي
الإجمالي: 1035.00 ر.ي
```

## 🔧 **التحديثات التقنية**

### **1. إضافة إعداد الرقم الضريبي:**
```php
// في migration
Setting::create([
    'key' => 'tax_number',
    'value' => '',
    'type' => 'text',
    'group' => 'tax',
    'label' => 'الرقم الضريبي',
    'description' => 'الرقم الضريبي للشركة الذي سيظهر في الفواتير',
    'is_public' => false
]);
```

### **2. تحديث API endpoint:**
```php
// في POSController
public function getTaxSettings()
{
    return response()->json([
        'tax_enabled' => Setting::getValue('tax_enabled', false),
        'tax_rate' => Setting::getValue('tax_rate', 0),
        'tax_name' => Setting::getValue('tax_name', 'ضريبة القيمة المضافة'),
        'tax_number' => Setting::getValue('tax_number', ''), // جديد
    ]);
}
```

### **3. تحديث template الفاتورة:**
```php
// في print.blade.php
@php
    // إعدادات الضريبة
    $taxEnabled = \App\Models\Setting::getValue('tax_enabled', false);
    $taxNumber = \App\Models\Setting::getValue('tax_number', '');
    $taxRate = \App\Models\Setting::getValue('tax_rate', 0);
    $taxName = \App\Models\Setting::getValue('tax_name', 'ضريبة القيمة المضافة');
@endphp

<!-- معلومات الضريبة -->
@if($taxEnabled && ($taxNumber || $sale->tax_amount > 0))
    <div class="tax-info">
        @if($taxNumber)
            <span><strong>الرقم الضريبي:</strong> {{ $taxNumber }}</span>
        @endif
        @if($sale->tax_amount > 0 && $taxRate > 0)
            <span><strong>{{ $taxName }}:</strong> {{ $taxRate }}%</span>
        @endif
    </div>
@endif
```

### **4. تحسين عرض الضريبة في المجاميع:**
```php
@if($sale->tax_amount > 0)
<div class="total-row">
    <span>{{ $taxName }}@if($taxRate > 0) ({{ $taxRate }}%)@endif:</span>
    <span>{{ number_format($sale->tax_amount, 2) }} ر.ي</span>
</div>
@endif
```

## 📋 **حالات العرض**

### **الحالة 1: الضريبة مفعلة + رقم ضريبي موجود**
```
✅ يظهر الرقم الضريبي في header
✅ تظهر نسبة الضريبة في header (إذا كانت الفاتورة تحتوي على ضريبة)
✅ يظهر اسم الضريبة مع النسبة في المجاميع
```

### **الحالة 2: الضريبة مفعلة + لا يوجد رقم ضريبي**
```
❌ لا يظهر الرقم الضريبي
✅ تظهر نسبة الضريبة في header (إذا كانت الفاتورة تحتوي على ضريبة)
✅ يظهر اسم الضريبة مع النسبة في المجاميع
```

### **الحالة 3: الضريبة معطلة**
```
❌ لا تظهر معلومات الضريبة في header
✅ تظهر قيمة الضريبة في المجاميع (إذا كانت موجودة في الفاتورة)
```

### **الحالة 4: فاتورة بدون ضريبة**
```
✅ يظهر الرقم الضريبي في header (إذا كان محدد)
❌ لا تظهر نسبة الضريبة
❌ لا يظهر سطر الضريبة في المجاميع
```

## ⚙️ **كيفية الإعداد**

### **1. تحديد الرقم الضريبي:**
1. اذهب إلى **الإعدادات** → **إعدادات الضرائب**
2. أدخل **الرقم الضريبي** في الحقل المخصص
3. احفظ الإعدادات

### **2. تفعيل الضريبة:**
1. فعل **"تفعيل الضرائب"**
2. حدد **معدل الضريبة** (مثال: 15%)
3. اختياري: غير **اسم الضريبة**
4. احفظ الإعدادات

### **3. اختبار الفاتورة:**
1. اذهب إلى **نقطة البيع**
2. أضف منتجات وأتمم البيع
3. اطبع الفاتورة أو اعرضها
4. تحقق من ظهور الرقم الضريبي والنسبة

## 🎯 **أمثلة عملية**

### **مثال 1: فاتورة كاملة مع ضريبة**
```
┌─────────────────────────────────────────┐
│              شركة التجارة المتقدمة       │
│ العنوان: صنعاء، اليمن | الهاتف: 123456  │
├─────────────────────────────────────────┤
│ الرقم الضريبي: 053551252022             │
│ ضريبة القيمة المضافة: 15%              │
├─────────────────────────────────────────┤
│ رقم الفاتورة: INV-2025-000025           │
│ التاريخ: 2025-05-28                     │
├─────────────────────────────────────────┤
│ المنتجات...                            │
├─────────────────────────────────────────┤
│ المجموع الفرعي: 1000.00 ر.ي           │
│ الخصم: 100.00 ر.ي                     │
│ ضريبة القيمة المضافة (15%): 135.00 ر.ي │
│ الإجمالي: 1035.00 ر.ي                 │
└─────────────────────────────────────────┘
```

### **مثال 2: فاتورة بدون ضريبة**
```
┌─────────────────────────────────────────┐
│              شركة التجارة المتقدمة       │
│ العنوان: صنعاء، اليمن | الهاتف: 123456  │
├─────────────────────────────────────────┤
│ الرقم الضريبي: 053551252022             │
├─────────────────────────────────────────┤
│ رقم الفاتورة: INV-2025-000026           │
│ التاريخ: 2025-05-28                     │
├─────────────────────────────────────────┤
│ المنتجات...                            │
├─────────────────────────────────────────┤
│ المجموع الفرعي: 1000.00 ر.ي           │
│ الخصم: 100.00 ر.ي                     │
│ الإجمالي: 900.00 ر.ي                  │
└─────────────────────────────────────────┘
```

## 🎉 **النتائج**

### **✅ للمستخدم:**
- **معلومات ضريبية كاملة** في الفاتورة
- **امتثال للقوانين** الضريبية
- **فواتير احترافية** مع جميع البيانات المطلوبة

### **✅ للنظام:**
- **مرونة في العرض** حسب الإعدادات
- **تكامل مع نظام الضرائب** الموجود
- **عرض ذكي** يتكيف مع حالة الفاتورة

### **✅ للأعمال:**
- **فواتير قانونية** تحتوي على الرقم الضريبي
- **شفافية في الضرائب** مع عرض النسبة
- **سهولة المراجعة** والتدقيق

---

**الآن الفواتير تعرض جميع المعلومات الضريبية المطلوبة بشكل احترافي!** 📄✨
