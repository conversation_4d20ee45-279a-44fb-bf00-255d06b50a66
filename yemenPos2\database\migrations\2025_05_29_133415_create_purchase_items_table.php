<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_items', function (Blueprint $table) {
            $table->id();

            // ربط بالمشترى
            $table->foreignId('purchase_id')->constrained()->onDelete('cascade');

            // ربط بالمنتج
            $table->foreignId('product_id')->constrained()->onDelete('cascade');

            // الكمية المشتراة
            $table->decimal('quantity', 10, 2);

            // سعر الشراء لهذه الدفعة
            $table->decimal('purchase_price', 10, 2);

            // إجمالي السعر (الكمية × سعر الشراء)
            $table->decimal('total_price', 12, 2);

            // تاريخ انتهاء الصلاحية (اختياري)
            $table->date('expiry_date')->nullable();

            // تاريخ الإنتاج (اختياري)
            $table->date('production_date')->nullable();

            // رقم الدفعة (سيتم إنشاؤه تلقائ<|im_start|>)
            $table->string('batch_number')->unique();

            // الكمية المتبقية (تبدأ بنفس الكمية المشتراة)
            $table->decimal('remaining_quantity', 10, 2);

            // حالة الدفعة
            $table->enum('batch_status', ['active', 'expired', 'depleted', 'recalled'])
                  ->default('active');

            // ملاحظات خاصة بهذا العنصر
            $table->text('notes')->nullable();

            $table->timestamps();

            // فهارس للبحث السريع
            $table->index(['product_id', 'expiry_date']);
            $table->index(['batch_number']);
            $table->index(['expiry_date', 'batch_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_items');
    }
};
