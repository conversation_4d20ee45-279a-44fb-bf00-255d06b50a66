@extends('layouts.app')

@section('title', 'تعديل الفاتورة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-edit me-2 text-primary"></i>
                        تعديل الفاتورة {{ $sale->invoice_number }}
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('sales.index') }}">المبيعات</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('sales.show', $sale) }}">{{ $sale->invoice_number }}</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('sales.show', $sale) }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>العودة للفاتورة
                    </a>
                </div>
            </div>

            <!-- Alert -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> تعديل الفاتورة سيؤثر على المخزون. سيتم إعادة المخزون للحالة السابقة ثم خصم الكميات الجديدة.
            </div>

            <!-- Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('sales.update', $sale) }}" id="editSaleForm">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- معلومات أساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">العميل</label>
                                    <select class="form-select" name="customer_id" id="customerSelect">
                                        <option value="">عميل عادي</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}"
                                                    {{ $sale->customer_id == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-select" name="payment_method" id="paymentMethod" required>
                                        <option value="نقدي" {{ $sale->payment_method == 'نقدي' ? 'selected' : '' }}>نقدي</option>
                                        <option value="بطاقة ائتمان" {{ $sale->payment_method == 'بطاقة ائتمان' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                        <option value="تحويل بنكي" {{ $sale->payment_method == 'تحويل بنكي' ? 'selected' : '' }}>تحويل بنكي</option>
                                        <option value="آجل" {{ $sale->payment_method == 'آجل' ? 'selected' : '' }}>آجل</option>
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الخصم</label>
                                            <input type="number" class="form-control" name="discount_amount"
                                                   value="{{ $sale->discount_amount }}" min="0" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الضريبة</label>
                                            <input type="number" class="form-control" name="tax_amount"
                                                   value="{{ $sale->tax_amount }}" min="0" step="0.01">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3" id="paidAmountSection" style="{{ $sale->payment_method === 'آجل' ? 'display: none;' : '' }}">
                                    <label class="form-label">المبلغ المدفوع</label>
                                    <input type="number" class="form-control" name="paid_amount"
                                           value="{{ $sale->paid_amount }}" min="0" step="0.01">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3">{{ $sale->notes }}</textarea>
                                </div>
                            </div>

                            <!-- المنتجات -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إضافة منتج</label>
                                    <select class="form-select" id="productSelect">
                                        <option value="">اختر منتج...</option>
                                        @foreach($products as $product)
                                            <option value="{{ $product->id }}"
                                                    data-name="{{ $product->name_ar }}"
                                                    data-price="{{ $product->selling_price }}"
                                                    data-stock="{{ $product->stock_quantity }}">
                                                {{ $product->name_ar }} - {{ $product->sku }} ({{ $product->stock_quantity }} متوفر)
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered" id="itemsTable">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية</th>
                                                <th>السعر</th>
                                                <th>الإجمالي</th>
                                                <th>إجراء</th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemsTableBody">
                                            @foreach($sale->saleItems as $item)
                                            <tr>
                                                <td>
                                                    {{ $item->product_name }}
                                                    <input type="hidden" name="items[{{ $loop->index }}][product_id]" value="{{ $item->product_id }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control quantity-input"
                                                           name="items[{{ $loop->index }}][quantity]"
                                                           value="{{ $item->quantity }}" min="1" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control price-input"
                                                           name="items[{{ $loop->index }}][price]"
                                                           value="{{ $item->unit_price }}" min="0" step="0.01" required>
                                                </td>
                                                <td class="item-total">{{ number_format($item->total_price, 2) }}</td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger remove-item">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- الإجماليات -->
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>المجموع الفرعي:</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <span id="subtotalDisplay">{{ number_format($sale->subtotal, 2) }}</span> ر.ي
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>الخصم:</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <span id="discountDisplay">{{ number_format($sale->discount_amount, 2) }}</span> ر.ي
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>الضريبة:</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <span id="taxDisplay">{{ number_format($sale->tax_amount, 2) }}</span> ر.ي
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>الإجمالي:</strong>
                                            </div>
                                            <div class="col-6 text-end">
                                                <strong><span id="totalDisplay">{{ number_format($sale->total_amount, 2) }}</span> ر.ي</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('sales.show', $sale) }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التعديلات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let itemIndex = {{ $sale->saleItems->count() }};

$(document).ready(function() {
    // إضافة منتج
    $('#productSelect').change(function() {
        const productId = $(this).val();
        const productName = $(this).find(':selected').data('name');
        const productPrice = $(this).find(':selected').data('price');
        const productStock = $(this).find(':selected').data('stock');

        if (productId) {
            addItemToTable(productId, productName, productPrice, productStock);
            $(this).val('');
        }
    });

    // حذف منتج
    $(document).on('click', '.remove-item', function() {
        $(this).closest('tr').remove();
        updateTotals();
        updateItemIndices();
    });

    // تحديث الإجماليات عند تغيير الكمية أو السعر
    $(document).on('input', '.quantity-input, .price-input', function() {
        updateItemTotal($(this).closest('tr'));
        updateTotals();
    });

    // تحديث الإجماليات عند تغيير الخصم أو الضريبة
    $('input[name="discount_amount"], input[name="tax_amount"]').on('input', function() {
        updateTotals();
    });

    // إظهار/إخفاء المبلغ المدفوع حسب طريقة الدفع
    $('#paymentMethod').change(function() {
        if ($(this).val() === 'آجل') {
            $('#paidAmountSection').hide();
            $('input[name="paid_amount"]').val(0);
        } else {
            $('#paidAmountSection').show();
        }
    });

    // تحديث الإجماليات عند التحميل
    updateTotals();
});

function addItemToTable(productId, productName, productPrice, productStock) {
    const row = `
        <tr>
            <td>
                ${productName}
                <input type="hidden" name="items[${itemIndex}][product_id]" value="${productId}">
            </td>
            <td>
                <input type="number" class="form-control quantity-input"
                       name="items[${itemIndex}][quantity]"
                       value="1" min="1" max="${productStock}" required>
            </td>
            <td>
                <input type="number" class="form-control price-input"
                       name="items[${itemIndex}][price]"
                       value="${productPrice}" min="0" step="0.01" required>
            </td>
            <td class="item-total">${productPrice}</td>
            <td>
                <button type="button" class="btn btn-sm btn-danger remove-item">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;

    $('#itemsTableBody').append(row);
    itemIndex++;
    updateTotals();
}

function updateItemTotal(row) {
    const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    const price = parseFloat(row.find('.price-input').val()) || 0;
    const total = quantity * price;

    row.find('.item-total').text(total.toFixed(2));
}

function updateTotals() {
    let subtotal = 0;

    // حساب المجموع الفرعي
    $('#itemsTableBody tr').each(function() {
        const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        const price = parseFloat($(this).find('.price-input').val()) || 0;
        subtotal += quantity * price;
    });

    const discount = parseFloat($('input[name="discount_amount"]').val()) || 0;
    const tax = parseFloat($('input[name="tax_amount"]').val()) || 0;
    const total = subtotal - discount + tax;

    // تحديث العرض
    $('#subtotalDisplay').text(subtotal.toFixed(2));
    $('#discountDisplay').text(discount.toFixed(2));
    $('#taxDisplay').text(tax.toFixed(2));
    $('#totalDisplay').text(total.toFixed(2));

    // تحديث المبلغ المدفوع إذا لم يكن آجل
    if ($('#paymentMethod').val() !== 'آجل') {
        $('input[name="paid_amount"]').val(total.toFixed(2));
    }
}

function updateItemIndices() {
    $('#itemsTableBody tr').each(function(index) {
        $(this).find('input[name*="["]').each(function() {
            const name = $(this).attr('name');
            const newName = name.replace(/\[\d+\]/, `[${index}]`);
            $(this).attr('name', newName);
        });
    });
    itemIndex = $('#itemsTableBody tr').length;
}

// التحقق من صحة النموذج قبل الإرسال
$('#editSaleForm').submit(function(e) {
    if ($('#itemsTableBody tr').length === 0) {
        e.preventDefault();
        alert('يجب إضافة منتج واحد على الأقل');
        return false;
    }

    // التحقق من الكميات المتوفرة
    let hasError = false;
    $('#itemsTableBody tr').each(function() {
        const quantity = parseInt($(this).find('.quantity-input').val());
        const maxStock = parseInt($(this).find('.quantity-input').attr('max'));

        if (quantity > maxStock) {
            hasError = true;
            alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
            return false;
        }
    });

    if (hasError) {
        e.preventDefault();
        return false;
    }
});
</script>
@endpush
