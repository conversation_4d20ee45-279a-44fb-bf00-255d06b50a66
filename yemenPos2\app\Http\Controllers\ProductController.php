<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\Supplier;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $perPage = request('per_page', 20);
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 20;

        $products = Product::with(['category', 'defaultSupplier', 'batches', 'activeBatches'])
            ->when(request('search'), function($query, $search) {
                $query->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%")
                      ->orWhere('barcode', 'like', "%{$search}%");
            })
            ->when(request('category'), function($query, $category) {
                $query->where('category_id', $category);
            })
            ->when(request('filter') === 'low_stock', function($query) {
                $query->whereRaw('stock_quantity <= min_stock_level');
            })
            ->when(request('filter') === 'expired', function($query) {
                $query->where('expiry_status', 'expired');
            })
            ->when(request('filter') === 'near_expiry', function($query) {
                $query->where('expiry_status', 'near_expiry');
            })
            ->latest()
            ->paginate($perPage)
            ->appends(request()->query());

        $categories = Category::where('is_active', true)->get();

        return view('products.index', compact('products', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)->get();
        $units = \App\Models\Unit::where('is_active', true)->get();
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();
        return view('products.create', compact('categories', 'units', 'suppliers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'sku' => 'required|string|unique:products,sku',
            'barcode' => 'nullable|string|unique:products,barcode',
            'category_id' => 'required|exists:categories,id',
            'default_supplier_id' => 'nullable|exists:suppliers,id',
            'selling_price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'min_stock_level' => 'required|integer|min:0',
            'expiry_alert_days' => 'nullable|integer|min:1|max:365',
            'unit' => 'required|string|max:50',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->all();

        // توليد SKU تلقائياً إذا لم يتم إدخاله
        if (empty($data['sku'])) {
            $data['sku'] = $this->generateSKU($data['name_ar'], $data['category_id']);
        }

        // توليد Barcode تلقائياً إذا لم يتم إدخاله
        if (empty($data['barcode'])) {
            $data['barcode'] = $this->generateBarcode();
        }

        // رفع الصورة إذا تم اختيارها
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('products', 'public');
            $data['image'] = $imagePath;
        }

        Product::create($data);

        return redirect()->route('products.index')
            ->with('success', 'تم إضافة المنتج بنجاح');
    }

    /**
     * Generate unique SKU
     */
    private function generateSKU($productName, $categoryId = null)
    {
        $sku = '';

        // إضافة رمز التصنيف
        if ($categoryId) {
            $category = Category::find($categoryId);
            if ($category) {
                $sku .= strtoupper(substr($category->name_ar, 0, 2));
            }
        } else {
            $sku .= 'PR'; // Product
        }

        // إضافة أول حرفين من اسم المنتج
        $words = explode(' ', $productName);
        foreach (array_slice($words, 0, 2) as $word) {
            if (!empty($word)) {
                $sku .= strtoupper(substr($word, 0, 1));
            }
        }

        // إضافة رقم عشوائي والتأكد من عدم التكرار
        do {
            $randomNumber = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            $finalSku = $sku . $randomNumber;
        } while (Product::where('sku', $finalSku)->exists());

        return $finalSku;
    }

    /**
     * Generate unique barcode
     */
    private function generateBarcode()
    {
        do {
            // توليد باركود من 13 رقم (EAN-13)
            $barcode = '627'; // رمز البلد لليمن

            // إضافة 9 أرقام عشوائية
            for ($i = 0; $i < 9; $i++) {
                $barcode .= rand(0, 9);
            }

            // حساب رقم التحقق
            $sum = 0;
            for ($i = 0; $i < 12; $i++) {
                $sum += intval($barcode[$i]) * ($i % 2 === 0 ? 1 : 3);
            }
            $checkDigit = (10 - ($sum % 10)) % 10;
            $barcode .= $checkDigit;

        } while (Product::where('barcode', $barcode)->exists());

        return $barcode;
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load('category', 'saleItems.sale', 'purchaseItems.purchase');
        return view('products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $categories = Category::where('is_active', true)->get();
        $units = \App\Models\Unit::where('is_active', true)->get();
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();
        return view('products.edit', compact('product', 'categories', 'units', 'suppliers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'sku' => 'required|string|unique:products,sku,' . $product->id,
            'barcode' => 'nullable|string|unique:products,barcode,' . $product->id,
            'category_id' => 'required|exists:categories,id',
            'default_supplier_id' => 'nullable|exists:suppliers,id',
            'selling_price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'min_stock_level' => 'required|integer|min:0',
            'expiry_alert_days' => 'nullable|integer|min:1|max:365',
            'unit' => 'required|string|max:50',
        ]);

        $product->update($request->all());

        return redirect()->route('products.index')
            ->with('success', 'تم تحديث المنتج بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        if ($product->saleItems()->exists()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف المنتج لأنه مرتبط بمبيعات');
        }

        $product->delete();

        return redirect()->route('products.index')
            ->with('success', 'تم حذف المنتج بنجاح');
    }

    /**
     * Search products for API
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        $products = Product::where('is_active', true)
            ->where(function($q) use ($query) {
                $q->where('name_ar', 'like', "%{$query}%")
                  ->orWhere('name_en', 'like', "%{$query}%")
                  ->orWhere('sku', 'like', "%{$query}%")
                  ->orWhere('barcode', 'like', "%{$query}%");
            })
            ->with('category')
            ->limit(10)
            ->get();

        return response()->json($products);
    }

    /**
     * Find product by barcode
     */
    public function findByBarcode($barcode)
    {
        $product = Product::where('barcode', $barcode)
            ->where('is_active', true)
            ->with('category')
            ->first();

        if (!$product) {
            return response()->json(['error' => 'المنتج غير موجود'], 404);
        }

        return response()->json($product);
    }

    /**
     * Generate SKU via API
     */
    public function generateSkuApi(Request $request)
    {
        $productName = $request->get('name');
        $categoryId = $request->get('category_id');

        if (!$productName) {
            return response()->json(['error' => 'اسم المنتج مطلوب'], 400);
        }

        $sku = $this->generateSKU($productName, $categoryId);

        return response()->json(['sku' => $sku]);
    }

    /**
     * Generate Barcode via API
     */
    public function generateBarcodeApi()
    {
        $barcode = $this->generateBarcode();

        return response()->json(['barcode' => $barcode]);
    }
}
