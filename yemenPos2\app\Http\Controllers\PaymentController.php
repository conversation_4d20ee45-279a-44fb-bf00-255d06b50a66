<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Sale;
use App\Models\Purchase;
use App\Models\Customer;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    /**
     * عرض قائمة المدفوعات
     */
    public function index(Request $request)
    {
        $query = Payment::with(['customer', 'supplier', 'user', 'payable']);

        // فلتر البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('payment_number', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('supplier', function($supplierQuery) use ($search) {
                      $supplierQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // فلتر التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        // فلتر طريقة الدفع
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // فلتر نوع الدفعة
        if ($request->filled('payment_type')) {
            $query->where('payment_type', $request->payment_type);
        }

        // فلتر الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $payments = $query->latest('payment_date')->paginate(20);
        $payments->appends($request->query());

        // إحصائيات
        $totalPayments = $query->sum('amount');
        $totalCount = $query->count();
        $cashPayments = $query->where('payment_method', 'نقدي')->sum('amount');
        $bankPayments = $query->where('payment_method', 'تحويل بنكي')->sum('amount');

        return view('payments.index', compact(
            'payments', 'totalPayments', 'totalCount', 'cashPayments', 'bankPayments'
        ));
    }

    /**
     * عرض نموذج إنشاء دفعة جديدة
     */
    public function create(Request $request)
    {
        $customers = Customer::where('is_active', true)->orderBy('name')->get();
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();

        // إذا كان هناك sale_id أو purchase_id في الطلب
        $sale = $request->sale_id ? Sale::find($request->sale_id) : null;
        $purchase = $request->purchase_id ? Purchase::find($request->purchase_id) : null;

        return view('payments.create', compact('customers', 'suppliers', 'sale', 'purchase'));
    }

    /**
     * حفظ دفعة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'payable_type' => 'required|in:App\Models\Sale,App\Models\Purchase',
            'payable_id' => 'required|integer',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:نقدي,تحويل بنكي,شيك,بطاقة ائتمان',
            'payment_type' => 'required|in:دفعة,استرداد',
            'payment_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'check_number' => 'nullable|string|max:255',
            'check_date' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:مؤكد,معلق,ملغي'
        ]);

        try {
            DB::beginTransaction();

            // إنشاء الدفعة
            $payment = Payment::create([
                'payment_number' => Payment::generatePaymentNumber(),
                'payable_type' => $request->payable_type,
                'payable_id' => $request->payable_id,
                'customer_id' => $request->customer_id,
                'supplier_id' => $request->supplier_id,
                'user_id' => auth()->id(),
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'payment_type' => $request->payment_type,
                'payment_date' => $request->payment_date,
                'reference_number' => $request->reference_number,
                'bank_name' => $request->bank_name,
                'check_number' => $request->check_number,
                'check_date' => $request->check_date,
                'notes' => $request->notes,
                'status' => $request->status
            ]);

            // تحديث حالة الفاتورة إذا كانت الدفعة مؤكدة
            if ($payment->status === 'مؤكد') {
                $this->updatePayableStatus($payment);
            }

            DB::commit();

            return redirect()->route('payments.index')
                ->with('success', 'تم إنشاء الدفعة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطأ في إنشاء الدفعة: ' . $e->getMessage()]);
        }
    }

    /**
     * عرض تفاصيل دفعة
     */
    public function show(Payment $payment)
    {
        $payment->load(['customer', 'supplier', 'user', 'payable']);
        return view('payments.show', compact('payment'));
    }

    /**
     * عرض نموذج تعديل دفعة
     */
    public function edit(Payment $payment)
    {
        $customers = Customer::where('is_active', true)->orderBy('name')->get();
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();

        return view('payments.edit', compact('payment', 'customers', 'suppliers'));
    }

    /**
     * تحديث دفعة
     */
    public function update(Request $request, Payment $payment)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:نقدي,تحويل بنكي,شيك,بطاقة ائتمان',
            'payment_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'check_number' => 'nullable|string|max:255',
            'check_date' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:مؤكد,معلق,ملغي'
        ]);

        try {
            DB::beginTransaction();

            $oldStatus = $payment->status;

            $payment->update([
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'payment_date' => $request->payment_date,
                'reference_number' => $request->reference_number,
                'bank_name' => $request->bank_name,
                'check_number' => $request->check_number,
                'check_date' => $request->check_date,
                'notes' => $request->notes,
                'status' => $request->status
            ]);

            // إعادة حساب حالة الفاتورة إذا تغيرت الحالة
            if ($oldStatus !== $payment->status) {
                $this->updatePayableStatus($payment);
            }

            DB::commit();

            return redirect()->route('payments.index')
                ->with('success', 'تم تحديث الدفعة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطأ في تحديث الدفعة: ' . $e->getMessage()]);
        }
    }

    /**
     * حذف دفعة
     */
    public function destroy(Payment $payment)
    {
        try {
            DB::beginTransaction();

            // حفظ معلومات الفاتورة قبل الحذف
            $payable = $payment->payable;

            $payment->delete();

            // إعادة حساب حالة الفاتورة
            if ($payable) {
                $this->recalculatePayableStatus($payable);
            }

            DB::commit();

            return redirect()->route('payments.index')
                ->with('success', 'تم حذف الدفعة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطأ في حذف الدفعة: ' . $e->getMessage()]);
        }
    }

    /**
     * تحديث حالة الفاتورة بناءً على المدفوعات
     */
    private function updatePayableStatus(Payment $payment)
    {
        $payable = $payment->payable;
        if (!$payable) return;

        $this->recalculatePayableStatus($payable);
    }

    /**
     * إعادة حساب حالة الفاتورة
     */
    private function recalculatePayableStatus($payable)
    {
        $totalPayments = $payable->payments()->where('status', 'مؤكد')->sum('amount');

        if ($totalPayments >= $payable->total_amount) {
            $payable->update([
                'status' => 'مكتملة',
                'paid_amount' => $payable->total_amount,
                'remaining_amount' => 0
            ]);
        } elseif ($totalPayments > 0) {
            $payable->update([
                'status' => 'دفع جزئي',
                'paid_amount' => $totalPayments,
                'remaining_amount' => $payable->total_amount - $totalPayments
            ]);
        } else {
            $payable->update([
                'status' => 'معلقة',
                'paid_amount' => 0,
                'remaining_amount' => $payable->total_amount
            ]);
        }
    }
}
