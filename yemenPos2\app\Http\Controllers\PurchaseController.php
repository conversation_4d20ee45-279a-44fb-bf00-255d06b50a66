<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\PurchaseItem;
use App\Models\Product;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PurchaseController extends Controller
{
    /**
     * عرض قائمة المشتريات
     */
    public function index()
    {
        $perPage = request('per_page', 20);
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 20;

        $purchases = Purchase::with(['supplier', 'user', 'purchaseItems'])
            ->when(request('search'), function($query, $search) {
                $query->where('purchase_number', 'like', "%{$search}%")
                      ->orWhereHas('supplier', function($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%")
                            ->orWhere('company_name', 'like', "%{$search}%");
                      });
            })
            ->when(request('date_from'), function($query, $dateFrom) {
                $query->whereDate('purchase_date', '>=', $dateFrom);
            })
            ->when(request('date_to'), function($query, $dateTo) {
                $query->whereDate('purchase_date', '<=', $dateTo);
            })
            ->when(request('status'), function($query, $status) {
                $query->where('status', $status);
            })
            ->orderBy('purchase_date', 'desc')
            ->paginate($perPage)
            ->appends(request()->query());

        return view('purchases.index', compact('purchases'));
    }

    /**
     * عرض نموذج إنشاء مشترى جديد
     */
    public function create()
    {
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();
        $products = Product::where('is_active', true)->with('category')->orderBy('name_ar')->get();

        return view('purchases.create', compact('suppliers', 'products'));
    }

    /**
     * حفظ مشترى جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_date' => 'required|date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.purchase_price' => 'required|numeric|min:0',
            'items.*.expiry_date' => 'nullable|date|after:today',
            'items.*.production_date' => 'nullable|date|before_or_equal:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // إنشاء رقم المشترى
            $purchaseNumber = Purchase::generatePurchaseNumber();

            // حساب الإجمالي
            $totalAmount = 0;
            foreach ($request->items as $item) {
                $totalAmount += $item['quantity'] * $item['purchase_price'];
            }

            // إنشاء المشترى
            $purchase = Purchase::create([
                'purchase_number' => $purchaseNumber,
                'supplier_id' => $request->supplier_id,
                'user_id' => 1, // auth()->id(),
                'purchase_date' => $request->purchase_date,
                'total_amount' => $totalAmount,
                'status' => 'received',
                'notes' => $request->notes,
                'paid_amount' => 0,
                'remaining_amount' => $totalAmount,
                'payment_status' => 'unpaid'
            ]);

            // إضافة عناصر المشترى (الدفعات)
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);

                // إنشاء رقم دفعة فريد
                $batchNumber = PurchaseItem::generateBatchNumber($item['product_id']);

                // إنشاء عنصر المشترى (الدفعة)
                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'purchase_price' => $item['purchase_price'],
                    'total_price' => $item['quantity'] * $item['purchase_price'],
                    'expiry_date' => $item['expiry_date'] ?? null,
                    'production_date' => $item['production_date'] ?? null,
                    'batch_number' => $batchNumber,
                    'remaining_quantity' => $item['quantity'],
                    'batch_status' => 'active',
                    'notes' => null
                ]);

                // تحديث سعر البيع للمنتج بناءً على آخر دفعة
                $profitMargin = $product->profit_margin ?? 20;
                $newSellingPrice = $item['purchase_price'] * (1 + ($profitMargin / 100));

                $product->update([
                    'selling_price' => $newSellingPrice
                ]);
            }

            DB::commit();

            return redirect()->route('purchases.show', $purchase)
                ->with('success', 'تم إنشاء المشترى بنجاح وتم إنشاء الدفعات للمنتجات');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطأ في إنشاء المشترى: ' . $e->getMessage()])
                         ->withInput();
        }
    }

    /**
     * عرض تفاصيل مشترى
     */
    public function show(Purchase $purchase)
    {
        $purchase->load(['supplier', 'user', 'purchaseItems.product']);
        return view('purchases.show', compact('purchase'));
    }

    /**
     * عرض نموذج تعديل مشترى
     */
    public function edit(Purchase $purchase)
    {
        $suppliers = Supplier::where('status', 'نشط')->get();
        $products = Product::where('status', 'نشط')->get();
        $purchase->load(['purchaseItems.product']);

        return view('purchases.edit', compact('purchase', 'suppliers', 'products'));
    }

    /**
     * تحديث مشترى
     */
    public function update(Request $request, Purchase $purchase)
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_date' => 'required|date',
            'notes' => 'nullable|string',
        ]);

        try {
            $purchase->update([
                'supplier_id' => $request->supplier_id,
                'purchase_date' => $request->purchase_date,
                'notes' => $request->notes,
            ]);

            return redirect()->route('purchases.show', $purchase)
                ->with('success', 'تم تحديث المشترى بنجاح');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطأ في تحديث المشترى: ' . $e->getMessage()]);
        }
    }

    /**
     * حذف مشترى
     */
    public function destroy(Purchase $purchase)
    {
        try {
            DB::beginTransaction();

            // إرجاع المخزون
            foreach ($purchase->purchaseItems as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $product->decrement('stock', $item->quantity);
                }
            }

            // حذف عناصر المشترى
            $purchase->purchaseItems()->delete();

            // حذف المشترى
            $purchase->delete();

            DB::commit();

            return redirect()->route('purchases.index')
                ->with('success', 'تم حذف المشترى بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطأ في حذف المشترى: ' . $e->getMessage()]);
        }
    }

    /**
     * API: الحصول على المشتريات غير المدفوعة
     */
    public function getUnpaidPurchases()
    {
        $purchases = Purchase::with(['supplier'])
            ->where(function($q) {
                $q->where('status', 'دفع جزئي')
                  ->orWhere('remaining_amount', '>', 0);
            })
            ->select('id', 'purchase_number', 'supplier_id', 'total_amount', 'paid_amount', 'remaining_amount')
            ->get();

        return response()->json($purchases);
    }
}
