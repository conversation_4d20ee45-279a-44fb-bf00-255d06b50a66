<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\PurchaseItem;
use App\Models\Product;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PurchaseController extends Controller
{
    /**
     * عرض قائمة المشتريات
     */
    public function index()
    {
        $purchases = Purchase::with(['supplier', 'user'])
            ->orderBy('purchase_date', 'desc')
            ->paginate(20);

        return view('purchases.index', compact('purchases'));
    }

    /**
     * عرض نموذج إنشاء مشترى جديد
     */
    public function create()
    {
        $suppliers = Supplier::where('status', 'نشط')->get();
        $products = Product::where('status', 'نشط')->get();

        return view('purchases.create', compact('suppliers', 'products'));
    }

    /**
     * حفظ مشترى جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_date' => 'required|date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.unit_cost' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // إنشاء رقم المشترى
            $purchaseNumber = 'PUR-' . date('Y') . '-' . str_pad(Purchase::count() + 1, 6, '0', STR_PAD_LEFT);

            // حساب المجاميع
            $subtotal = 0;
            foreach ($request->items as $item) {
                $subtotal += $item['quantity'] * $item['unit_cost'];
            }

            $discountAmount = $request->discount_amount ?? 0;
            $taxAmount = $request->tax_amount ?? 0;
            $totalAmount = $subtotal - $discountAmount + $taxAmount;

            // إنشاء المشترى
            $purchase = Purchase::create([
                'purchase_number' => $purchaseNumber,
                'supplier_id' => $request->supplier_id,
                'user_id' => auth()->id(),
                'purchase_date' => $request->purchase_date,
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'status' => 'مكتملة',
                'notes' => $request->notes,
            ]);

            // إضافة عناصر المشترى
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);

                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'unit_cost' => $item['unit_cost'],
                    'total_cost' => $item['quantity'] * $item['unit_cost'],
                    'unit' => $product->unit->name ?? 'قطعة',
                ]);

                // تحديث مخزون المنتج
                $product->increment('stock', $item['quantity']);
            }

            DB::commit();

            return redirect()->route('purchases.show', $purchase)
                ->with('success', 'تم إنشاء المشترى بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطأ في إنشاء المشترى: ' . $e->getMessage()]);
        }
    }

    /**
     * عرض تفاصيل مشترى
     */
    public function show(Purchase $purchase)
    {
        $purchase->load(['supplier', 'user', 'purchaseItems.product']);
        return view('purchases.show', compact('purchase'));
    }

    /**
     * عرض نموذج تعديل مشترى
     */
    public function edit(Purchase $purchase)
    {
        $suppliers = Supplier::where('status', 'نشط')->get();
        $products = Product::where('status', 'نشط')->get();
        $purchase->load(['purchaseItems.product']);

        return view('purchases.edit', compact('purchase', 'suppliers', 'products'));
    }

    /**
     * تحديث مشترى
     */
    public function update(Request $request, Purchase $purchase)
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_date' => 'required|date',
            'notes' => 'nullable|string',
        ]);

        try {
            $purchase->update([
                'supplier_id' => $request->supplier_id,
                'purchase_date' => $request->purchase_date,
                'notes' => $request->notes,
            ]);

            return redirect()->route('purchases.show', $purchase)
                ->with('success', 'تم تحديث المشترى بنجاح');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطأ في تحديث المشترى: ' . $e->getMessage()]);
        }
    }

    /**
     * حذف مشترى
     */
    public function destroy(Purchase $purchase)
    {
        try {
            DB::beginTransaction();

            // إرجاع المخزون
            foreach ($purchase->purchaseItems as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $product->decrement('stock', $item->quantity);
                }
            }

            // حذف عناصر المشترى
            $purchase->purchaseItems()->delete();

            // حذف المشترى
            $purchase->delete();

            DB::commit();

            return redirect()->route('purchases.index')
                ->with('success', 'تم حذف المشترى بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطأ في حذف المشترى: ' . $e->getMessage()]);
        }
    }

    /**
     * API: الحصول على المشتريات غير المدفوعة
     */
    public function getUnpaidPurchases()
    {
        $purchases = Purchase::with(['supplier'])
            ->where(function($q) {
                $q->where('status', 'دفع جزئي')
                  ->orWhere('remaining_amount', '>', 0);
            })
            ->select('id', 'purchase_number', 'supplier_id', 'total_amount', 'paid_amount', 'remaining_amount')
            ->get();

        return response()->json($purchases);
    }
}
