@extends('layouts.app')

@section('title', 'عرض العميل - ' . $customer->name)
@section('page-title', 'عرض العميل')

@push('styles')
<style>
    .customer-details-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 200px);
        padding: 30px 0;
    }
    
    .details-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .details-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }
    
    .details-header::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }
    
    .customer-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        font-weight: bold;
        margin: 0 auto 20px;
        border: 5px solid rgba(255,255,255,0.3);
    }
    
    .info-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 2px solid #e9ecef;
    }
    
    .section-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }
    
    .section-title i {
        margin-left: 10px;
        font-size: 1.2rem;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
    }
    
    .info-label i {
        margin-left: 8px;
        color: #667eea;
    }
    
    .info-value {
        color: #212529;
        font-weight: 500;
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .status-active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .sales-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .btn-action {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 0 5px;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .contact-link {
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
    }
    
    .contact-link:hover {
        color: #667eea;
        transform: scale(1.05);
    }
</style>
@endpush

@section('content')
<div class="customer-details-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- معلومات العميل -->
                <div class="details-card">
                    <div class="details-header">
                        <div class="customer-avatar">
                            {{ substr($customer->name, 0, 1) }}
                        </div>
                        <h2 class="mb-2">{{ $customer->name }}</h2>
                        <p class="mb-0 opacity-75">عميل #{{ $customer->id }}</p>
                    </div>
                    <div class="p-4">
                        <!-- المعلومات الأساسية -->
                        <div class="info-section">
                            <h6 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-user"></i>
                                    اسم العميل
                                </div>
                                <div class="info-value">{{ $customer->name }}</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-phone"></i>
                                    رقم الهاتف
                                </div>
                                <div class="info-value">
                                    <a href="tel:{{ $customer->phone }}" class="contact-link">
                                        {{ $customer->phone }}
                                    </a>
                                </div>
                            </div>
                            
                            @if($customer->email)
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-envelope"></i>
                                    البريد الإلكتروني
                                </div>
                                <div class="info-value">
                                    <a href="mailto:{{ $customer->email }}" class="contact-link">
                                        {{ $customer->email }}
                                    </a>
                                </div>
                            </div>
                            @endif
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-toggle-on"></i>
                                    الحالة
                                </div>
                                <div class="info-value">
                                    @if($customer->is_active)
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle me-1"></i>
                                            نشط
                                        </span>
                                    @else
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-times-circle me-1"></i>
                                            غير نشط
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        @if($customer->address)
                        <!-- العنوان -->
                        <div class="info-section">
                            <h6 class="section-title">
                                <i class="fas fa-map-marker-alt"></i>
                                العنوان
                            </h6>
                            <p class="mb-0 text-muted">{{ $customer->address }}</p>
                        </div>
                        @endif
                        
                        @if($customer->notes)
                        <!-- الملاحظات -->
                        <div class="info-section">
                            <h6 class="section-title">
                                <i class="fas fa-sticky-note"></i>
                                الملاحظات
                            </h6>
                            <p class="mb-0 text-muted">{{ $customer->notes }}</p>
                        </div>
                        @endif
                        
                        <!-- معلومات النظام -->
                        <div class="info-section">
                            <h6 class="section-title">
                                <i class="fas fa-clock"></i>
                                معلومات النظام
                            </h6>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar-plus"></i>
                                    تاريخ التسجيل
                                </div>
                                <div class="info-value">{{ $customer->created_at->format('Y-m-d') }}</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar-edit"></i>
                                    آخر تحديث
                                </div>
                                <div class="info-value">{{ $customer->updated_at->format('Y-m-d') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- الإجراءات -->
                <div class="details-card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            الإجراءات
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <a href="{{ route('customers.edit', $customer) }}" class="btn btn-warning btn-action">
                            <i class="fas fa-edit me-2"></i>
                            تعديل العميل
                        </a>
                        
                        <a href="{{ route('customers.index') }}" class="btn btn-secondary btn-action">
                            <i class="fas fa-list me-2"></i>
                            قائمة العملاء
                        </a>
                        
                        <button type="button" class="btn btn-success btn-action">
                            <i class="fas fa-plus me-2"></i>
                            مبيعة جديدة
                        </button>
                        
                        <button type="button" class="btn btn-danger btn-action" 
                                onclick="deleteCustomer({{ $customer->id }})">
                            <i class="fas fa-trash me-2"></i>
                            حذف العميل
                        </button>
                    </div>
                </div>
                
                <!-- إحصائيات -->
                <div class="details-card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            الإحصائيات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <div class="display-4 text-primary fw-bold">{{ $customer->sales_count }}</div>
                            <p class="text-muted mb-0">إجمالي المبيعات</p>
                        </div>
                        
                        <hr>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="h5 text-success mb-0">0</div>
                                <small class="text-muted">مبيعات هذا الشهر</small>
                            </div>
                            <div class="col-6">
                                <div class="h5 text-info mb-0">0 ر.ي</div>
                                <small class="text-muted">إجمالي المبلغ</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- اتصال سريع -->
                <div class="details-card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-phone me-2"></i>
                            اتصال سريع
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <a href="tel:{{ $customer->phone }}" class="btn btn-success btn-action w-100 mb-2">
                            <i class="fas fa-phone me-2"></i>
                            اتصال هاتفي
                        </a>
                        
                        <a href="https://wa.me/{{ $customer->phone }}" target="_blank" class="btn btn-success btn-action w-100 mb-2">
                            <i class="fab fa-whatsapp me-2"></i>
                            رسالة واتساب
                        </a>
                        
                        @if($customer->email)
                        <a href="mailto:{{ $customer->email }}" class="btn btn-primary btn-action w-100">
                            <i class="fas fa-envelope me-2"></i>
                            إرسال بريد
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        @if($customer->sales->count() > 0)
        <!-- المبيعات الأخيرة -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="sales-table">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-shopping-bag me-2"></i>
                            المبيعات الأخيرة ({{ $customer->sales_count }})
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($customer->sales as $sale)
                                    <tr>
                                        <td>
                                            <span class="fw-bold">#{{ $sale->invoice_number }}</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $sale->sale_date }}</small>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">{{ number_format($sale->total_amount, 2) }} ر.ي</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $sale->payment_method }}</span>
                                        </td>
                                        <td>
                                            <span class="badge 
                                                @if($sale->status === 'مكتملة') bg-success
                                                @elseif($sale->status === 'معلقة') bg-warning
                                                @else bg-danger
                                                @endif">
                                                {{ $sale->status }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('sales.show', $sale) }}" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>هل أنت متأكد من حذف هذا العميل؟</h5>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                    @if($customer->sales_count > 0)
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            تحذير: هناك {{ $customer->sales_count }} مبيعة مسجلة لهذا العميل
                        </div>
                    @endif
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف نهائياً</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteCustomer(customerId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/customers/${customerId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endpush
