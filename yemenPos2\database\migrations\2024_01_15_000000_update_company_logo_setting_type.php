<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تحديث نوع حقل شعار الشركة من string إلى file
        Setting::where('key', 'company_logo')->update(['type' => 'file']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إرجاع نوع الحقل إلى string
        Setting::where('key', 'company_logo')->update(['type' => 'string']);
    }
};
