<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ $sale->invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .invoice-header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .company-info {
            color: #666;
            font-size: 12px;
        }
        
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .invoice-info, .customer-info {
            flex: 1;
            min-width: 250px;
            margin-bottom: 20px;
        }
        
        .invoice-info {
            text-align: left;
        }
        
        .customer-info {
            text-align: right;
        }
        
        .info-title {
            font-weight: bold;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 1px solid #ddd;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .items-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .totals-section {
            margin-left: auto;
            width: 300px;
            border: 1px solid #ddd;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .total-row:last-child {
            border-bottom: none;
            background: #2c3e50;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .payment-info {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .payment-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status-complete {
            background: #d4edda;
            color: #155724;
        }
        
        .status-partial {
            background: #fff3cd;
            color: #856404;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .invoice-container {
                padding: 0;
                max-width: none;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-btn no-print" onclick="window.print()">
        🖨️ طباعة
    </button>
    
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="company-name">نظام نقطة البيع</div>
            <div class="company-info">
                العنوان: صنعاء، اليمن | الهاتف: +*********** 789 | البريد: <EMAIL>
            </div>
        </div>
        
        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="customer-info">
                <div class="info-title">بيانات العميل</div>
                <div class="info-row">
                    <span>العميل:</span>
                    <span>{{ $sale->customer ? $sale->customer->name : 'عميل عادي' }}</span>
                </div>
                @if($sale->customer)
                <div class="info-row">
                    <span>الهاتف:</span>
                    <span>{{ $sale->customer->phone ?? 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span>العنوان:</span>
                    <span>{{ $sale->customer->address ?? 'غير محدد' }}</span>
                </div>
                @endif
            </div>
            
            <div class="invoice-info">
                <div class="info-title">بيانات الفاتورة</div>
                <div class="info-row">
                    <span>رقم الفاتورة:</span>
                    <span>{{ $sale->invoice_number }}</span>
                </div>
                <div class="info-row">
                    <span>التاريخ:</span>
                    <span>{{ $sale->sale_date->format('Y-m-d') }}</span>
                </div>
                <div class="info-row">
                    <span>الوقت:</span>
                    <span>{{ $sale->sale_time }}</span>
                </div>
                <div class="info-row">
                    <span>الكاشير:</span>
                    <span>{{ $sale->user->name ?? 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span>الحالة:</span>
                    <span class="payment-status {{ $sale->status === 'مكتملة' ? 'status-complete' : 'status-partial' }}">
                        {{ $sale->status }}
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكود</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                @foreach($sale->saleItems as $index => $item)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $item->product_name }}</td>
                    <td>{{ $item->product_sku }}</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ $item->unit }}</td>
                    <td>{{ number_format($item->unit_price, 2) }} ر.ي</td>
                    <td>{{ number_format($item->total_price, 2) }} ر.ي</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        
        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>{{ number_format($sale->subtotal, 2) }} ر.ي</span>
            </div>
            @if($sale->discount_amount > 0)
            <div class="total-row">
                <span>الخصم:</span>
                <span>{{ number_format($sale->discount_amount, 2) }} ر.ي</span>
            </div>
            @endif
            @if($sale->tax_amount > 0)
            <div class="total-row">
                <span>الضريبة:</span>
                <span>{{ number_format($sale->tax_amount, 2) }} ر.ي</span>
            </div>
            @endif
            <div class="total-row">
                <span>الإجمالي:</span>
                <span>{{ number_format($sale->total_amount, 2) }} ر.ي</span>
            </div>
        </div>
        
        <!-- Payment Info -->
        <div class="payment-info">
            <div class="info-row">
                <span><strong>طريقة الدفع:</strong></span>
                <span>{{ $sale->payment_method }}</span>
            </div>
            <div class="info-row">
                <span><strong>المبلغ المدفوع:</strong></span>
                <span>{{ number_format($sale->paid_amount ?? $sale->total_amount, 2) }} ر.ي</span>
            </div>
            @if($sale->remaining_amount > 0)
            <div class="info-row">
                <span><strong>المبلغ المتبقي:</strong></span>
                <span style="color: #dc3545; font-weight: bold;">{{ number_format($sale->remaining_amount, 2) }} ر.ي</span>
            </div>
            @endif
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام نقطة البيع - {{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            // تأخير قصير للسماح بتحميل الصفحة
            setTimeout(() => {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
