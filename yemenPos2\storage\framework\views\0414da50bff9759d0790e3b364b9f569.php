<?php $__env->startSection('title', 'الفواتير المرتجعة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-undo me-2 text-secondary"></i>
                        الفواتير المرتجعة
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('sales.index')); ?>">المبيعات</a></li>
                            <li class="breadcrumb-item active">الفواتير المرتجعة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-outline-primary me-2">
                        <i class="fas fa-list me-2"></i>جميع المبيعات
                    </a>
                    <a href="<?php echo e(route('sales.unposted')); ?>" class="btn btn-outline-warning me-2">
                        <i class="fas fa-clock me-2"></i>غير المرحلة
                    </a>
                    <a href="<?php echo e(route('sales.posted')); ?>" class="btn btn-outline-success">
                        <i class="fas fa-check me-2"></i>المرحلة
                    </a>
                </div>
            </div>

            <!-- Alert -->
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>معلومة:</strong> هذه الصفحة تعرض الفواتير المرتجعة فقط. تم إعادة المخزون لهذه الفواتير.
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3><?php echo e($countRefunded); ?></h3>
                            <p class="mb-0">إجمالي الفواتير المرتجعة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <h3><?php echo e(number_format($totalRefunded, 0)); ?></h3>
                            <p class="mb-0">إجمالي قيمة المرتجعات (ر.ي)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('sales.refunded')); ?>">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">البحث</label>
                                    <input type="text" class="form-control" name="search"
                                           value="<?php echo e(request('search')); ?>"
                                           placeholder="رقم الفاتورة أو اسم العميل">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="date_from"
                                           value="<?php echo e(request('date_from')); ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="date_to"
                                           value="<?php echo e(request('date_to')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">العميل</label>
                                    <select class="form-select" name="customer_id">
                                        <option value="">جميع العملاء</option>
                                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($customer->id); ?>"
                                                    <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                                <?php echo e($customer->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php if(request()->hasAny(['search', 'date_from', 'date_to', 'customer_id'])): ?>
                            <div class="row">
                                <div class="col-12">
                                    <a href="<?php echo e(route('sales.refunded')); ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times me-2"></i>مسح الفلاتر
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <!-- Sales Table -->
            <?php if($sales->count() > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-undo me-2"></i>
                            الفواتير المرتجعة (<?php echo e($sales->total()); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>تاريخ الفاتورة</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>تاريخ الاسترجاع</th>
                                        <th>المستخدم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo e(route('sales.show', $sale)); ?>" class="text-decoration-none fw-bold">
                                                <?php echo e($sale->invoice_number); ?>

                                            </a>
                                            <br>
                                            <small class="text-secondary">
                                                <i class="fas fa-undo me-1"></i>مرتجعة
                                            </small>
                                        </td>
                                        <td>
                                            <?php if($sale->customer): ?>
                                                <div>
                                                    <span class="fw-bold"><?php echo e($sale->customer->name); ?></span>
                                                    <br>
                                                    <small class="text-muted"><?php echo e($sale->customer->phone); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">عميل نقدي</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div>
                                                <?php echo e($sale->sale_date->format('Y-m-d')); ?>

                                                <br>
                                                <small class="text-muted">
                                                    <?php echo e($sale->sale_time->format('g:i')); ?>

                                                    <?php echo e($sale->sale_time->format('A') == 'AM' ? 'ص' : 'م'); ?>

                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-secondary">
                                                <?php echo e(number_format($sale->total_amount, 2)); ?> ر.ي
                                            </span>
                                        </td>
                                        <td>
                                            <div>
                                                <?php echo e($sale->updated_at->format('Y-m-d')); ?>

                                                <br>
                                                <small class="text-muted">
                                                    <?php echo e($sale->updated_at->format('g:i')); ?>

                                                    <?php echo e($sale->updated_at->format('A') == 'AM' ? 'ص' : 'م'); ?>

                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($sale->user->name); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('sales.show', $sale)); ?>"
                                                   class="btn btn-sm btn-outline-info"
                                                   title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('sales.print', $sale)); ?>"
                                                   class="btn btn-sm btn-outline-secondary"
                                                   title="طباعة"
                                                   target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>

                                                <?php if(auth()->user()->hasRole('admin')): ?>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            title="حذف نهائي"
                                                            onclick="forceDeleteSale(<?php echo e($sale->id); ?>)">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="صفحات الفواتير المرتجعة">
                                <?php echo e($sales->links('pagination::bootstrap-4')); ?>

                            </nav>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-smile fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز! لا توجد فواتير مرتجعة</h5>
                    <p class="text-muted">لم يتم العثور على فواتير مرتجعة تطابق معايير البحث</p>
                    <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للمبيعات
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Force Delete Confirmation Modal -->
<div class="modal fade" id="forceDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف النهائي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>
                هل أنت متأكد من حذف هذه الفاتورة المرتجعة نهائياً؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="forceDeleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف نهائي</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.alert {
    border-left: 4px solid #6c757d;
}

.card h3 {
    font-size: 2rem;
    font-weight: bold;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function forceDeleteSale(saleId) {
    const forceDeleteForm = document.getElementById('forceDeleteForm');
    forceDeleteForm.action = `/sales/${saleId}/force-delete`;

    const forceDeleteModal = new bootstrap.Modal(document.getElementById('forceDeleteModal'));
    forceDeleteModal.show();
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/sales/refunded.blade.php ENDPATH**/ ?>