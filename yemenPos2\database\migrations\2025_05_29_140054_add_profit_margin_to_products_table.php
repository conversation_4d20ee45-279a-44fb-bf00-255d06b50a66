<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // إضافة نسبة الربح
            $table->decimal('profit_margin', 5, 2)->default(20.00)->after('wholesale_price');

            // إضافة أيام التنبيه لانتهاء الصلاحية
            $table->integer('expiry_alert_days')->default(30)->after('profit_margin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn(['profit_margin', 'expiry_alert_days']);
        });
    }
};
