@extends('layouts.app')

@section('title', 'تعديل المشترى - ' . $purchase->purchase_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المشترى {{ $purchase->purchase_number }}
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('purchases.index') }}">المشتريات</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('purchases.show', $purchase) }}">{{ $purchase->purchase_number }}</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('purchases.show', $purchase) }}" class="btn btn-info">
                        <i class="fas fa-eye me-2"></i>عرض
                    </a>
                    <a href="{{ route('purchases.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <form action="{{ route('purchases.update', $purchase) }}" method="POST" id="purchaseForm">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <!-- Purchase Info -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات المشترى
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم المشترى</label>
                                    <input type="text" class="form-control" value="{{ $purchase->purchase_number }}" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المورد <span class="text-danger">*</span></label>
                                    <select name="supplier_id" class="form-select" required>
                                        <option value="">اختر المورد</option>
                                        @foreach($suppliers as $supplier)
                                            <option value="{{ $supplier->id }}" 
                                                {{ $purchase->supplier_id == $supplier->id ? 'selected' : '' }}>
                                                {{ $supplier->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ المشترى <span class="text-danger">*</span></label>
                                    <input type="date" name="purchase_date" class="form-control" 
                                           value="{{ $purchase->purchase_date->format('Y-m-d') }}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="ملاحظات إضافية...">{{ $purchase->notes }}</textarea>
                                </div>

                                <!-- Current Totals (Read Only) -->
                                <div class="border-top pt-3">
                                    <h6 class="text-muted mb-3">المجاميع الحالية</h6>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المجموع الفرعي:</span>
                                        <span>{{ number_format($purchase->subtotal, 2) }} ر.ي</span>
                                    </div>
                                    @if($purchase->discount_amount > 0)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الخصم:</span>
                                        <span class="text-danger">- {{ number_format($purchase->discount_amount, 2) }} ر.ي</span>
                                    </div>
                                    @endif
                                    @if($purchase->tax_amount > 0)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الضريبة:</span>
                                        <span>{{ number_format($purchase->tax_amount, 2) }} ر.ي</span>
                                    </div>
                                    @endif
                                    <div class="d-flex justify-content-between border-top pt-2">
                                        <strong>الإجمالي:</strong>
                                        <strong class="text-primary">{{ number_format($purchase->total_amount, 2) }} ر.ي</strong>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>حفظ التعديلات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Purchase Items (Read Only) -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>
                                    المنتجات ({{ $purchase->purchaseItems->count() }} صنف)
                                </h5>
                                <small class="text-muted">لا يمكن تعديل المنتجات بعد إنشاء المشترى</small>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>#</th>
                                                <th>المنتج</th>
                                                <th>الكود</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>سعر الشراء</th>
                                                <th>الإجمالي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($purchase->purchaseItems as $index => $item)
                                            <tr>
                                                <td>{{ $index + 1 }}</td>
                                                <td>
                                                    <div>
                                                        <strong>{{ $item->product_name }}</strong>
                                                        @if($item->product)
                                                            <br><small class="text-muted">المخزون الحالي: {{ $item->product->stock }}</small>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td>{{ $item->product_sku }}</td>
                                                <td>
                                                    <span class="badge bg-info">{{ $item->quantity }}</span>
                                                </td>
                                                <td>{{ $item->unit }}</td>
                                                <td>{{ number_format($item->unit_cost, 2) }} ر.ي</td>
                                                <td><strong>{{ number_format($item->total_cost, 2) }} ر.ي</strong></td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                        <tfoot class="table-light">
                                            <tr>
                                                <th colspan="6" class="text-end">الإجمالي:</th>
                                                <th>{{ number_format($purchase->purchaseItems->sum('total_cost'), 2) }} ر.ي</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                <!-- Note about editing items -->
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> لا يمكن تعديل المنتجات أو الكميات بعد إنشاء المشترى لضمان سلامة المخزون. 
                                    يمكنك فقط تعديل معلومات المشترى الأساسية مثل المورد والتاريخ والملاحظات.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}

.alert {
    border-radius: 0.375rem;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .card-body .row {
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush
@endsection
