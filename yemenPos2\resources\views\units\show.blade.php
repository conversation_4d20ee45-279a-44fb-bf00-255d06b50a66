@extends('layouts.app')

@section('title', 'عرض الوحدة - ' . $unit->name_ar)
@section('page-title', 'عرض الوحدة')

@push('styles')
<style>
    .unit-details-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 200px);
        padding: 30px 0;
    }
    
    .details-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .details-header {
        background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }
    
    .details-header::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }
    
    .info-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 2px solid #e9ecef;
    }
    
    .section-title {
        color: #17a2b8;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }
    
    .section-title i {
        margin-left: 10px;
        font-size: 1.2rem;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
    }
    
    .info-label i {
        margin-left: 8px;
        color: #17a2b8;
    }
    
    .info-value {
        color: #212529;
        font-weight: 500;
    }
    
    .symbol-display {
        background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
        color: white;
        border-radius: 10px;
        padding: 10px 15px;
        font-size: 1.2rem;
        font-weight: bold;
        display: inline-block;
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .status-active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .products-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .btn-action {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 0 5px;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
</style>
@endpush

@section('content')
<div class="unit-details-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- معلومات الوحدة -->
                <div class="details-card">
                    <div class="details-header">
                        <i class="fas fa-balance-scale fa-3x mb-3"></i>
                        <h2 class="mb-2">{{ $unit->name_ar }}</h2>
                        @if($unit->name_en)
                            <p class="mb-0 opacity-75">{{ $unit->name_en }}</p>
                        @endif
                    </div>
                    <div class="p-4">
                        <!-- المعلومات الأساسية -->
                        <div class="info-section">
                            <h6 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-tag"></i>
                                    اسم الوحدة (عربي)
                                </div>
                                <div class="info-value">{{ $unit->name_ar }}</div>
                            </div>
                            
                            @if($unit->name_en)
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-globe"></i>
                                    اسم الوحدة (إنجليزي)
                                </div>
                                <div class="info-value">{{ $unit->name_en }}</div>
                            </div>
                            @endif
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-code"></i>
                                    رمز الوحدة
                                </div>
                                <div class="info-value">
                                    @if($unit->symbol)
                                        <span class="symbol-display">{{ $unit->symbol }}</span>
                                    @else
                                        <span class="text-muted">لا يوجد رمز</span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-toggle-on"></i>
                                    الحالة
                                </div>
                                <div class="info-value">
                                    @if($unit->is_active)
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle me-1"></i>
                                            نشط
                                        </span>
                                    @else
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-times-circle me-1"></i>
                                            غير نشط
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        @if($unit->description)
                        <!-- الوصف -->
                        <div class="info-section">
                            <h6 class="section-title">
                                <i class="fas fa-align-left"></i>
                                الوصف
                            </h6>
                            <p class="mb-0 text-muted">{{ $unit->description }}</p>
                        </div>
                        @endif
                        
                        <!-- معلومات النظام -->
                        <div class="info-section">
                            <h6 class="section-title">
                                <i class="fas fa-clock"></i>
                                معلومات النظام
                            </h6>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar-plus"></i>
                                    تاريخ الإنشاء
                                </div>
                                <div class="info-value">{{ $unit->created_at->format('Y-m-d') }}</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar-edit"></i>
                                    آخر تحديث
                                </div>
                                <div class="info-value">{{ $unit->updated_at->format('Y-m-d') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- الإجراءات -->
                <div class="details-card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            الإجراءات
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <a href="{{ route('units.edit', $unit) }}" class="btn btn-warning btn-action">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الوحدة
                        </a>
                        
                        <a href="{{ route('units.index') }}" class="btn btn-secondary btn-action">
                            <i class="fas fa-list me-2"></i>
                            قائمة الوحدات
                        </a>
                        
                        <button type="button" class="btn btn-danger btn-action" 
                                onclick="deleteUnit({{ $unit->id }})">
                            <i class="fas fa-trash me-2"></i>
                            حذف الوحدة
                        </button>
                    </div>
                </div>
                
                <!-- إحصائيات -->
                <div class="details-card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            الإحصائيات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <div class="display-4 text-primary fw-bold">{{ $unit->products_count }}</div>
                            <p class="text-muted mb-0">منتج يستخدم هذه الوحدة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        @if($unit->products->count() > 0)
        <!-- المنتجات التي تستخدم هذه الوحدة -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="products-table">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            المنتجات التي تستخدم هذه الوحدة ({{ $unit->products_count }})
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المنتج</th>
                                        <th>التصنيف</th>
                                        <th>السعر</th>
                                        <th>المخزون</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($unit->products as $product)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($product->image)
                                                    <img src="{{ asset('storage/' . $product->image) }}" 
                                                         alt="{{ $product->name_ar }}" 
                                                         class="rounded me-2" 
                                                         style="width: 40px; height: 40px; object-fit: cover;">
                                                @endif
                                                <div>
                                                    <h6 class="mb-0">{{ $product->name_ar }}</h6>
                                                    <small class="text-muted">{{ $product->sku }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($product->category)
                                                <span class="badge bg-primary">{{ $product->category->name_ar }}</span>
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">{{ number_format($product->selling_price, 2) }} ر.ي</span>
                                        </td>
                                        <td>
                                            @if($product->isLowStock())
                                                <span class="badge bg-warning">{{ $product->stock_quantity }}</span>
                                            @else
                                                <span class="badge bg-success">{{ $product->stock_quantity }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $product->created_at->format('Y-m-d') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('products.show', $product) }}" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>هل أنت متأكد من حذف هذه الوحدة؟</h5>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                    @if($unit->products_count > 0)
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            تحذير: هناك {{ $unit->products_count }} منتج يستخدم هذه الوحدة
                        </div>
                    @endif
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف نهائياً</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteUnit(unitId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/units/${unitId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endpush
