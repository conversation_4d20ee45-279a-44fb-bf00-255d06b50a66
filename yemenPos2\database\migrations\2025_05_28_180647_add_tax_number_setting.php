<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة إعداد الرقم الضريبي
        Setting::create([
            'key' => 'tax_number',
            'value' => '',
            'type' => 'text',
            'group' => 'tax',
            'label' => 'الرقم الضريبي',
            'description' => 'الرقم الضريبي للشركة الذي سيظهر في الفواتير',
            'is_public' => false
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // حذف إعداد الرقم الضريبي
        Setting::where('key', 'tax_number')->delete();
    }
};
