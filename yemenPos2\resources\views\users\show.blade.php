@extends('layouts.app')

@section('title', 'عرض المستخدم')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-user me-2"></i>
                        عرض المستخدم: {{ $user->name }}
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('users.index') }}">المستخدمين</a></li>
                            <li class="breadcrumb-item active">عرض المستخدم</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('users.edit', $user) }}" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                    <a href="{{ route('users.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات المستخدم الأساسية -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                المعلومات الأساسية
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <!-- صورة المستخدم -->
                            <div class="user-avatar-large mb-3">
                                @if($user->avatar)
                                    <img src="{{ asset('storage/' . $user->avatar) }}" 
                                         alt="{{ $user->name }}" class="rounded-circle" width="120" height="120">
                                @else
                                    <div class="avatar-placeholder-large">
                                        {{ strtoupper(substr($user->name, 0, 2)) }}
                                    </div>
                                @endif
                            </div>

                            <h5 class="card-title">{{ $user->name }}</h5>
                            <p class="text-muted">{{ $user->email }}</p>

                            <!-- الحالة -->
                            <div class="mb-3">
                                @if($user->is_active)
                                    <span class="badge bg-success fs-6">نشط</span>
                                @else
                                    <span class="badge bg-danger fs-6">غير نشط</span>
                                @endif
                            </div>

                            <!-- الدور -->
                            @if($user->role)
                            <div class="mb-3">
                                <span class="badge bg-primary fs-6">{{ $user->role->display_name }}</span>
                            </div>
                            @endif

                            <!-- معلومات الاتصال -->
                            @if($user->phone)
                            <div class="contact-info">
                                <i class="fas fa-phone text-muted me-2"></i>
                                <span>{{ $user->phone }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- تفاصيل إضافية -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                التفاصيل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">الاسم الكامل:</label>
                                        <span class="info-value">{{ $user->name }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">البريد الإلكتروني:</label>
                                        <span class="info-value">{{ $user->email }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">رقم الهاتف:</label>
                                        <span class="info-value">{{ $user->phone ?? 'غير محدد' }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">الدور:</label>
                                        <span class="info-value">
                                            @if($user->role)
                                                {{ $user->role->display_name }}
                                            @else
                                                غير محدد
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>

                            @if($user->address)
                            <div class="row">
                                <div class="col-12">
                                    <div class="info-item">
                                        <label class="info-label">العنوان:</label>
                                        <span class="info-value">{{ $user->address }}</span>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">تاريخ الإنشاء:</label>
                                        <span class="info-value">{{ $user->created_at->format('Y-m-d H:i') }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">آخر تحديث:</label>
                                        <span class="info-value">{{ $user->updated_at->format('Y-m-d H:i') }}</span>
                                    </div>
                                </div>
                            </div>

                            @if($user->last_login_at)
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">آخر دخول:</label>
                                        <span class="info-value">{{ $user->last_login_at->format('Y-m-d H:i') }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">منذ:</label>
                                        <span class="info-value">{{ $user->last_login_at->diffForHumans() }}</span>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- الصلاحيات -->
                    @if($user->role && $user->role->permissions)
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-key me-2"></i>
                                الصلاحيات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @php
                                    $permissions = json_decode($user->role->permissions, true);
                                @endphp
                                @if($permissions)
                                    @foreach($permissions as $permission)
                                    <div class="col-md-4 mb-2">
                                        <span class="badge bg-info">{{ $permission }}</span>
                                    </div>
                                    @endforeach
                                @else
                                    <div class="col-12">
                                        <p class="text-muted">لا توجد صلاحيات محددة</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.user-avatar-large {
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-placeholder-large {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2.5rem;
}

.info-item {
    margin-bottom: 1rem;
}

.info-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.875rem;
    display: block;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 0.95rem;
    color: #212529;
}

.contact-info {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.card {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item {
    font-size: 0.875rem;
}

.badge.fs-6 {
    font-size: 0.875rem !important;
    padding: 0.5em 0.75em;
}

@media (max-width: 768px) {
    .avatar-placeholder-large {
        width: 80px;
        height: 80px;
        font-size: 1.8rem;
    }
    
    .col-md-4, .col-md-6 {
        margin-bottom: 1rem;
    }
}
</style>
@endpush
@endsection
