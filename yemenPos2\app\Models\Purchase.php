<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Purchase extends Model
{
    protected $fillable = [
        'purchase_number',
        'supplier_id',
        'user_id',
        'purchase_date',
        'total_amount',
        'status',
        'notes',
        'paid_amount',
        'remaining_amount',
        'payment_status'
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
    ];

    /**
     * العلاقة مع المورد
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع عناصر المشترى
     */
    public function items(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * إنشاء رقم مشترى تلقائي
     */
    public static function generatePurchaseNumber(): string
    {
        $date = Carbon::now()->format('Ymd');
        $sequence = static::whereDate('created_at', Carbon::today())->count() + 1;

        return "PUR-{$date}-" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * حساب إجمالي المبلغ من العناصر
     */
    public function calculateTotal(): void
    {
        $this->total_amount = $this->items()->sum('total_price');
        $this->remaining_amount = $this->total_amount - $this->paid_amount;
        $this->save();
    }

    /**
     * تحديث حالة الدفع
     */
    public function updatePaymentStatus(): void
    {
        if ($this->paid_amount <= 0) {
            $this->payment_status = 'unpaid';
        } elseif ($this->paid_amount >= $this->total_amount) {
            $this->payment_status = 'paid';
        } else {
            $this->payment_status = 'partial';
        }

        $this->remaining_amount = $this->total_amount - $this->paid_amount;
        $this->save();
    }

    /**
     * التحقق من اكتمال الاستلام
     */
    public function isFullyReceived(): bool
    {
        return $this->status === 'received';
    }

    /**
     * الحصول على عدد العناصر
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items()->count();
    }

    /**
     * الحصول على إجمالي الكمية
     */
    public function getTotalQuantityAttribute(): float
    {
        return $this->items()->sum('quantity');
    }
}
