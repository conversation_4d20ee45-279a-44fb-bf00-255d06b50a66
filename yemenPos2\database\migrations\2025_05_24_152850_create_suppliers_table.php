<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم المورد
            $table->string('company_name')->nullable(); // اسم الشركة
            $table->string('phone'); // رقم الهاتف
            $table->string('whatsapp')->nullable(); // رقم الواتساب
            $table->string('email')->nullable(); // البريد الإلكتروني
            $table->text('address')->nullable(); // العنوان
            $table->string('tax_number')->nullable(); // الرقم الضريبي
            $table->decimal('current_balance', 10, 2)->default(0); // الرصيد الحالي (مديونية)
            $table->enum('payment_terms', ['نقدي', 'آجل', 'مختلط'])->default('نقدي'); // شروط الدفع
            $table->integer('credit_days')->default(0); // أيام الائتمان
            $table->boolean('is_active')->default(true); // نشط/غير نشط
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suppliers');
    }
};
