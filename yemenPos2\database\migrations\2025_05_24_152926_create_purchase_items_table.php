<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_id')->constrained()->onDelete('cascade'); // أمر الشراء
            $table->foreignId('product_id')->constrained()->onDelete('cascade'); // المنتج
            $table->string('product_name'); // اسم المنتج (نسخة للحفظ)
            $table->string('product_sku'); // رمز المنتج (نسخة للحفظ)
            $table->integer('quantity_ordered'); // الكمية المطلوبة
            $table->integer('quantity_received')->default(0); // الكمية المستلمة
            $table->decimal('unit_cost', 10, 2); // تكلفة الوحدة
            $table->decimal('discount_amount', 10, 2)->default(0); // خصم على الصنف
            $table->decimal('total_cost', 10, 2); // التكلفة الإجمالية
            $table->string('unit')->default('قطعة'); // الوحدة
            $table->date('expiry_date')->nullable(); // تاريخ الانتهاء
            $table->string('batch_number')->nullable(); // رقم الدفعة
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_items');
    }
};
