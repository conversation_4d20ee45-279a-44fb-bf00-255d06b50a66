<?php $__env->startSection('title', 'إدارة العملاء - نظام نقطة المبيعات'); ?>
<?php $__env->startSection('page-title', 'إدارة العملاء'); ?>

<?php $__env->startSection('content'); ?>
<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('customers.index')); ?>">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="البحث بالاسم أو الهاتف أو البريد...">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="status" onchange="this.form.submit()">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>
                            نشط
                        </option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>
                            غير نشط
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-2"></i>
                        إعادة تعيين
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عميل
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>
            قائمة العملاء (<?php echo e($customers->total()); ?>)
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if($customers->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>العنوان</th>
                            <th>عدد المبيعات</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="customer-avatar me-3">
                                        <?php echo e(substr($customer->name, 0, 1)); ?>

                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo e($customer->name); ?></h6>
                                        <small class="text-muted">#<?php echo e($customer->id); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="tel:<?php echo e($customer->phone); ?>" class="text-decoration-none">
                                    <i class="fas fa-phone me-1 text-success"></i>
                                    <?php echo e($customer->phone); ?>

                                </a>
                            </td>
                            <td>
                                <?php if($customer->email): ?>
                                    <a href="mailto:<?php echo e($customer->email); ?>" class="text-decoration-none">
                                        <i class="fas fa-envelope me-1 text-primary"></i>
                                        <?php echo e($customer->email); ?>

                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">لا يوجد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($customer->address): ?>
                                    <span class="text-truncate d-inline-block" style="max-width: 150px;"
                                          title="<?php echo e($customer->address); ?>">
                                        <i class="fas fa-map-marker-alt me-1 text-warning"></i>
                                        <?php echo e($customer->address); ?>

                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">لا يوجد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-primary"><?php echo e($customer->sales_count); ?> مبيعة</span>
                            </td>
                            <td>
                                <?php if($customer->is_active): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo e($customer->created_at->format('Y-m-d')); ?>

                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('customers.show', $customer)); ?>"
                                       class="btn btn-sm btn-outline-info"
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('customers.edit', $customer)); ?>"
                                       class="btn btn-sm btn-outline-primary"
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger"
                                            title="حذف"
                                            onclick="deleteCustomer(<?php echo e($customer->id); ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="صفحات العملاء">
                    <?php echo e($customers->links('pagination::bootstrap-4')); ?>

                </nav>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد عملاء</h5>
                <p class="text-muted">لم يتم العثور على عملاء يطابقون معايير البحث</p>
                <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول عميل
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function deleteCustomer(customerId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/customers/${customerId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-submit search form on input
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/customers/index.blade.php ENDPATH**/ ?>