<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    protected $fillable = [
        'name',
        'phone',
        'whatsapp',
        'email',
        'address',
        'credit_limit',
        'current_balance',
        'customer_type',
        'is_active',
        'birth_date',
        'notes'
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'birth_date' => 'date',
    ];

    /**
     * العلاقة مع المبيعات
     */
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * التحقق من تجاوز حد الائتمان
     */
    public function isOverCreditLimit(): bool
    {
        return $this->current_balance > $this->credit_limit;
    }

    /**
     * الحصول على إجمالي المبيعات
     */
    public function getTotalSalesAttribute(): float
    {
        return $this->sales()->sum('total_amount');
    }

    /**
     * الحصول على إجمالي المدفوعات
     */
    public function getTotalPaymentsAttribute(): float
    {
        return $this->payments()->where('payment_type', 'دفعة')->sum('amount');
    }

    /**
     * الحصول على رقم الواتساب المنسق
     */
    public function getFormattedWhatsappAttribute(): ?string
    {
        if (!$this->whatsapp) return null;

        // إزالة الرموز والمسافات
        $number = preg_replace('/[^0-9]/', '', $this->whatsapp);

        // إضافة رمز الدولة إذا لم يكن موجوداً
        if (!str_starts_with($number, '967')) {
            $number = '967' . ltrim($number, '0');
        }

        return '+' . $number;
    }
}
