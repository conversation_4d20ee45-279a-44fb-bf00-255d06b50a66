@extends('layouts.app')

@section('title', 'إدارة الموردين - نظام نقطة المبيعات')
@section('page-title', 'إدارة الموردين')

@section('content')
<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('suppliers.index') }}">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search"
                               value="{{ request('search') }}"
                               placeholder="البحث بالاسم أو الشركة أو الهاتف...">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="status" onchange="this.form.submit()">
                        <option value="">جميع الحالات</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                            نشط
                        </option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>
                            غير نشط
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <a href="{{ route('suppliers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-2"></i>
                        إعادة تعيين
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="{{ route('suppliers.create') }}" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مورد
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Suppliers Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-truck me-2"></i>
            قائمة الموردين ({{ $suppliers->total() }})
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        @if($suppliers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المورد</th>
                            <th>الشركة</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>شروط الدفع</th>
                            <th>عدد المشتريات</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($suppliers as $supplier)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="supplier-avatar me-3">
                                        {{ substr($supplier->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ $supplier->name }}</h6>
                                        <small class="text-muted">#{{ $supplier->id }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($supplier->company_name)
                                    <span class="fw-bold">{{ $supplier->company_name }}</span>
                                @else
                                    <span class="text-muted">لا يوجد</span>
                                @endif
                            </td>
                            <td>
                                <a href="tel:{{ $supplier->phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1 text-success"></i>
                                    {{ $supplier->phone }}
                                </a>
                                @if($supplier->whatsapp)
                                    <br>
                                    <a href="https://wa.me/{{ $supplier->whatsapp }}" class="text-decoration-none text-success" target="_blank">
                                        <i class="fab fa-whatsapp me-1"></i>
                                        {{ $supplier->whatsapp }}
                                    </a>
                                @endif
                            </td>
                            <td>
                                @if($supplier->email)
                                    <a href="mailto:{{ $supplier->email }}" class="text-decoration-none">
                                        <i class="fas fa-envelope me-1 text-primary"></i>
                                        {{ $supplier->email }}
                                    </a>
                                @else
                                    <span class="text-muted">لا يوجد</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge
                                    @if($supplier->payment_terms === 'نقدي') bg-success
                                    @elseif($supplier->payment_terms === 'آجل') bg-warning
                                    @else bg-info
                                    @endif">
                                    {{ $supplier->payment_terms }}
                                </span>
                                @if($supplier->credit_days > 0)
                                    <br><small class="text-muted">{{ $supplier->credit_days }} يوم</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ $supplier->purchases_count }} مشترى</span>
                            </td>
                            <td>
                                @if($supplier->is_active)
                                    <span class="badge bg-success">نشط</span>
                                @else
                                    <span class="badge bg-secondary">غير نشط</span>
                                @endif
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ $supplier->created_at->format('Y-m-d') }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('suppliers.show', $supplier) }}"
                                       class="btn btn-sm btn-outline-info"
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('suppliers.edit', $supplier) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger"
                                            title="حذف"
                                            onclick="deleteSupplier({{ $supplier->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="صفحات الموردين">
                    {{ $suppliers->links('pagination::bootstrap-4') }}
                </nav>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد موردين</h5>
                <p class="text-muted">لم يتم العثور على موردين يطابقون معايير البحث</p>
                <a href="{{ route('suppliers.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول مورد
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا المورد؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.supplier-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>
@endpush

@push('scripts')
<script>
function deleteSupplier(supplierId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/suppliers/${supplierId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-submit search form on input
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
@endpush
