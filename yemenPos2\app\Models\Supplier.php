<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    protected $fillable = [
        'name',
        'company_name',
        'phone',
        'whatsapp',
        'email',
        'address',
        'tax_number',
        'current_balance',
        'payment_terms',
        'credit_days',
        'is_active',
        'notes'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * العلاقة مع المشتريات
     */
    public function purchases(): HasMany
    {
        return $this->hasMany(Purchase::class);
    }
}
