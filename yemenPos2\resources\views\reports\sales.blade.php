@extends('layouts.app')

@section('title', 'تقرير المبيعات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-chart-line me-2"></i>
                        تقرير المبيعات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('reports.index') }}">التقارير</a></li>
                            <li class="breadcrumb-item active">تقرير المبيعات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('reports.sales') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from" 
                                       value="{{ $dateFrom }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to" 
                                       value="{{ $dateTo }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="customer_id">
                                    <option value="">جميع العملاء</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" 
                                                {{ $customerId == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="مكتملة" {{ $status == 'مكتملة' ? 'selected' : '' }}>مكتملة</option>
                                    <option value="جزئي" {{ $status == 'جزئي' ? 'selected' : '' }}>دفع جزئي</option>
                                    <option value="معلقة" {{ $status == 'معلقة' ? 'selected' : '' }}>معلقة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                </button>
                                <a href="{{ route('reports.sales') }}" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                </a>
                                <button type="button" class="btn btn-success" onclick="exportData()">
                                    <i class="fas fa-download me-2"></i>تصدير
                                </button>
                                <button type="button" class="btn btn-info" onclick="printReport()">
                                    <i class="fas fa-print me-2"></i>طباعة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المبيعات</h6>
                                    <h4>{{ number_format($totalSales, 2) }} ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">عدد المعاملات</h6>
                                    <h4>{{ $totalTransactions }}</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-receipt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">متوسط المعاملة</h6>
                                    <h4>{{ number_format($averageTransaction, 2) }} ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calculator fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المديونيات</h6>
                                    <h4>{{ number_format($totalRemaining, 2) }} ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                المبيعات اليومية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="salesChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        تفاصيل المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    @if($sales->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="salesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>الإجمالي</th>
                                    <th>المدفوع</th>
                                    <th>المتبقي</th>
                                    <th>الحالة</th>
                                    <th>الكاشير</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($sales as $sale)
                                <tr>
                                    <td>
                                        <strong>{{ $sale->invoice_number }}</strong>
                                    </td>
                                    <td>{{ $sale->customer->name ?? 'عميل نقدي' }}</td>
                                    <td>
                                        {{ $sale->sale_date->format('Y-m-d') }}
                                        <br><small class="text-muted">{{ $sale->sale_time }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ number_format($sale->total_amount, 2) }} ر.ي</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">
                                            {{ number_format($sale->paid_amount ?? $sale->total_amount, 2) }} ر.ي
                                        </span>
                                    </td>
                                    <td>
                                        @if($sale->remaining_amount > 0)
                                            <span class="text-danger">
                                                {{ number_format($sale->remaining_amount, 2) }} ر.ي
                                            </span>
                                        @else
                                            <span class="text-muted">0.00 ر.ي</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($sale->status === 'مكتملة')
                                            <span class="badge bg-success">مكتملة</span>
                                        @elseif($sale->status === 'جزئي')
                                            <span class="badge bg-warning">دفع جزئي</span>
                                        @else
                                            <span class="badge bg-secondary">{{ $sale->status }}</span>
                                        @endif
                                    </td>
                                    <td>{{ $sale->user->name ?? 'غير محدد' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('sales.show', $sale) }}" 
                                               class="btn btn-sm btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('sales.print', $sale) }}" 
                                               class="btn btn-sm btn-primary" title="طباعة" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مبيعات في الفترة المحددة</h5>
                        <p class="text-muted">جرب تغيير فلاتر البحث</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمبيعات اليومية
const ctx = document.getElementById('salesChart').getContext('2d');
const dailySalesData = @json($dailySales);

new Chart(ctx, {
    type: 'line',
    data: {
        labels: dailySalesData.map(item => item.date),
        datasets: [{
            label: 'المبيعات (ر.ي)',
            data: dailySalesData.map(item => item.total),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ر.ي';
                    }
                }
            }
        }
    }
});

function exportData() {
    alert('ميزة التصدير قيد التطوير');
}

function printReport() {
    window.print();
}
</script>
@endsection
