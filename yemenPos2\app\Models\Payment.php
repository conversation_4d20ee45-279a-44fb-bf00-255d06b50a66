<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Payment extends Model
{
    protected $fillable = [
        'payment_number',
        'payable_type',
        'payable_id',
        'customer_id',
        'supplier_id',
        'user_id',
        'amount',
        'payment_method',
        'payment_type',
        'payment_date',
        'reference_number',
        'bank_name',
        'check_number',
        'check_date',
        'currency',
        'exchange_rate',
        'notes',
        'status'
    ];

    protected $casts = [
        'payment_date' => 'date',
        'check_date' => 'date',
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4'
    ];

    /**
     * العلاقة مع الكائن القابل للدفع (Sale أو Purchase)
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع المورد
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * إنشاء رقم دفعة تلقائي
     */
    public static function generatePaymentNumber(): string
    {
        $lastPayment = self::latest('id')->first();
        $number = $lastPayment ? $lastPayment->id + 1 : 1;
        return 'PAY-' . date('Y') . '-' . str_pad($number, 6, '0', STR_PAD_LEFT);
    }

    /**
     * الحصول على نوع الكائن القابل للدفع بالعربية
     */
    public function getPayableTypeArabicAttribute(): string
    {
        return match($this->payable_type) {
            'App\Models\Sale' => 'مبيعات',
            'App\Models\Purchase' => 'مشتريات',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على طريقة الدفع مع الأيقونة
     */
    public function getPaymentMethodWithIconAttribute(): string
    {
        return match($this->payment_method) {
            'نقدي' => '<i class="fas fa-money-bill-wave text-success"></i> نقدي',
            'تحويل بنكي' => '<i class="fas fa-university text-primary"></i> تحويل بنكي',
            'شيك' => '<i class="fas fa-file-invoice text-warning"></i> شيك',
            'بطاقة ائتمان' => '<i class="fas fa-credit-card text-info"></i> بطاقة ائتمان',
            default => $this->payment_method
        };
    }

    /**
     * الحصول على حالة الدفعة مع اللون
     */
    public function getStatusBadgeAttribute(): string
    {
        return match($this->status) {
            'مؤكد' => '<span class="badge bg-success">مؤكد</span>',
            'معلق' => '<span class="badge bg-warning">معلق</span>',
            'ملغي' => '<span class="badge bg-danger">ملغي</span>',
            default => '<span class="badge bg-secondary">' . $this->status . '</span>'
        };
    }
}
