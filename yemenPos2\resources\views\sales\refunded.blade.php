@extends('layouts.app')

@section('title', 'الفواتير المرتجعة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-undo me-2 text-secondary"></i>
                        الفواتير المرتجعة
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('sales.index') }}">المبيعات</a></li>
                            <li class="breadcrumb-item active">الفواتير المرتجعة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('sales.index') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-list me-2"></i>جميع المبيعات
                    </a>
                    <a href="{{ route('sales.unposted') }}" class="btn btn-outline-warning me-2">
                        <i class="fas fa-clock me-2"></i>غير المرحلة
                    </a>
                    <a href="{{ route('sales.posted') }}" class="btn btn-outline-success">
                        <i class="fas fa-check me-2"></i>المرحلة
                    </a>
                </div>
            </div>

            <!-- Alert -->
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>معلومة:</strong> هذه الصفحة تعرض الفواتير المرتجعة فقط. تم إعادة المخزون لهذه الفواتير.
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3>{{ $countRefunded }}</h3>
                            <p class="mb-0">إجمالي الفواتير المرتجعة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <h3>{{ number_format($totalRefunded, 0) }}</h3>
                            <p class="mb-0">إجمالي قيمة المرتجعات (ر.ي)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('sales.refunded') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">البحث</label>
                                    <input type="text" class="form-control" name="search"
                                           value="{{ request('search') }}"
                                           placeholder="رقم الفاتورة أو اسم العميل">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="date_from"
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="date_to"
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">العميل</label>
                                    <select class="form-select" name="customer_id">
                                        <option value="">جميع العملاء</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}"
                                                    {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if(request()->hasAny(['search', 'date_from', 'date_to', 'customer_id']))
                            <div class="row">
                                <div class="col-12">
                                    <a href="{{ route('sales.refunded') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times me-2"></i>مسح الفلاتر
                                    </a>
                                </div>
                            </div>
                        @endif
                    </form>
                </div>
            </div>

            <!-- Sales Table -->
            @if($sales->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-undo me-2"></i>
                            الفواتير المرتجعة ({{ $sales->total() }})
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>تاريخ الفاتورة</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>تاريخ الاسترجاع</th>
                                        <th>المستخدم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sales as $sale)
                                    <tr>
                                        <td>
                                            <a href="{{ route('sales.show', $sale) }}" class="text-decoration-none fw-bold">
                                                {{ $sale->invoice_number }}
                                            </a>
                                            <br>
                                            <small class="text-secondary">
                                                <i class="fas fa-undo me-1"></i>مرتجعة
                                            </small>
                                        </td>
                                        <td>
                                            @if($sale->customer)
                                                <div>
                                                    <span class="fw-bold">{{ $sale->customer->name }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $sale->customer->phone }}</small>
                                                </div>
                                            @else
                                                <span class="text-muted">عميل نقدي</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>
                                                {{ $sale->sale_date->format('Y-m-d') }}
                                                <br>
                                                <small class="text-muted">
                                                    {{ $sale->sale_time->format('g:i') }}
                                                    {{ $sale->sale_time->format('A') == 'AM' ? 'ص' : 'م' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-secondary">
                                                {{ number_format($sale->total_amount, 2) }} ر.ي
                                            </span>
                                        </td>
                                        <td>
                                            <div>
                                                {{ $sale->updated_at->format('Y-m-d') }}
                                                <br>
                                                <small class="text-muted">
                                                    {{ $sale->updated_at->format('g:i') }}
                                                    {{ $sale->updated_at->format('A') == 'AM' ? 'ص' : 'م' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $sale->user->name }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('sales.show', $sale) }}"
                                                   class="btn btn-sm btn-outline-info"
                                                   title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('sales.print', $sale) }}"
                                                   class="btn btn-sm btn-outline-secondary"
                                                   title="طباعة"
                                                   target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>

                                                @if(auth()->user()->hasRole('admin'))
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            title="حذف نهائي"
                                                            onclick="forceDeleteSale({{ $sale->id }})">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="صفحات الفواتير المرتجعة">
                                {{ $sales->links('pagination::bootstrap-4') }}
                            </nav>
                        </div>
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-smile fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز! لا توجد فواتير مرتجعة</h5>
                    <p class="text-muted">لم يتم العثور على فواتير مرتجعة تطابق معايير البحث</p>
                    <a href="{{ route('sales.index') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للمبيعات
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Force Delete Confirmation Modal -->
<div class="modal fade" id="forceDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف النهائي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>
                هل أنت متأكد من حذف هذه الفاتورة المرتجعة نهائياً؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="forceDeleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف نهائي</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.alert {
    border-left: 4px solid #6c757d;
}

.card h3 {
    font-size: 2rem;
    font-weight: bold;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
</style>
@endpush

@push('scripts')
<script>
function forceDeleteSale(saleId) {
    const forceDeleteForm = document.getElementById('forceDeleteForm');
    forceDeleteForm.action = `/sales/${saleId}/force-delete`;

    const forceDeleteModal = new bootstrap.Modal(document.getElementById('forceDeleteModal'));
    forceDeleteModal.show();
}
</script>
@endpush
