@extends('layouts.app')

@section('title', 'إدارة الوحدات - نظام نقطة المبيعات')
@section('page-title', 'إدارة الوحدات')

@section('content')
<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('units.index') }}">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search"
                               value="{{ request('search') }}"
                               placeholder="البحث بالاسم أو الرمز...">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="status" onchange="this.form.submit()">
                        <option value="">جميع الحالات</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                            نشط
                        </option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>
                            غير نشط
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <a href="{{ route('units.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-2"></i>
                        إعادة تعيين
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="{{ route('units.create') }}" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>
                        إضافة وحدة
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Units Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-balance-scale me-2"></i>
            قائمة الوحدات ({{ $units->total() }})
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        @if($units->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الوحدة</th>
                            <th>الرمز</th>
                            <th>الوصف</th>
                            <th>عدد المنتجات</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($units as $unit)
                        <tr>
                            <td>
                                <div>
                                    <h6 class="mb-0">{{ $unit->name_ar }}</h6>
                                    @if($unit->name_en)
                                        <small class="text-muted">{{ $unit->name_en }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if($unit->symbol)
                                    <span class="badge bg-secondary">{{ $unit->symbol }}</span>
                                @else
                                    <span class="text-muted">لا يوجد</span>
                                @endif
                            </td>
                            <td>
                                @if($unit->description)
                                    <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                          title="{{ $unit->description }}">
                                        {{ $unit->description }}
                                    </span>
                                @else
                                    <span class="text-muted">لا يوجد وصف</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ $unit->products_count }} منتج</span>
                            </td>
                            <td>
                                @if($unit->is_active)
                                    <span class="badge bg-success">نشط</span>
                                @else
                                    <span class="badge bg-secondary">غير نشط</span>
                                @endif
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ $unit->created_at->format('Y-m-d') }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('units.show', $unit) }}"
                                       class="btn btn-sm btn-outline-info"
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('units.edit', $unit) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger"
                                            title="حذف"
                                            onclick="deleteUnit({{ $unit->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="صفحات الوحدات">
                    {{ $units->links('pagination::bootstrap-4') }}
                </nav>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وحدات</h5>
                <p class="text-muted">لم يتم العثور على وحدات تطابق معايير البحث</p>
                <a href="{{ route('units.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول وحدة
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه الوحدة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>
@endpush

@push('scripts')
<script>
function deleteUnit(unitId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/units/${unitId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-submit search form on input
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
@endpush
