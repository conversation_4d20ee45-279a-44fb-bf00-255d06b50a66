<?php $__env->startSection('title', 'إدارة المديونيات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        إدارة المديونيات (الفواتير المرحلة)
                    </h4>
                    <div>
                        <span class="badge bg-warning fs-6">
                            إجمالي المديونيات: <?php echo e(number_format($totalDebts, 2)); ?> ر.ي
                        </span>
                    </div>
                </div>

                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-filter me-2"></i>
                                فلاتر البحث
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="<?php echo e(route('sales.debts')); ?>">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">البحث</label>
                                        <input type="text" class="form-control" name="search"
                                               value="<?php echo e(request('search')); ?>" placeholder="رقم الفاتورة أو اسم العميل...">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" name="date_from"
                                               value="<?php echo e(request('date_from')); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" name="date_to"
                                               value="<?php echo e(request('date_to')); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">العميل</label>
                                        <select class="form-select" name="customer_id">
                                            <option value="">جميع العملاء</option>
                                            <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($customer->id); ?>"
                                                        <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                                    <?php echo e($customer->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">الحد الأدنى</label>
                                        <input type="number" class="form-control" name="min_amount"
                                               value="<?php echo e(request('min_amount')); ?>" placeholder="0" step="0.01">
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <a href="<?php echo e(route('sales.debts')); ?>" class="btn btn-secondary">
                                            <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- ملاحظة توضيحية -->
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> تظهر هنا فقط الفواتير المرحلة التي لها مبلغ متبقي (غير مدفوعة أو مدفوعة جزئياً).
                        الفواتير غير المرحلة لا تظهر في المديونيات.
                    </div>

                    <!-- إحصائيات -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي المديونيات</h6>
                                            <h4><?php echo e(number_format($allDebts, 2)); ?> ر.ي</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-money-bill-wave fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">المديونيات المفلترة</h6>
                                            <h4><?php echo e(number_format($totalDebts, 2)); ?> ر.ي</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-filter fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">عدد الفواتير</h6>
                                            <h4><?php echo e($partialSales->total()); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-receipt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">متوسط المديونية</h6>
                                            <h4><?php echo e($partialSales->count() > 0 ? number_format($totalDebts / $partialSales->count(), 2) : '0.00'); ?> ر.ي</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calculator fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المديونيات -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>الإجمالي</th>
                                    <th>المدفوع</th>
                                    <th>المتبقي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="debtsTableBody">
                                <?php $__currentLoopData = $partialSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr data-sale-id="<?php echo e($sale->id); ?>">
                                    <td>
                                        <strong><?php echo e($sale->invoice_number); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($sale->customer->name ?? 'عميل عادي'); ?></strong>
                                            <?php if($sale->customer && $sale->customer->phone): ?>
                                            <br><small class="text-muted"><?php echo e($sale->customer->phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo e($sale->sale_date->format('Y-m-d')); ?>

                                            <br><small class="text-muted"><?php echo e($sale->sale_time); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo e(number_format($sale->total_amount, 2)); ?> ر.ي</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">
                                            <?php echo e(number_format($sale->paid_amount ?? 0, 2)); ?> ر.ي
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-danger fw-bold">
                                            <?php echo e(number_format($sale->remaining_amount ?? 0, 2)); ?> ر.ي
                                        </span>
                                    </td>
                                    <td>
                                        <?php if($sale->status === 'غير مدفوعة'): ?>
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        <?php elseif($sale->status === 'دفع جزئي'): ?>
                                            <span class="badge bg-warning">دفع جزئي</span>
                                        <?php elseif($sale->status === 'مكتملة'): ?>
                                            <span class="badge bg-success">مكتملة</span>
                                        <?php elseif($sale->status === 'مسترجعة'): ?>
                                            <span class="badge bg-secondary">مسترجعة</span>
                                        <?php else: ?>
                                            <span class="badge bg-info"><?php echo e($sale->status); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if($sale->remaining_amount > 0): ?>
                                            <button class="btn btn-sm btn-success"
                                                    onclick="payDebt(<?php echo e($sale->id); ?>, <?php echo e($sale->remaining_amount); ?>)"
                                                    title="دفع المتبقي كاملاً">
                                                <i class="fas fa-money-bill-wave"></i>
                                                <span class="d-none d-md-inline ms-1">كامل</span>
                                            </button>
                                            <button class="btn btn-sm btn-warning"
                                                    onclick="partialPayDebt(<?php echo e($sale->id); ?>, <?php echo e($sale->remaining_amount); ?>)"
                                                    title="دفع جزئي">
                                                <i class="fas fa-coins"></i>
                                                <span class="d-none d-md-inline ms-1">جزئي</span>
                                            </button>
                                            <?php endif; ?>
                                            <button class="btn btn-sm btn-info"
                                                    onclick="viewSale(<?php echo e($sale->id); ?>)"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary"
                                                    onclick="printInvoice(<?php echo e($sale->id); ?>)"
                                                    title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="صفحات المديونيات">
                            <?php echo e($partialSales->links('pagination::bootstrap-4')); ?>

                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal دفع المديونية المحسن -->
<div class="modal fade" id="payDebtModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    دفع مديونية
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- معلومات الفاتورة -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>
                            معلومات الفاتورة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">رقم الفاتورة</label>
                                    <div class="fw-bold fs-5" id="invoiceNumber">-</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">تاريخ الفاتورة</label>
                                    <div id="invoiceDate">-</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">العميل</label>
                                    <div id="customerName">-</div>
                                    <small class="text-muted" id="customerPhone"></small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">طريقة الدفع الأصلية</label>
                                    <div id="originalPaymentMethod">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص المبالغ -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            ملخص المبالغ
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="border rounded p-3 bg-info text-white">
                                    <div class="fs-6">إجمالي الفاتورة</div>
                                    <div class="fs-4 fw-bold" id="totalAmount">0.00 ر.ي</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="border rounded p-3 bg-success text-white">
                                    <div class="fs-6">المدفوع سابقاً</div>
                                    <div class="fs-4 fw-bold" id="paidAmount">0.00 ر.ي</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="border rounded p-3 bg-danger text-white">
                                    <div class="fs-6">المتبقي للدفع</div>
                                    <div class="fs-4 fw-bold" id="remainingAmount">0.00 ر.ي</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج الدفع -->
                <form id="payDebtForm">
                    <input type="hidden" id="saleId">

                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                تفاصيل الدفع
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control form-control-lg" id="paymentAmount"
                                                   step="0.01" min="0" required placeholder="0.00">
                                            <span class="input-group-text">ر.ي</span>
                                        </div>
                                        <div class="form-text">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="setFullAmount()">
                                                <i class="fas fa-money-bill-wave me-1"></i>دفع كامل
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="setHalfAmount()">
                                                <i class="fas fa-divide me-1"></i>نصف المبلغ
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                        <select class="form-select form-select-lg" id="paymentMethod" required>
                                            <option value="">اختر طريقة الدفع</option>
                                            <option value="نقدي">💵 نقدي</option>
                                            <option value="بطاقة ائتمان">💳 بطاقة ائتمان</option>
                                            <option value="تحويل بنكي">🏦 تحويل بنكي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ملاحظات الدفع</label>
                                <textarea class="form-control" id="paymentNotes" rows="3"
                                          placeholder="أضف أي ملاحظات حول عملية الدفع..."></textarea>
                            </div>

                            <!-- معاينة النتيجة -->
                            <div class="alert alert-info" id="paymentPreview" style="display: none;">
                                <h6><i class="fas fa-info-circle me-2"></i>معاينة النتيجة:</h6>
                                <div id="previewText"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-success btn-lg" onclick="submitPayment()" id="submitBtn">
                    <i class="fas fa-check me-2"></i>تأكيد الدفع
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let currentSaleData = null;
let currentRemainingAmount = 0;

// دفع المديونية كاملة
function payDebt(saleId, remainingAmount) {
    currentRemainingAmount = remainingAmount;
    loadSaleDetails(saleId, true); // true = دفع كامل
}

// دفع جزئي
function partialPayDebt(saleId, remainingAmount) {
    currentRemainingAmount = remainingAmount;
    loadSaleDetails(saleId, false); // false = دفع جزئي
}

// جلب تفاصيل الفاتورة
function loadSaleDetails(saleId, isFullPayment) {
    // إظهار loading
    showLoading();

    fetch(`/sales/${saleId}`, {
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('فشل في جلب بيانات الفاتورة');
        }
        return response.json();
    })
    .then(data => {
        currentSaleData = data;
        populateModal(data, isFullPayment);
        hideLoading();

        const modal = new bootstrap.Modal(document.getElementById('payDebtModal'));
        modal.show();
    })
    .catch(error => {
        hideLoading();
        console.error('خطأ:', error);
        alert('خطأ في جلب بيانات الفاتورة: ' + error.message);
    });
}

// ملء البيانات في النافذة المنبثقة
function populateModal(sale, isFullPayment) {
    // معلومات الفاتورة
    document.getElementById('saleId').value = sale.id;
    document.getElementById('invoiceNumber').textContent = sale.invoice_number;
    document.getElementById('invoiceDate').textContent = formatDate(sale.sale_date) + ' - ' + formatTime(sale.sale_time);

    // معلومات العميل
    if (sale.customer) {
        document.getElementById('customerName').textContent = sale.customer.name;
        document.getElementById('customerPhone').textContent = sale.customer.phone || '';
    } else {
        document.getElementById('customerName').textContent = 'عميل عادي';
        document.getElementById('customerPhone').textContent = '';
    }

    // طريقة الدفع الأصلية
    document.getElementById('originalPaymentMethod').textContent = sale.payment_method;

    // المبالغ
    document.getElementById('totalAmount').textContent = formatCurrency(sale.total_amount);
    document.getElementById('paidAmount').textContent = formatCurrency(sale.paid_amount || 0);
    document.getElementById('remainingAmount').textContent = formatCurrency(currentRemainingAmount);

    // إعداد حقل المبلغ المدفوع
    const paymentAmountInput = document.getElementById('paymentAmount');
    paymentAmountInput.max = currentRemainingAmount;

    if (isFullPayment) {
        paymentAmountInput.value = currentRemainingAmount.toFixed(2);
    } else {
        paymentAmountInput.value = '';
        paymentAmountInput.focus();
    }

    // إعادة تعيين الحقول الأخرى
    document.getElementById('paymentMethod').value = '';
    document.getElementById('paymentNotes').value = '';
    document.getElementById('paymentPreview').style.display = 'none';

    // إضافة مستمعي الأحداث
    setupEventListeners();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    const paymentAmountInput = document.getElementById('paymentAmount');
    const paymentMethodSelect = document.getElementById('paymentMethod');

    // تحديث المعاينة عند تغيير المبلغ أو طريقة الدفع
    paymentAmountInput.addEventListener('input', updatePreview);
    paymentMethodSelect.addEventListener('change', updatePreview);
}

// تحديث معاينة النتيجة
function updatePreview() {
    const paymentAmount = parseFloat(document.getElementById('paymentAmount').value) || 0;
    const paymentMethod = document.getElementById('paymentMethod').value;

    if (paymentAmount > 0 && paymentMethod) {
        const newRemaining = currentRemainingAmount - paymentAmount;
        let status = '';
        let statusClass = '';

        if (newRemaining <= 0) {
            status = 'مكتملة';
            statusClass = 'text-success';
        } else if (paymentAmount > 0) {
            status = 'دفع جزئي';
            statusClass = 'text-warning';
        }

        const previewText = `
            <div class="row">
                <div class="col-md-6">
                    <strong>المبلغ المدفوع:</strong> ${formatCurrency(paymentAmount)}<br>
                    <strong>طريقة الدفع:</strong> ${paymentMethod}
                </div>
                <div class="col-md-6">
                    <strong>المتبقي بعد الدفع:</strong> ${formatCurrency(Math.max(0, newRemaining))}<br>
                    <strong>حالة الفاتورة:</strong> <span class="${statusClass}">${status}</span>
                </div>
            </div>
        `;

        document.getElementById('previewText').innerHTML = previewText;
        document.getElementById('paymentPreview').style.display = 'block';

        // تفعيل/تعطيل زر التأكيد
        const submitBtn = document.getElementById('submitBtn');
        if (paymentAmount <= currentRemainingAmount && paymentAmount > 0) {
            submitBtn.disabled = false;
            submitBtn.classList.remove('btn-secondary');
            submitBtn.classList.add('btn-success');
        } else {
            submitBtn.disabled = true;
            submitBtn.classList.remove('btn-success');
            submitBtn.classList.add('btn-secondary');
        }
    } else {
        document.getElementById('paymentPreview').style.display = 'none';
        document.getElementById('submitBtn').disabled = true;
    }
}

// دفع كامل
function setFullAmount() {
    document.getElementById('paymentAmount').value = currentRemainingAmount.toFixed(2);
    updatePreview();
}

// نصف المبلغ
function setHalfAmount() {
    const halfAmount = (currentRemainingAmount / 2).toFixed(2);
    document.getElementById('paymentAmount').value = halfAmount;
    updatePreview();
}

// تأكيد الدفع
function submitPayment() {
    const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
    const paymentMethod = document.getElementById('paymentMethod').value;

    // التحقق من صحة البيانات
    if (!paymentAmount || paymentAmount <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        return;
    }

    if (paymentAmount > currentRemainingAmount) {
        alert('المبلغ المدفوع أكبر من المبلغ المتبقي');
        return;
    }

    if (!paymentMethod) {
        alert('يرجى اختيار طريقة الدفع');
        return;
    }

    // تعطيل الزر أثناء المعالجة
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';

    const formData = {
        sale_id: document.getElementById('saleId').value,
        payment_amount: paymentAmount,
        payment_method: paymentMethod,
        notes: document.getElementById('paymentNotes').value
    };

    fetch('/sales/pay-debt', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح
            showSuccessMessage('تم دفع المديونية بنجاح');

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('payDebtModal'));
            modal.hide();

            // إعادة تحميل الصفحة بعد تأخير قصير
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            alert('خطأ: ' + data.message);
            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ أثناء معالجة الدفع');
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

// دوال مساعدة
function formatCurrency(amount) {
    return parseFloat(amount).toFixed(2) + ' ر.ي';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatTime(timeString) {
    const time = new Date('2000-01-01 ' + timeString);
    return time.toLocaleTimeString('ar-SA', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function showLoading() {
    // يمكن إضافة spinner هنا
    console.log('جاري التحميل...');
}

function hideLoading() {
    // إخفاء spinner
    console.log('انتهى التحميل');
}

function showSuccessMessage(message) {
    // يمكن استخدام toast أو alert محسن
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// طباعة الفاتورة
function printInvoice(saleId) {
    window.open(`/sales/${saleId}/print`, '_blank');
}

// عرض تفاصيل الفاتورة
function viewSale(saleId) {
    window.open(`/sales/${saleId}`, '_blank');
}
</script>

<?php $__env->startPush('styles'); ?>
<style>
/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين البطاقات الإحصائية */
.card h5 {
    font-size: 1.5rem;
    font-weight: bold;
}

.card p {
    font-size: 0.875rem;
}

/* تحسين الفلاتر */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-control, .form-select {
    font-size: 0.875rem;
}

/* تحسين breadcrumb */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item {
    font-size: 0.875rem;
}

/* تحسين الـ badges */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }

    .col-md-3, .col-md-2 {
        margin-bottom: 1rem;
    }
}

/* تحسين البطاقات الملونة */
.card.bg-warning h6,
.card.bg-danger h6,
.card.bg-info h6,
.card.bg-success h6 {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.card.bg-warning h4,
.card.bg-danger h4,
.card.bg-info h4,
.card.bg-success h4 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/sales/debts.blade.php ENDPATH**/ ?>