<?php $__env->startSection('title', 'إضافة منتج جديد - نظام نقطة المبيعات'); ?>
<?php $__env->startSection('page-title', 'إضافة منتج جديد'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .product-form-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 200px);
        padding: 30px 0;
    }

    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }

    .form-header::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }

    .section-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }

    .section-title i {
        margin-left: 10px;
        font-size: 1.2rem;
    }

    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .auto-generate-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 10px;
        color: white;
        padding: 8px 15px;
        font-size: 0.85rem;
        transition: all 0.3s ease;
    }

    .auto-generate-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .profit-display {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
    }

    .profit-item {
        margin-bottom: 10px;
    }

    .profit-item:last-child {
        margin-bottom: 0;
    }

    .profit-value {
        font-size: 1.2rem;
        font-weight: bold;
    }

    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-back {
        border: 2px solid #6c757d;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .btn-back:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .input-group-text {
        background: #667eea;
        color: white;
        border: none;
        border-radius: 0 12px 12px 0;
    }

    .form-floating label {
        right: 15px;
        left: auto;
        color: #6c757d;
    }

    .required-asterisk {
        color: #dc3545;
        font-weight: bold;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .image-upload-area {
        border: 3px dashed #dee2e6;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .image-upload-area:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }

    .image-upload-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="product-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card">
                    <div class="form-header">
                        <i class="fas fa-box fa-3x mb-3"></i>
                        <h2 class="mb-2">إضافة منتج جديد</h2>
                        <p class="mb-0 opacity-75">أضف منتج جديد إلى المخزون مع جميع التفاصيل المطلوبة</p>
                    </div>
                    <div class="p-4">
                        <form action="<?php echo e(route('products.store')); ?>" method="POST" enctype="multipart/form-data" id="productForm">
                            <?php echo csrf_field(); ?>

                            <!-- المعلومات الأساسية -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    المعلومات الأساسية
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_ar" class="form-label">
                                                اسم المنتج (عربي) <span class="required-asterisk">*</span>
                                            </label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="name_ar" name="name_ar" value="<?php echo e(old('name_ar')); ?>"
                                                   placeholder="أدخل اسم المنتج بالعربية" required>
                                            <?php $__errorArgs = ['name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_en" class="form-label">اسم المنتج (إنجليزي)</label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['name_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="name_en" name="name_en" value="<?php echo e(old('name_en')); ?>"
                                                   placeholder="أدخل اسم المنتج بالإنجليزية">
                                            <?php $__errorArgs = ['name_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sku" class="form-label">
                                                رمز المنتج (SKU) <span class="required-asterisk">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="text" class="form-control <?php $__errorArgs = ['sku'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="sku" name="sku" value="<?php echo e(old('sku')); ?>"
                                                       placeholder="سيتم توليده تلقائياً" required>
                                                <button type="button" class="btn auto-generate-btn" id="generateSku">
                                                    <i class="fas fa-magic"></i> توليد
                                                </button>
                                            </div>
                                            <?php $__errorArgs = ['sku'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="barcode" class="form-label">الباركود</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control <?php $__errorArgs = ['barcode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="barcode" name="barcode" value="<?php echo e(old('barcode')); ?>"
                                                       placeholder="سيتم توليده تلقائياً">
                                                <button type="button" class="btn auto-generate-btn" id="generateBarcode">
                                                    <i class="fas fa-barcode"></i> توليد
                                                </button>
                                            </div>
                                            <?php $__errorArgs = ['barcode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="category_id" class="form-label">
                                                التصنيف <span class="required-asterisk">*</span>
                                            </label>
                                            <select class="form-select <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    id="category_id" name="category_id" required>
                                                <option value="">اختر التصنيف</option>
                                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($category->id); ?>"
                                                            <?php echo e(old('category_id') == $category->id ? 'selected' : ''); ?>>
                                                        <?php echo e($category->name_ar); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="unit" class="form-label">
                                                الوحدة <span class="required-asterisk">*</span>
                                            </label>
                                            <select class="form-select <?php $__errorArgs = ['unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    id="unit" name="unit" required>
                                                <option value="">اختر الوحدة</option>
                                                <?php $__currentLoopData = $units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($unit->name_ar); ?>"
                                                            <?php echo e(old('unit') == $unit->name_ar ? 'selected' : ''); ?>>
                                                        <?php echo e($unit->name_ar); ?>

                                                        <?php if($unit->symbol): ?>
                                                            (<?php echo e($unit->symbol); ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="default_supplier_id" class="form-label">
                                                المورد الافتراضي
                                            </label>
                                            <select class="form-select <?php $__errorArgs = ['default_supplier_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    id="default_supplier_id" name="default_supplier_id">
                                                <option value="">اختر المورد الافتراضي</option>
                                                <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($supplier->id); ?>"
                                                            <?php echo e(old('default_supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                                                        <?php echo e($supplier->name); ?>

                                                        <?php if($supplier->company_name): ?>
                                                            - <?php echo e($supplier->company_name); ?>

                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['default_supplier_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الأسعار الأساسية -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-dollar-sign"></i>
                                    الأسعار الأساسية
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="selling_price" class="form-label">
                                                سعر البيع الافتراضي (ر.ي) <span class="required-asterisk">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control <?php $__errorArgs = ['selling_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="selling_price" name="selling_price" value="<?php echo e(old('selling_price')); ?>"
                                                       step="0.01" min="0" placeholder="0.00" required>
                                                <span class="input-group-text">ر.ي</span>
                                            </div>
                                            <?php $__errorArgs = ['selling_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <small class="text-muted">سعر البيع الافتراضي، يمكن تغييره في كل دفعة</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="wholesale_price" class="form-label">سعر الجملة (ر.ي)</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control <?php $__errorArgs = ['wholesale_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="wholesale_price" name="wholesale_price" value="<?php echo e(old('wholesale_price')); ?>"
                                                       step="0.01" min="0" placeholder="0.00">
                                                <span class="input-group-text">ر.ي</span>
                                            </div>
                                            <?php $__errorArgs = ['wholesale_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <small class="text-muted">سعر البيع بالجملة (اختياري)</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="min_stock_level" class="form-label">
                                                الحد الأدنى للمخزون <span class="required-asterisk">*</span>
                                            </label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['min_stock_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="min_stock_level" name="min_stock_level" value="<?php echo e(old('min_stock_level', 5)); ?>"
                                                   min="0" placeholder="5" required>
                                            <?php $__errorArgs = ['min_stock_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <small class="text-muted">تنبيه عند وصول المخزون لهذا الحد</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="expiry_alert_days" class="form-label">
                                                أيام التنبيه لانتهاء الصلاحية
                                            </label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['expiry_alert_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="expiry_alert_days" name="expiry_alert_days" value="<?php echo e(old('expiry_alert_days', 30)); ?>"
                                                   min="1" max="365" placeholder="30">
                                            <?php $__errorArgs = ['expiry_alert_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <small class="text-muted">عدد الأيام قبل انتهاء الصلاحية للتنبيه</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الوصف والصورة -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-align-left"></i>
                                    الوصف والصورة
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="description_ar" class="form-label">الوصف (عربي)</label>
                                            <textarea class="form-control <?php $__errorArgs = ['description_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                      id="description_ar" name="description_ar" rows="4"
                                                      placeholder="أدخل وصف المنتج بالعربية..."><?php echo e(old('description_ar')); ?></textarea>
                                            <?php $__errorArgs = ['description_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="mb-3">
                                            <label for="description_en" class="form-label">الوصف (إنجليزي)</label>
                                            <textarea class="form-control <?php $__errorArgs = ['description_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                      id="description_en" name="description_en" rows="4"
                                                      placeholder="أدخل وصف المنتج بالإنجليزية..."><?php echo e(old('description_en')); ?></textarea>
                                            <?php $__errorArgs = ['description_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">صورة المنتج</label>
                                            <div class="image-upload-area" onclick="document.getElementById('product_image').click()">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <h6 class="text-muted">اضغط لرفع صورة المنتج</h6>
                                                <p class="text-muted small mb-0">أو اسحب الصورة هنا</p>
                                                <input type="file" id="product_image" name="image" accept="image/*" style="display: none;">
                                            </div>
                                            <div id="image-preview" class="mt-3" style="display: none;">
                                                <img id="preview-img" src="" alt="معاينة الصورة" class="img-fluid rounded" style="max-height: 200px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الإعدادات -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-cogs"></i>
                                    إعدادات المنتج
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                                   value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_active">
                                                <strong>منتج نشط</strong>
                                                <br><small class="text-muted">يظهر المنتج في نقطة البيع</small>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="track_stock" name="track_stock"
                                                   value="1" <?php echo e(old('track_stock', true) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="track_stock">
                                                <strong>تتبع المخزون</strong>
                                                <br><small class="text-muted">تحديث المخزون تلقائياً عند البيع</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="d-flex justify-content-between align-items-center pt-4">
                                <a href="<?php echo e(route('products.index')); ?>" class="btn btn-back">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    العودة للقائمة
                                </a>

                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-save">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المنتج
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// حساب الربح تلقائياً
function calculateProfit() {
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

    const profit = sellingPrice - purchasePrice;
    const profitMargin = purchasePrice > 0 ? (profit / purchasePrice) * 100 : 0;

    document.getElementById('profitAmount').textContent = profit.toFixed(2) + ' ر.ي';
    document.getElementById('profitMargin').textContent = profitMargin.toFixed(1) + '%';

    // تغيير لون العرض حسب الربح
    const profitDisplay = document.querySelector('.profit-display');
    if (profit > 0) {
        profitDisplay.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
    } else if (profit < 0) {
        profitDisplay.style.background = 'linear-gradient(135deg, #dc3545 0%, #fd7e14 100%)';
    } else {
        profitDisplay.style.background = 'linear-gradient(135deg, #6c757d 0%, #adb5bd 100%)';
    }
}

// توليد رمز المنتج (SKU) تلقائياً
async function generateSKU() {
    const categorySelect = document.getElementById('category_id');
    const nameAr = document.getElementById('name_ar').value;

    if (!nameAr) {
        alert('يرجى إدخال اسم المنتج أولاً');
        return;
    }

    try {
        const response = await fetch('/api/products/generate-sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                name: nameAr,
                category_id: categorySelect.value
            })
        });

        const data = await response.json();

        if (response.ok) {
            document.getElementById('sku').value = data.sku;
            showToast('تم توليد رمز المنتج بنجاح', 'success');
        } else {
            showToast(data.error || 'حدث خطأ أثناء توليد رمز المنتج', 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('حدث خطأ أثناء توليد رمز المنتج', 'danger');
    }
}

// توليد باركود تلقائياً
async function generateBarcode() {
    try {
        const response = await fetch('/api/products/generate-barcode', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (response.ok) {
            document.getElementById('barcode').value = data.barcode;
            showToast('تم توليد الباركود بنجاح', 'success');
        } else {
            showToast(data.error || 'حدث خطأ أثناء توليد الباركود', 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('حدث خطأ أثناء توليد الباركود', 'danger');
    }
}

// معاينة الصورة
function handleImagePreview() {
    const input = document.getElementById('product_image');
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');

    input.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('productForm').reset();
        document.getElementById('image-preview').style.display = 'none';
        calculateProfit();
        showToast('تم إعادة تعيين النموذج', 'info');
    }
}

// إظهار رسائل التنبيه
function showToast(message, type = 'success') {
    // إنشاء عنصر التنبيه
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// التحقق من صحة النموذج قبل الإرسال
function validateForm() {
    const form = document.getElementById('productForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    if (!isValid) {
        showToast('يرجى ملء جميع الحقول المطلوبة', 'danger');
        return false;
    }

    return true;
}

// إعداد الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // حساب الربح عند تغيير الأسعار
    document.getElementById('purchase_price').addEventListener('input', calculateProfit);
    document.getElementById('selling_price').addEventListener('input', calculateProfit);

    // توليد SKU تلقائياً عند تغيير اسم المنتج أو التصنيف
    document.getElementById('name_ar').addEventListener('input', function() {
        if (this.value && !document.getElementById('sku').value) {
            generateSKU();
        }
    });

    document.getElementById('category_id').addEventListener('change', function() {
        if (document.getElementById('name_ar').value && !document.getElementById('sku').value) {
            generateSKU();
        }
    });

    // أزرار التوليد التلقائي
    document.getElementById('generateSku').addEventListener('click', generateSKU);
    document.getElementById('generateBarcode').addEventListener('click', generateBarcode);

    // معاينة الصورة
    handleImagePreview();

    // التحقق من النموذج عند الإرسال
    document.getElementById('productForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });

    // حساب الربح الأولي
    calculateProfit();

    // توليد Barcode تلقائياً عند تحميل الصفحة
    setTimeout(() => {
        if (!document.getElementById('barcode').value) {
            generateBarcode();
        }
    }, 500);
});

// السحب والإفلات للصور
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.image-upload-area');
    const fileInput = document.getElementById('product_image');

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        uploadArea.classList.add('dragover');
    }

    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);
        }
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/products/create.blade.php ENDPATH**/ ?>