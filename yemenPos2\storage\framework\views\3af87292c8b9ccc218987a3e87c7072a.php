<?php $__env->startSection('title', 'إضافة عميل جديد - نظام نقطة المبيعات'); ?>
<?php $__env->startSection('page-title', 'إضافة عميل جديد'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .customer-form-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 200px);
        padding: 30px 0;
    }
    
    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }
    
    .form-header::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }
    
    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-section:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }
    
    .section-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }
    
    .section-title i {
        margin-left: 10px;
        font-size: 1.2rem;
    }
    
    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
    
    .btn-back {
        border: 2px solid #6c757d;
        border-radius: 12px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .btn-back:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }
    
    .required-asterisk {
        color: #dc3545;
        font-weight: bold;
    }
    
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .input-icon {
        position: relative;
    }
    
    .input-icon i {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }
    
    .input-icon .form-control {
        padding-right: 45px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="customer-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card">
                    <div class="form-header">
                        <i class="fas fa-user-plus fa-3x mb-3"></i>
                        <h2 class="mb-2">إضافة عميل جديد</h2>
                        <p class="mb-0 opacity-75">أضف عميل جديد إلى قاعدة البيانات</p>
                    </div>
                    <div class="p-4">
                        <form action="<?php echo e(route('customers.store')); ?>" method="POST" id="customerForm">
                            <?php echo csrf_field(); ?>
                            
                            <!-- المعلومات الأساسية -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    المعلومات الأساسية
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">
                                                اسم العميل <span class="required-asterisk">*</span>
                                            </label>
                                            <div class="input-icon">
                                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="name" name="name" value="<?php echo e(old('name')); ?>"
                                                       placeholder="أدخل اسم العميل الكامل" required>
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">
                                                رقم الهاتف <span class="required-asterisk">*</span>
                                            </label>
                                            <div class="input-icon">
                                                <input type="tel" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="phone" name="phone" value="<?php echo e(old('phone')); ?>"
                                                       placeholder="مثال: 0501234567" required>
                                                <i class="fas fa-phone"></i>
                                            </div>
                                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني</label>
                                            <div class="input-icon">
                                                <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="email" name="email" value="<?php echo e(old('email')); ?>"
                                                       placeholder="<EMAIL>">
                                                <i class="fas fa-envelope"></i>
                                            </div>
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-map-marker-alt"></i>
                                    معلومات إضافية
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <div class="input-icon">
                                        <textarea class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                  id="address" name="address" rows="3"
                                                  placeholder="أدخل عنوان العميل..."><?php echo e(old('address')); ?></textarea>
                                        <i class="fas fa-home"></i>
                                    </div>
                                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="notes" name="notes" rows="3"
                                              placeholder="أي ملاحظات إضافية عن العميل..."><?php echo e(old('notes')); ?></textarea>
                                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            
                            <!-- الإعدادات -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="fas fa-cogs"></i>
                                    إعدادات العميل
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                                   value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_active">
                                                <strong>عميل نشط</strong>
                                                <br><small class="text-muted">يظهر العميل في قوائم المبيعات</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- أزرار الإجراءات -->
                            <div class="d-flex justify-content-between align-items-center pt-4">
                                <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-back">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    العودة للقائمة
                                </a>
                                
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-save">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ العميل
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('customerForm').reset();
        showToast('تم إعادة تعيين النموذج', 'info');
    }
}

// إظهار رسائل التنبيه
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// التحقق من صحة النموذج قبل الإرسال
function validateForm() {
    const form = document.getElementById('customerForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    // التحقق من صحة رقم الهاتف
    const phone = document.getElementById('phone').value;
    const phoneRegex = /^[0-9+\-\s()]+$/;
    if (phone && !phoneRegex.test(phone)) {
        document.getElementById('phone').classList.add('is-invalid');
        isValid = false;
        showToast('يرجى إدخال رقم هاتف صحيح', 'danger');
    }
    
    // التحقق من صحة البريد الإلكتروني
    const email = document.getElementById('email').value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (email && !emailRegex.test(email)) {
        document.getElementById('email').classList.add('is-invalid');
        isValid = false;
        showToast('يرجى إدخال بريد إلكتروني صحيح', 'danger');
    }
    
    if (!isValid) {
        showToast('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'danger');
        return false;
    }
    
    return true;
}

// إعداد الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من النموذج عند الإرسال
    document.getElementById('customerForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // تنسيق رقم الهاتف أثناء الكتابة
    document.getElementById('phone').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 0) {
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('05')) {
                // Saudi mobile format
            } else if (value.length === 9 && !value.startsWith('0')) {
                value = '05' + value;
            }
        }
        e.target.value = value;
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/customers/create.blade.php ENDPATH**/ ?>