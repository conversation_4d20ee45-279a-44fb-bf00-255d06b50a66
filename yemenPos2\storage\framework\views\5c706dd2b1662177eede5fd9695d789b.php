<?php $__env->startSection('title', 'لوحة التحكم - نظام نقطة المبيعات'); ?>
<?php $__env->startSection('page-title', 'لوحة التحكم'); ?>

<?php $__env->startSection('content'); ?>
<!-- عرض الوقت الحالي -->
<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-info d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-info-circle me-2"></i>
                <strong>مرحباً بك في نظام نقطة المبيعات</strong>
            </div>
            <div class="text-end">
                <div class="badge bg-dark fs-6">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"><?php echo e(now()->format('Y-m-d H:i:s')); ?></span>
                </div>
                <div class="small text-muted">
                    التوقيت المحلي (اليمن +03:00)
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<!-- الصف الأول: عمودين -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="stats-card">
            <div class="stats-number"><?php echo e(number_format($todaySales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-calendar-day me-1"></i>
                مبيعات اليوم (ر.ي)
            </div>
            <div class="stats-period">إحصائية يومية</div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-number"><?php echo e(number_format($monthlySales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-calendar-alt me-1"></i>
                مبيعات الشهر (ر.ي)
            </div>
            <div class="stats-period">إحصائية شهرية</div>
        </div>
    </div>
</div>

<!-- الصف الثاني: ثلاثة أعمدة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);">
            <div class="stats-number"><?php echo e($todayTransactions); ?></div>
            <div class="stats-label">
                <i class="fas fa-receipt me-1"></i>
                معاملات اليوم
            </div>
            <div class="stats-period">إحصائية يومية</div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
            <div class="stats-number"><?php echo e($unpostedCount); ?></div>
            <div class="stats-label">
                <i class="fas fa-clock me-1"></i>
                فواتير غير مرحلة
            </div>
            <div class="stats-period">إحصائية عامة</div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
            <div class="stats-number"><?php echo e(number_format($unpostedSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-exclamation-triangle me-1"></i>
                قيمة غير مرحلة (ر.ي)
            </div>
            <div class="stats-period">إحصائية عامة</div>
        </div>
    </div>
</div>

<!-- الصف الثالث: أربعة أعمدة - إحصائيات طرق الدفع اليومية -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
            <div class="stats-number"><?php echo e(number_format($todayCashSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-money-bill-wave me-1"></i>
                النقدي المحصل اليومي (ر.ي)
            </div>
            <div class="stats-period">إحصائية يومية</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);">
            <div class="stats-number"><?php echo e(number_format($todayBankSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-university me-1"></i>
                البنك المحصل اليومي (ر.ي)
            </div>
            <div class="stats-period">إحصائية يومية</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
            <div class="stats-number"><?php echo e(number_format($todayCreditSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-calendar-plus me-1"></i>
                المديونية اليومية (ر.ي)
            </div>
            <div class="stats-period">إحصائية يومية</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);">
            <div class="stats-number"><?php echo e($todayUnposted); ?></div>
            <div class="stats-label">
                <i class="fas fa-clock me-1"></i>
                غير مرحلة اليوم
            </div>
            <div class="stats-period">إحصائية يومية</div>
        </div>
    </div>
</div>

<!-- الصف الرابع: أربعة أعمدة - إحصائيات طرق الدفع الشهرية -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); opacity: 0.8;">
            <div class="stats-number"><?php echo e(number_format($cashSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-money-bill-wave me-1"></i>
                النقدي المحصل الشهري (ر.ي)
            </div>
            <div class="stats-period">إحصائية شهرية</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); opacity: 0.8;">
            <div class="stats-number"><?php echo e(number_format($bankSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-university me-1"></i>
                البنك المحصل الشهري (ر.ي)
            </div>
            <div class="stats-period">إحصائية شهرية</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); opacity: 0.8;">
            <div class="stats-number"><?php echo e(number_format($creditSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-calendar-plus me-1"></i>
                المديونيات الشهرية (ر.ي)
            </div>
            <div class="stats-period">إحصائية شهرية</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="stats-number"><?php echo e($totalProducts); ?></div>
            <div class="stats-label">
                <i class="fas fa-box me-1"></i>
                إجمالي المنتجات
            </div>
            <div class="stats-period">إحصائية عامة</div>
        </div>
    </div>
</div>

<!-- الصف الخامس: أربعة أعمدة - إحصائيات متنوعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="stats-number"><?php echo e($refundedCount); ?></div>
            <div class="stats-label">
                <i class="fas fa-undo me-1"></i>
                إجمالي الفواتير المرتجعة
            </div>
            <div class="stats-period">إحصائية عامة</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-number"><?php echo e(number_format($refundedSales, 0)); ?></div>
            <div class="stats-label">
                <i class="fas fa-chart-line me-1"></i>
                قيمة المرتجعات (ر.ي)
            </div>
            <div class="stats-period">إحصائية عامة</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
            <div class="stats-number"><?php echo e($todayRefunded); ?></div>
            <div class="stats-label">
                <i class="fas fa-calendar-day me-1"></i>
                مرتجعة اليوم
            </div>
            <div class="stats-period">إحصائية يومية</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);">
            <div class="stats-number"><?php echo e($lowStockProducts); ?></div>
            <div class="stats-label">
                <i class="fas fa-exclamation-triangle me-1"></i>
                منتجات مخزون منخفض
            </div>
            <div class="stats-period">إحصائية عامة</div>
        </div>
    </div>
</div>

<!-- تنبيهات المخزون -->
<?php if($lowStockProducts > 0): ?>
<div class="alert alert-warning" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>تنبيه:</strong> يوجد <?php echo e($lowStockProducts); ?> منتج بمخزون منخفض.
    <a href="<?php echo e(route('products.index')); ?>?filter=low_stock" class="alert-link">عرض المنتجات</a>
</div>
<?php endif; ?>

<div class="row">
    <!-- أحدث المبيعات -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    أحدث المبيعات
                </h5>
                <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if($recentSales->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>حالة الدفع</th>
                                    <th>حالة الترحيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <a href="<?php echo e(route('sales.show', $sale)); ?>" class="text-decoration-none">
                                            <?php echo e($sale->invoice_number); ?>

                                        </a>
                                    </td>
                                    <td><?php echo e($sale->customer->name ?? 'عميل عادي'); ?></td>
                                    <td>
                                        <span class="fw-bold text-success">
                                            <?php echo e(number_format($sale->total_amount, 2)); ?> ر.ي
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo e($sale->sale_date->format('Y-m-d')); ?>

                                            <br>
                                            <small class="text-muted">
                                                <?php echo e($sale->sale_time->format('g:i')); ?>

                                                <?php echo e($sale->sale_time->format('A') == 'AM' ? 'ص' : 'م'); ?>

                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($sale->status === 'مكتملة'): ?>
                                            <span class="badge bg-success">مكتملة</span>
                                        <?php elseif($sale->status === 'غير مدفوعة'): ?>
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        <?php elseif($sale->status === 'دفع جزئي'): ?>
                                            <span class="badge bg-warning">دفع جزئي</span>
                                        <?php elseif($sale->status === 'مسترجعة'): ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-undo me-1"></i>مسترجعة
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-info"><?php echo e($sale->status); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($sale->is_posted): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>مرحلة
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock me-1"></i>غير مرحلة
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مبيعات حتى الآن</p>
                        <a href="<?php echo e(route('pos.index')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء فاتورة جديدة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- المنتجات الأكثر مبيعاً -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    المنتجات الأكثر مبيعاً
                </h5>
            </div>
            <div class="card-body">
                <?php if($topProducts->count() > 0): ?>
                    <?php $__currentLoopData = $topProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-1"><?php echo e($product->name_ar); ?></h6>
                            <small class="text-muted"><?php echo e($product->sku); ?></small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary"><?php echo e($product->total_sold ?? 0); ?></span>
                            <br>
                            <small class="text-muted">قطعة</small>
                        </div>
                    </div>
                    <?php if(!$loop->last): ?>
                        <hr>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-2x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بيانات مبيعات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    مبيعات آخر 7 أيام
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary"><?php echo e($totalCustomers); ?></h4>
                        <p class="text-muted mb-0">إجمالي العملاء</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning"><?php echo e($monthlyTransactions); ?></h4>
                        <p class="text-muted mb-0">معاملات الشهر</p>
                    </div>
                </div>

                <hr>

                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('pos.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        فاتورة جديدة
                    </a>
                    <a href="<?php echo e(route('products.create')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-box me-2"></i>
                        إضافة منتج
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* تصغير حجم الخط في الإحصائيات */
.stats-card .stats-number {
    font-size: 1.8rem !important; /* تصغير أكثر للأرقام */
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-card .stats-label {
    font-size: 0.8rem !important; /* تصغير النص */
    opacity: 0.9;
    margin-bottom: 8px;
}

/* تسمية نوع الإحصائية */
.stats-card .stats-period {
    font-size: 0.7rem !important;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 3px 8px;
    border-radius: 10px;
    display: inline-block;
    margin-top: 5px;
}

/* تحسين المظهر للشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-card .stats-number {
        font-size: 1.4rem !important;
    }

    .stats-card .stats-label {
        font-size: 0.7rem !important;
    }

    .stats-card .stats-period {
        font-size: 0.6rem !important;
        padding: 2px 6px;
    }
}

/* تحسين responsive للصفوف الجديدة */
@media (max-width: 768px) {
    .col-md-6, .col-md-4, .col-md-3, .col-md-2 {
        margin-bottom: 15px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمبيعات
const ctx = document.getElementById('salesChart').getContext('2d');
const salesData = <?php echo json_encode($salesChart, 15, 512) ?>;

new Chart(ctx, {
    type: 'line',
    data: {
        labels: salesData.map(item => item.date),
        datasets: [{
            label: 'المبيعات (ر.ي)',
            data: salesData.map(item => item.amount),
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ر.ي';
                    }
                }
            }
        }
    }
});

// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const options = {
        timeZone: 'Asia/Aden',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };

    const timeString = now.toLocaleString('en-CA', options).replace(',', '');
    document.getElementById('current-time').textContent = timeString;
}

// تحديث الوقت كل ثانية
setInterval(updateTime, 1000);
// تحديث فوري عند تحميل الصفحة
updateTime();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/dashboard.blade.php ENDPATH**/ ?>