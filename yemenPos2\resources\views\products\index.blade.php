@extends('layouts.app')

@section('title', 'إدارة المنتجات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-boxes me-2"></i>
                        إدارة المنتجات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المنتجات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('products.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المنتجات</h6>
                                    <h3 class="mb-0">{{ $products->total() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-boxes fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المنتجات المتاحة</h6>
                                    <h3 class="mb-0">{{ $products->where('total_stock', '>', 0)->count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">مخزون منخفض</h6>
                                    <h3 class="mb-0">{{ $products->filter(function($p) { return $p->isLowStock(); })->count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">نفد المخزون</h6>
                                    <h3 class="mb-0">{{ $products->filter(function($p) { return $p->isOutOfStock(); })->count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('products.index') }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">البحث</label>
                                    <input type="text" name="search" class="form-control" 
                                           value="{{ request('search') }}" 
                                           placeholder="ابحث بالاسم، الكود، أو الباركود...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">التصنيف</label>
                                    <select name="category" class="form-select">
                                        <option value="">جميع التصنيفات</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" 
                                                    {{ request('category') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name_ar }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">الفلتر</label>
                                    <select name="filter" class="form-select">
                                        <option value="">جميع المنتجات</option>
                                        <option value="low_stock" {{ request('filter') == 'low_stock' ? 'selected' : '' }}>
                                            مخزون منخفض
                                        </option>
                                        <option value="out_of_stock" {{ request('filter') == 'out_of_stock' ? 'selected' : '' }}>
                                            نفد المخزون
                                        </option>
                                        <option value="expired" {{ request('filter') == 'expired' ? 'selected' : '' }}>
                                            منتهي الصلاحية
                                        </option>
                                        <option value="near_expiry" {{ request('filter') == 'near_expiry' ? 'selected' : '' }}>
                                            قريب الانتهاء
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المنتجات
                        <span class="badge bg-secondary ms-2">{{ $products->total() }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($products->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="15%">المنتج</th>
                                        <th width="10%">الكود</th>
                                        <th width="10%">التصنيف</th>
                                        <th width="8%">الكمية الإجمالية</th>
                                        <th width="8%">عدد الدفعات</th>
                                        <th width="10%">سعر البيع</th>
                                        <th width="8%">نسبة الربح</th>
                                        <th width="10%">حالة الصلاحية</th>
                                        <th width="8%">حالة المخزون</th>
                                        <th width="8%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($products as $index => $product)
                                        <tr>
                                            <td>{{ $products->firstItem() + $index }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($product->image)
                                                        <img src="{{ asset('storage/' . $product->image) }}" 
                                                             alt="{{ $product->name_ar }}" 
                                                             class="rounded me-2" 
                                                             style="width: 40px; height: 40px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-box text-white"></i>
                                                        </div>
                                                    @endif
                                                    <div>
                                                        <strong>{{ $product->name_ar }}</strong>
                                                        @if($product->name_en)
                                                            <br><small class="text-muted">{{ $product->name_en }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code>{{ $product->sku }}</code>
                                                @if($product->barcode)
                                                    <br><small class="text-muted">{{ $product->barcode }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($product->category)
                                                    <span class="badge bg-info">{{ $product->category->name_ar }}</span>
                                                @else
                                                    <span class="text-muted">غير محدد</span>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ number_format($product->total_stock, 2) }}</strong>
                                                <br><small class="text-muted">{{ $product->unit }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ $product->available_batches_count }}</span>
                                                @if($product->available_batches_count > 0)
                                                    <br><small class="text-muted">دفعة متاحة</small>
                                                @else
                                                    <br><small class="text-muted">لا توجد دفعات</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($product->selling_price)
                                                    <strong>{{ number_format($product->selling_price, 2) }} ر.ي</strong>
                                                @else
                                                    <span class="text-muted">غير محدد</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ $product->profit_margin }}%</span>
                                            </td>
                                            <td>
                                                @php
                                                    $expiryStatus = $product->expiry_status;
                                                @endphp
                                                @if($expiryStatus === 'expired')
                                                    <span class="badge bg-danger">منتهي الصلاحية</span>
                                                @elseif($expiryStatus === 'near_expiry')
                                                    <span class="badge bg-warning">قريب الانتهاء</span>
                                                @elseif($expiryStatus === 'valid')
                                                    <span class="badge bg-success">صالح</span>
                                                @else
                                                    <span class="badge bg-secondary">لا ينتهي</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($product->isOutOfStock())
                                                    <span class="badge bg-danger">نفد المخزون</span>
                                                @elseif($product->isLowStock())
                                                    <span class="badge bg-warning">مخزون منخفض</span>
                                                @else
                                                    <span class="badge bg-success">متوفر</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('products.show', $product) }}" 
                                                       class="btn btn-sm btn-info" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('products.edit', $product) }}" 
                                                       class="btn btn-sm btn-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('products.destroy', $product) }}" 
                                                          method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                                title="حذف" 
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                عرض {{ $products->firstItem() }} إلى {{ $products->lastItem() }} 
                                من أصل {{ $products->total() }} منتج
                            </div>
                            <div>
                                {{ $products->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات</h5>
                            <p class="text-muted">لم يتم العثور على منتجات مطابقة لمعايير البحث</p>
                            <a href="{{ route('products.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    font-weight: 600;
    border-top: none;
}

.badge {
    font-size: 0.75em;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-left: 2px;
}

.table-responsive {
    border-radius: 0.375rem;
}
</style>
@endpush
