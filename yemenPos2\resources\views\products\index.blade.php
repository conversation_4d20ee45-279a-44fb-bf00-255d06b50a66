@extends('layouts.app')

@section('title', 'إدارة المنتجات - نظام نقطة المبيعات')
@section('page-title', 'إدارة المنتجات')

@section('content')
<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('products.index') }}">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search"
                               value="{{ request('search') }}"
                               placeholder="البحث بالاسم أو الرمز أو الباركود...">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="category" onchange="this.form.submit()">
                        <option value="">جميع التصنيفات</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}"
                                    {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->name_ar }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="filter" onchange="this.form.submit()">
                        <option value="">جميع المنتجات</option>
                        <option value="low_stock" {{ request('filter') === 'low_stock' ? 'selected' : '' }}>
                            مخزون منخفض
                        </option>
                    </select>
                </div>
                <div class="col-md-2">
                    <a href="{{ route('products.create') }}" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-box me-2"></i>
            قائمة المنتجات ({{ $products->total() }})
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        @if($products->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>اسم المنتج</th>
                            <th>الرمز</th>
                            <th>التصنيف</th>
                            <th>سعر الشراء</th>
                            <th>سعر البيع</th>
                            <th>المخزون</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($products as $product)
                        <tr class="{{ $product->isLowStock() ? 'table-warning' : '' }}">
                            <td>
                                @if($product->image)
                                    <img src="{{ asset('storage/' . $product->image) }}"
                                         alt="{{ $product->name_ar }}"
                                         class="rounded"
                                         style="width: 50px; height: 50px; object-fit: cover;">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <h6 class="mb-0">{{ $product->name_ar }}</h6>
                                    @if($product->name_en)
                                        <small class="text-muted">{{ $product->name_en }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $product->sku }}</span>
                                @if($product->barcode)
                                    <br><small class="text-muted">{{ $product->barcode }}</small>
                                @endif
                            </td>
                            <td>{{ $product->category->name_ar ?? 'غير مصنف' }}</td>
                            <td>{{ number_format($product->purchase_price, 2) }} ر.ي</td>
                            <td>
                                <span class="fw-bold text-success">
                                    {{ number_format($product->selling_price, 2) }} ر.ي
                                </span>
                                <br>
                                <small class="text-muted">
                                    ربح: {{ number_format($product->profit, 2) }} ر.ي
                                    ({{ number_format($product->profit_margin, 1) }}%)
                                </small>
                            </td>
                            <td>
                                <span class="badge {{ $product->isOutOfStock() ? 'bg-danger' : ($product->isLowStock() ? 'bg-warning' : 'bg-success') }}">
                                    {{ $product->stock_quantity }} {{ $product->unit }}
                                </span>
                                @if($product->isLowStock())
                                    <br><small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        مخزون منخفض
                                    </small>
                                @endif
                            </td>
                            <td>
                                @if($product->is_active)
                                    <span class="badge bg-success">نشط</span>
                                @else
                                    <span class="badge bg-secondary">غير نشط</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('products.show', $product) }}"
                                       class="btn btn-sm btn-outline-info"
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('products.edit', $product) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger"
                                            title="حذف"
                                            onclick="deleteProduct({{ $product->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="صفحات المنتجات">
                    {{ $products->links('pagination::bootstrap-4') }}
                </nav>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد منتجات</h5>
                <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
                <a href="{{ route('products.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول منتج
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين البطاقات الإحصائية */
.card h5 {
    font-size: 1.5rem;
    font-weight: bold;
}

.card p {
    font-size: 0.875rem;
}

/* تحسين الفلاتر */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-control, .form-select {
    font-size: 0.875rem;
}

/* تحسين breadcrumb */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item {
    font-size: 0.875rem;
}

/* تحسين الـ badges */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>
@endpush

@push('scripts')
<script>
function deleteProduct(productId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/products/${productId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-submit search form on input
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
@endpush
