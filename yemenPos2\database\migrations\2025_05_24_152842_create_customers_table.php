<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم العميل
            $table->string('phone')->unique(); // رقم الهاتف
            $table->string('whatsapp')->nullable(); // رقم الواتساب
            $table->string('email')->nullable(); // البريد الإلكتروني
            $table->text('address')->nullable(); // العنوان
            $table->decimal('credit_limit', 10, 2)->default(0); // حد الائتمان
            $table->decimal('current_balance', 10, 2)->default(0); // الرصيد الحالي
            $table->enum('customer_type', ['نقدي', 'آجل', 'مختلط'])->default('نقدي'); // نوع العميل
            $table->boolean('is_active')->default(true); // نشط/غير نشط
            $table->date('birth_date')->nullable(); // تاريخ الميلاد
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
