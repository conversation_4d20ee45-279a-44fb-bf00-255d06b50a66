<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // إعدادات عامة
            ['key' => 'company_name', 'value' => 'متجر نقطة المبيعات', 'type' => 'string', 'group' => 'company', 'label' => 'اسم الشركة', 'description' => 'اسم الشركة أو المتجر', 'is_public' => true],
            ['key' => 'company_address', 'value' => 'صنعاء، اليمن', 'type' => 'string', 'group' => 'company', 'label' => 'عنوان الشركة', 'description' => 'عنوان الشركة أو المتجر', 'is_public' => true],
            ['key' => 'company_phone', 'value' => '+967-1-234567', 'type' => 'string', 'group' => 'company', 'label' => 'هاتف الشركة', 'description' => 'رقم هاتف الشركة', 'is_public' => true],
            ['key' => 'company_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'company', 'label' => 'بريد الشركة', 'description' => 'البريد الإلكتروني للشركة', 'is_public' => true],
            ['key' => 'company_logo', 'value' => null, 'type' => 'file', 'group' => 'company', 'label' => 'شعار الشركة', 'description' => 'شعار الشركة', 'is_public' => true],

            // إعدادات العملة
            ['key' => 'default_currency', 'value' => 'YER', 'type' => 'string', 'group' => 'currency', 'label' => 'العملة الافتراضية', 'description' => 'العملة الافتراضية للنظام', 'is_public' => true],
            ['key' => 'currency_symbol', 'value' => 'ر.ي', 'type' => 'string', 'group' => 'currency', 'label' => 'رمز العملة', 'description' => 'رمز العملة المعروض', 'is_public' => true],
            ['key' => 'currency_position', 'value' => 'after', 'type' => 'string', 'group' => 'currency', 'label' => 'موضع رمز العملة', 'description' => 'موضع رمز العملة (before/after)', 'is_public' => true],

            // إعدادات الضرائب
            ['key' => 'tax_enabled', 'value' => 'false', 'type' => 'boolean', 'group' => 'tax', 'label' => 'تفعيل الضرائب', 'description' => 'تفعيل نظام الضرائب', 'is_public' => false],
            ['key' => 'tax_rate', 'value' => '0', 'type' => 'number', 'group' => 'tax', 'label' => 'معدل الضريبة', 'description' => 'معدل الضريبة بالنسبة المئوية', 'is_public' => false],
            ['key' => 'tax_number', 'value' => null, 'type' => 'string', 'group' => 'tax', 'label' => 'الرقم الضريبي', 'description' => 'الرقم الضريبي للشركة', 'is_public' => true],

            // إعدادات الفواتير
            ['key' => 'invoice_prefix', 'value' => 'INV-', 'type' => 'string', 'group' => 'invoice', 'label' => 'بادئة رقم الفاتورة', 'description' => 'البادئة المستخدمة في أرقام الفواتير', 'is_public' => false],
            ['key' => 'invoice_start_number', 'value' => '1', 'type' => 'number', 'group' => 'invoice', 'label' => 'رقم البداية للفواتير', 'description' => 'الرقم الذي تبدأ منه الفواتير', 'is_public' => false],
            ['key' => 'invoice_footer', 'value' => 'شكراً لتعاملكم معنا', 'type' => 'string', 'group' => 'invoice', 'label' => 'تذييل الفاتورة', 'description' => 'النص المعروض في أسفل الفاتورة', 'is_public' => true],

            // إعدادات الواتساب
            ['key' => 'whatsapp_enabled', 'value' => 'true', 'type' => 'boolean', 'group' => 'whatsapp', 'label' => 'تفعيل الواتساب', 'description' => 'تفعيل إرسال الفواتير عبر الواتساب', 'is_public' => false],
            ['key' => 'whatsapp_message_template', 'value' => 'مرحباً {customer_name}، إليك فاتورتك رقم {invoice_number} بمبلغ {total_amount} {currency}. شكراً لتعاملكم معنا.', 'type' => 'string', 'group' => 'whatsapp', 'label' => 'قالب رسالة الواتساب', 'description' => 'قالب الرسالة المرسلة عبر الواتساب', 'is_public' => false],

            // إعدادات المخزون
            ['key' => 'low_stock_alert', 'value' => 'true', 'type' => 'boolean', 'group' => 'inventory', 'label' => 'تنبيه نفاد المخزون', 'description' => 'تفعيل تنبيهات نفاد المخزون', 'is_public' => false],
            ['key' => 'auto_update_stock', 'value' => 'true', 'type' => 'boolean', 'group' => 'inventory', 'label' => 'تحديث المخزون تلقائياً', 'description' => 'تحديث المخزون تلقائياً عند البيع', 'is_public' => false],

            // إعدادات النظام
            ['key' => 'system_language', 'value' => 'ar', 'type' => 'string', 'group' => 'system', 'label' => 'لغة النظام', 'description' => 'اللغة الافتراضية للنظام', 'is_public' => true],
            ['key' => 'date_format', 'value' => 'Y-m-d', 'type' => 'string', 'group' => 'system', 'label' => 'تنسيق التاريخ', 'description' => 'تنسيق عرض التاريخ', 'is_public' => true],
            ['key' => 'time_format', 'value' => 'H:i', 'type' => 'string', 'group' => 'system', 'label' => 'تنسيق الوقت', 'description' => 'تنسيق عرض الوقت', 'is_public' => true],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
