# تحديث إعدادات العملة

## التغييرات المطبقة

### ✅ **تم الحذف:**
1. **سعر صرف الجنيه المصري** (`exchange_rate_egp`)
2. **سعر صرف الدولار الأمريكي** (`exchange_rate_usd`)
3. **سعر صرف الريال السعودي** (`exchange_rate_sar`)
4. **التحديث التلقائي لأسعار الصرف** (`auto_update_exchange_rates`)

### ✅ **الإعدادات المؤكدة:**
1. **العملة الافتراضية**: الريال اليمني (YER)
2. **رمز العملة**: ر.ي
3. **موضع العملة**: بعد الرقم (after)

## الملفات المُحدثة

### 1. Migration جديد
- `database/migrations/2024_01_15_000001_remove_exchange_rate_settings.php`
- يحذف إعدادات أسعار الصرف غير المرغوب فيها
- يؤكد الإعدادات الصحيحة للعملة اليمنية

### 2. ملفات المساعدة
- `currency_cleanup.bat` - تشغيل التحديثات تلقائياً
- `check_currency_settings.php` - فحص الإعدادات الحالية

## كيفية التطبيق

### الطريقة الأولى: استخدام ملف batch
```bash
# في مجلد المشروع
currency_cleanup.bat
```

### الطريقة الثانية: تشغيل الأوامر يدوياً
```bash
# تشغيل migration
php artisan migrate --force

# مسح الكاش
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# فحص النتائج
php check_currency_settings.php
```

## النتائج المتوقعة

بعد تطبيق التحديثات:

### ✅ **في صفحة الإعدادات:**
- لن تظهر حقول أسعار الصرف للعملات الأخرى
- ستظهر فقط إعدادات العملة اليمنية:
  - العملة الافتراضية: YER
  - رمز العملة: ر.ي
  - موضع العملة: بعد

### ✅ **في الفواتير:**
- جميع المبالغ ستظهر بالريال اليمني
- رمز العملة سيظهر بعد الرقم: `1000 ر.ي`
- لن تكون هناك خيارات تحويل عملة

### ✅ **في النظام:**
- جميع العمليات المالية بالريال اليمني فقط
- تبسيط واجهة المستخدم
- إزالة التعقيدات غير المطلوبة

## التحقق من النجاح

### 1. فحص قاعدة البيانات
```sql
SELECT * FROM settings WHERE `group` = 'currency';
```

يجب أن تظهر فقط:
- `default_currency` = YER
- `currency_symbol` = ر.ي
- `currency_position` = after

### 2. فحص صفحة الإعدادات
- اذهب إلى الإعدادات → إعدادات العملة
- يجب ألا تظهر حقول أسعار الصرف
- يجب أن تظهر فقط الإعدادات الأساسية للعملة

### 3. فحص الفواتير
- أنشئ فاتورة جديدة
- تأكد من ظهور العملة بالشكل: `المبلغ ر.ي`

## استكشاف الأخطاء

### إذا لم تختف حقول أسعار الصرف:
1. تأكد من تشغيل migration بنجاح
2. امسح الكاش: `php artisan cache:clear`
3. أعد تحميل الصفحة

### إذا ظهرت أخطاء في النظام:
1. تحقق من سجلات الأخطاء: `storage/logs/`
2. تأكد من أن العملة الافتراضية مُعرفة بشكل صحيح
3. أعد تشغيل الخادم

## التراجع عن التغييرات

إذا كنت تريد إعادة أسعار الصرف:
```bash
php artisan migrate:rollback --step=1
```

## الدعم

إذا واجهت أي مشاكل:
1. تشغيل `check_currency_settings.php` لفحص الحالة الحالية
2. مراجعة سجلات الأخطاء
3. التأكد من تشغيل جميع الأوامر المطلوبة

---

**ملاحظة**: هذا التحديث يبسط النظام ويركز على العملة اليمنية فقط، مما يجعل الاستخدام أسهل وأوضح للمستخدمين اليمنيين.
