<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تحديث جميع الحالات من "معلقة" إلى "غير مدفوعة"
        DB::table('sales')
            ->where('status', 'معلقة')
            ->update(['status' => 'غير مدفوعة']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // العكس: تحديث من "غير مدفوعة" إلى "معلقة"
        DB::table('sales')
            ->where('status', 'غير مدفوعة')
            ->update(['status' => 'معلقة']);
    }
};
