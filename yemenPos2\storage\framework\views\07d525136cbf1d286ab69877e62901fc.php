<?php $__env->startSection('title', 'إدارة المستخدمين'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المستخدمين</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>مستخدم جديد
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المستخدمين</h6>
                                    <h4><?php echo e($users->total()); ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المستخدمين النشطين</h6>
                                    <h4><?php echo e($users->where('status', 'نشط')->count()); ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المستخدمين غير النشطين</h6>
                                    <h4><?php echo e($users->where('status', 'غير نشط')->count()); ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-times fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المتصلين اليوم</h6>
                                    <h4><?php echo e($users->where('last_login_at', '>=', today())->count()); ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sign-in-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($users->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الصورة</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>آخر دخول</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="user-avatar">
                                            <?php if($user->avatar): ?>
                                                <img src="<?php echo e(asset('storage/' . $user->avatar)); ?>"
                                                     alt="<?php echo e($user->name); ?>" class="rounded-circle" width="40" height="40">
                                            <?php else: ?>
                                                <div class="avatar-placeholder">
                                                    <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($user->name); ?></strong>
                                            <?php if($user->id === auth()->id()): ?>
                                                <span class="badge bg-info ms-1">أنت</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?php echo e($user->email); ?></td>
                                    <td><?php echo e($user->phone ?? 'غير محدد'); ?></td>
                                    <td>
                                        <?php if($user->role): ?>
                                            <span class="badge bg-secondary"><?php echo e($user->role->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($user->is_active): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($user->last_login_at): ?>
                                            <?php echo e($user->last_login_at->diffForHumans()); ?>

                                        <?php else: ?>
                                            <span class="text-muted">لم يدخل بعد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($user->created_at->format('Y-m-d')); ?>

                                        <br><small class="text-muted"><?php echo e($user->created_at->format('H:i')); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('users.show', $user)); ?>"
                                               class="btn btn-sm btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('users.edit', $user)); ?>"
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if($user->id !== auth()->id() && $user->email !== '<EMAIL>'): ?>
                                            <button class="btn btn-sm btn-danger"
                                                    onclick="deleteUser(<?php echo e($user->id); ?>)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="صفحات المستخدمين">
                            <?php echo e($users->links('pagination::bootstrap-4')); ?>

                        </nav>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مستخدمين</h5>
                        <p class="text-muted">ابدأ بإضافة مستخدم جديد</p>
                        <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>مستخدم جديد
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المستخدم؟</p>
                <p class="text-danger"><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>

<script>
function deleteUser(id) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/users/${id}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/users/index.blade.php ENDPATH**/ ?>