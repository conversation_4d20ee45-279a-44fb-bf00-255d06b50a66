<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\POSController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\Auth\LoginController;

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// الصفحة الرئيسية - إعادة توجيه إلى لوحة التحكم
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Protected Routes (require authentication)
Route::middleware(['auth'])->group(function () {
    // لوحة التحكم
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // إدارة التصنيفات
    Route::resource('categories', CategoryController::class);

    // إدارة الوحدات
    Route::resource('units', UnitController::class);

    // إدارة المنتجات
    Route::resource('products', ProductController::class);

    // إدارة العملاء
    Route::resource('customers', CustomerController::class);

    // إدارة الموردين
    Route::resource('suppliers', SupplierController::class);

    // إدارة المشتريات
    Route::resource('purchases', PurchaseController::class);

    // التقارير
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('sales', [ReportController::class, 'sales'])->name('sales');
        Route::get('purchases', [ReportController::class, 'purchases'])->name('purchases');
        Route::get('inventory', [ReportController::class, 'inventory'])->name('inventory');
        Route::get('profit-loss', [ReportController::class, 'profitLoss'])->name('profit-loss');
        Route::get('customers', [ReportController::class, 'customers'])->name('customers');
    });

    // إدارة المستخدمين
    Route::resource('users', UserController::class);
    Route::post('users/{user}/change-password', [UserController::class, 'changePassword'])->name('users.change-password');

    // إدارة المدفوعات
    Route::resource('payments', PaymentController::class);

    // إدارة الإعدادات
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingController::class, 'index'])->name('index');
        Route::put('/update', [SettingController::class, 'update'])->name('update');
        Route::post('/reset', [SettingController::class, 'reset'])->name('reset');
        Route::get('/export', [SettingController::class, 'export'])->name('export');
        Route::post('/import', [SettingController::class, 'import'])->name('import');
    });

    // نقطة البيع
    Route::get('/pos', [POSController::class, 'index'])->name('pos.index');
    Route::post('/pos/search-products', [POSController::class, 'searchProducts'])->name('pos.search-products');
    Route::post('/pos/search-barcode', [POSController::class, 'searchByBarcode'])->name('pos.search-barcode');
    Route::post('/pos/create-sale', [POSController::class, 'createSale'])->name('pos.create-sale');

    // اختبار POS
    Route::get('/test-pos', function() {
        return view('test-pos');
    });

    // إدارة المبيعات
    Route::resource('sales', SaleController::class);
    Route::get('sales/{sale}/print', [SaleController::class, 'print'])->name('sales.print');

    // إدارة المديونيات
    Route::get('sales-debts', [SaleController::class, 'debts'])->name('sales.debts');
    Route::post('sales/pay-debt', [SaleController::class, 'payDebt'])->name('sales.pay-debt');

    // إدارة المديونيات المتقدمة
    Route::get('debts', [App\Http\Controllers\DebtController::class, 'index'])->name('debts.index');
    Route::post('debts/pay', [App\Http\Controllers\DebtController::class, 'payCustomerDebts'])->name('debts.pay');
    Route::get('debts/customer/{customer}', [App\Http\Controllers\DebtController::class, 'getCustomerDebts'])->name('debts.customer');

    // كشف حساب العملاء
    Route::get('statements', [App\Http\Controllers\CustomerStatementController::class, 'index'])->name('statements.index');
    Route::get('statements/export-pdf', [App\Http\Controllers\CustomerStatementController::class, 'exportPdf'])->name('statements.export-pdf');
    Route::post('statements/send-whatsapp', [App\Http\Controllers\CustomerStatementController::class, 'sendWhatsApp'])->name('statements.send-whatsapp');
    Route::get('statements/get-statement', [App\Http\Controllers\CustomerStatementController::class, 'getStatement'])->name('statements.get-statement');
    Route::get('statements/download-temp/{filename}', [App\Http\Controllers\CustomerStatementController::class, 'downloadTempPdf'])->name('statements.download-temp');

    // اختبار PDF
    Route::get('test-pdf', [App\Http\Controllers\TestPdfController::class, 'test']);

    // ترحيل المبيعات
    Route::get('sales-unposted', [SaleController::class, 'unposted'])->name('sales.unposted');
    Route::get('sales-posted', [SaleController::class, 'posted'])->name('sales.posted');
    Route::get('sales-refunded', [SaleController::class, 'refunded'])->name('sales.refunded');
    Route::post('sales/{sale}/post', [SaleController::class, 'post'])->name('sales.post');
    Route::post('sales/{sale}/unpost', [SaleController::class, 'unpost'])->name('sales.unpost');
    Route::post('sales/post-multiple', [SaleController::class, 'postMultiple'])->name('sales.post-multiple');

    // عمليات الفواتير غير المرحلة
    Route::post('sales/{sale}/refund', [SaleController::class, 'refund'])->name('sales.refund');
    Route::delete('sales/{sale}/force-delete', [SaleController::class, 'forceDelete'])->name('sales.force-delete');

    // مسارات إضافية للمبيعات
    Route::prefix('sales')->name('sales.')->group(function () {
        Route::post('{sale}/send-whatsapp', [SaleController::class, 'sendWhatsApp'])->name('send-whatsapp'); // إرسال واتساب
    });

    // مسارات API للبحث السريع
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('products/search', [ProductController::class, 'search'])->name('products.search');
        Route::get('products/barcode/{barcode}', [ProductController::class, 'findByBarcode'])->name('products.barcode');
        Route::post('products/generate-sku', [ProductController::class, 'generateSkuApi'])->name('products.generate-sku');
        Route::post('products/generate-barcode', [ProductController::class, 'generateBarcodeApi'])->name('products.generate-barcode');

        // API للمدفوعات
        Route::get('sales/unpaid', [SaleController::class, 'getUnpaidSales'])->name('sales.unpaid');
        Route::get('purchases/unpaid', [PurchaseController::class, 'getUnpaidPurchases'])->name('purchases.unpaid');
    });
});
