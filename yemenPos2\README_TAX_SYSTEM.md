# نظام الضرائب التلقائي في نقطة البيع

## الميزات الجديدة

### ✅ **حساب الضريبة التلقائي**
- يتم حساب الضريبة تلقائياً حسب الإعدادات
- لا حاجة لإدخال قيمة الضريبة يدوياً
- يتم إعادة حساب الضريبة عند تغيير المنتجات أو الخصم

### ✅ **التحكم من الإعدادات**
- تفعيل/إلغاء تفعيل الضريبة من لوحة التحكم
- تحديد معدل الضريبة (نسبة مئوية)
- تخصيص اسم الضريبة

### ✅ **واجهة ذكية**
- إظهار/إخفاء حقل الضريبة حسب التفعيل
- تحديث تلقائي للتسميات
- حساب الضريبة على المبلغ بعد الخصم

## كيفية الاستخدام

### 1. تفعيل الضريبة
1. اذهب إلى **الإعدادات** → **إعدادات الضرائب**
2. فعل خيار **"تفعيل الضرائب"**
3. حدد **معدل الضريبة** (مثال: 15 للضريبة 15%)
4. اختياري: غير **اسم الضريبة** (افتراضي: ضريبة القيمة المضافة)
5. احفظ الإعدادات

### 2. استخدام نقطة البيع
1. اذهب إلى **نقطة البيع** (`/pos`)
2. أضف منتجات للسلة
3. **الضريبة ستُحسب تلقائياً** حسب المعدل المحدد
4. عند إضافة خصم، ستُعاد حساب الضريبة على المبلغ بعد الخصم
5. أتمم البيع كالمعتاد

### 3. إلغاء تفعيل الضريبة
1. اذهب إلى **الإعدادات** → **إعدادات الضرائب**
2. ألغ تفعيل **"تفعيل الضرائب"**
3. احفظ الإعدادات
4. حقل الضريبة سيختفي من نقطة البيع

## التحسينات التقنية

### 1. API جديد
- `GET /pos/tax-settings` - للحصول على إعدادات الضريبة
- يُستخدم في JavaScript لتحميل الإعدادات

### 2. JavaScript محسن
- دوال جديدة لحساب الضريبة التلقائي
- تحديث الواجهة حسب حالة التفعيل
- حساب ذكي يأخذ الخصم في الاعتبار

### 3. تكامل مع النظام الحالي
- يعمل مع جميع طرق الدفع
- متوافق مع نظام العملاء والمديونيات
- يحفظ قيمة الضريبة في الفاتورة

## أمثلة الحساب

### مثال 1: بدون خصم
- المبلغ الفرعي: 1000 ر.ي
- معدل الضريبة: 15%
- الضريبة: 1000 × 15% = 150 ر.ي
- **الإجمالي: 1150 ر.ي**

### مثال 2: مع خصم
- المبلغ الفرعي: 1000 ر.ي
- الخصم: 100 ر.ي
- المبلغ الخاضع للضريبة: 900 ر.ي
- الضريبة: 900 × 15% = 135 ر.ي
- **الإجمالي: 1035 ر.ي**

## الملفات المُحدثة

### 1. Backend
- `app/Http/Controllers/POSController.php` - إضافة API للضريبة
- `routes/web.php` - إضافة route جديد

### 2. Frontend
- `resources/views/pos/index.blade.php` - تحسين JavaScript
- إضافة دوال حساب الضريبة التلقائي
- تحديث واجهة المستخدم

### 3. ملفات الاختبار
- `test_tax_system.php` - اختبار شامل للنظام

## المزايا

### ✅ **للمستخدم**
- سهولة الاستخدام - لا حاجة لحساب الضريبة يدوياً
- دقة في الحسابات
- مرونة في التحكم (تفعيل/إلغاء)

### ✅ **للنظام**
- حسابات دقيقة ومتسقة
- تقليل الأخطاء البشرية
- توافق مع المعايير المحاسبية

### ✅ **للأعمال**
- امتثال للقوانين الضريبية
- تقارير دقيقة
- سهولة المراجعة والتدقيق

## استكشاف الأخطاء

### إذا لم تظهر الضريبة تلقائياً:
1. تأكد من تفعيل الضريبة في الإعدادات
2. تأكد من تحديد معدل ضريبة أكبر من 0
3. أعد تحميل صفحة نقطة البيع

### إذا كان الحساب خاطئ:
1. تحقق من معدل الضريبة في الإعدادات
2. تأكد من أن الخصم محسوب بشكل صحيح
3. راجع console المتصفح للأخطاء

### إذا لم يختف حقل الضريبة:
1. تأكد من إلغاء تفعيل الضريبة
2. امسح cache المتصفح
3. أعد تحميل الصفحة

## الاختبار

لاختبار النظام:
```bash
# تشغيل اختبار شامل
php test_tax_system.php
```

أو اختبار يدوي:
1. فعل الضريبة بمعدل 15%
2. اذهب لنقطة البيع
3. أضف منتجات بقيمة 1000 ر.ي
4. يجب أن تظهر الضريبة 150 ر.ي تلقائياً
5. أضف خصم 100 ر.ي
6. يجب أن تصبح الضريبة 135 ر.ي

---

**ملاحظة**: هذا النظام يحسب الضريبة على المبلغ بعد الخصم، وهو المعيار المحاسبي الصحيح.
