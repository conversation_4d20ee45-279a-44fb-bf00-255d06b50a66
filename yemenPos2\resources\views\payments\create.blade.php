@extends('layouts.app')

@section('title', 'إضافة دفعة جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('payments.index') }}">المدفوعات</a></li>
                            <li class="breadcrumb-item active">إضافة دفعة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('payments.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                بيانات الدفعة الجديدة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('payments.store') }}" method="POST">
                                @csrf

                                <!-- نوع المعاملة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">نوع المعاملة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('payable_type') is-invalid @enderror" 
                                                name="payable_type" id="payable_type" required>
                                            <option value="">اختر نوع المعاملة</option>
                                            <option value="App\Models\Sale" {{ old('payable_type') == 'App\Models\Sale' ? 'selected' : '' }}>
                                                مبيعات (دفع من عميل)
                                            </option>
                                            <option value="App\Models\Purchase" {{ old('payable_type') == 'App\Models\Purchase' ? 'selected' : '' }}>
                                                مشتريات (دفع لمورد)
                                            </option>
                                        </select>
                                        @error('payable_type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الفاتورة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('payable_id') is-invalid @enderror" 
                                                name="payable_id" id="payable_id" required>
                                            <option value="">اختر الفاتورة</option>
                                        </select>
                                        @error('payable_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- العميل/المورد -->
                                <div class="row mb-3">
                                    <div class="col-md-6" id="customer_section" style="display: none;">
                                        <label class="form-label">العميل</label>
                                        <select class="form-select" name="customer_id" id="customer_id">
                                            <option value="">اختر العميل</option>
                                            @foreach($customers as $customer)
                                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6" id="supplier_section" style="display: none;">
                                        <label class="form-label">المورد</label>
                                        <select class="form-select" name="supplier_id" id="supplier_id">
                                            <option value="">اختر المورد</option>
                                            @foreach($suppliers as $supplier)
                                                <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <!-- معلومات الدفعة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                                   name="amount" value="{{ old('amount') }}" 
                                                   step="0.01" min="0" required>
                                            <span class="input-group-text">ر.ي</span>
                                        </div>
                                        @error('amount')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">نوع الدفعة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('payment_type') is-invalid @enderror" 
                                                name="payment_type" required>
                                            <option value="دفعة" {{ old('payment_type') == 'دفعة' ? 'selected' : '' }}>دفعة</option>
                                            <option value="استرداد" {{ old('payment_type') == 'استرداد' ? 'selected' : '' }}>استرداد</option>
                                        </select>
                                        @error('payment_type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- طريقة الدفع والتاريخ -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                        <select class="form-select @error('payment_method') is-invalid @enderror" 
                                                name="payment_method" id="payment_method" required>
                                            <option value="نقدي" {{ old('payment_method') == 'نقدي' ? 'selected' : '' }}>نقدي</option>
                                            <option value="تحويل بنكي" {{ old('payment_method') == 'تحويل بنكي' ? 'selected' : '' }}>تحويل بنكي</option>
                                            <option value="شيك" {{ old('payment_method') == 'شيك' ? 'selected' : '' }}>شيك</option>
                                            <option value="بطاقة ائتمان" {{ old('payment_method') == 'بطاقة ائتمان' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                        </select>
                                        @error('payment_method')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الدفعة <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control @error('payment_date') is-invalid @enderror" 
                                               name="payment_date" value="{{ old('payment_date', date('Y-m-d')) }}" required>
                                        @error('payment_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- تفاصيل إضافية حسب طريقة الدفع -->
                                <div id="bank_details" style="display: none;">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">رقم المرجع</label>
                                            <input type="text" class="form-control" name="reference_number" 
                                                   value="{{ old('reference_number') }}" placeholder="رقم التحويل">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">اسم البنك</label>
                                            <input type="text" class="form-control" name="bank_name" 
                                                   value="{{ old('bank_name') }}" placeholder="البنك الأهلي">
                                        </div>
                                    </div>
                                </div>

                                <div id="check_details" style="display: none;">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">رقم الشيك</label>
                                            <input type="text" class="form-control" name="check_number" 
                                                   value="{{ old('check_number') }}" placeholder="123456">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">تاريخ الشيك</label>
                                            <input type="date" class="form-control" name="check_date" 
                                                   value="{{ old('check_date') }}">
                                        </div>
                                    </div>
                                </div>

                                <!-- الحالة والملاحظات -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">حالة الدفعة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" 
                                                name="status" required>
                                            <option value="مؤكد" {{ old('status') == 'مؤكد' ? 'selected' : '' }}>مؤكد</option>
                                            <option value="معلق" {{ old('status') == 'معلق' ? 'selected' : '' }}>معلق</option>
                                            <option value="ملغي" {{ old('status') == 'ملغي' ? 'selected' : '' }}>ملغي</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              name="notes" rows="3" placeholder="ملاحظات إضافية...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- أزرار الحفظ -->
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('payments.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ الدفعة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const payableType = document.getElementById('payable_type');
    const payableId = document.getElementById('payable_id');
    const customerSection = document.getElementById('customer_section');
    const supplierSection = document.getElementById('supplier_section');
    const paymentMethod = document.getElementById('payment_method');
    const bankDetails = document.getElementById('bank_details');
    const checkDetails = document.getElementById('check_details');

    // تغيير نوع المعاملة
    payableType.addEventListener('change', function() {
        const type = this.value;
        payableId.innerHTML = '<option value="">اختر الفاتورة</option>';
        
        if (type === 'App\\Models\\Sale') {
            customerSection.style.display = 'block';
            supplierSection.style.display = 'none';
            loadSales();
        } else if (type === 'App\\Models\\Purchase') {
            customerSection.style.display = 'none';
            supplierSection.style.display = 'block';
            loadPurchases();
        } else {
            customerSection.style.display = 'none';
            supplierSection.style.display = 'none';
        }
    });

    // تغيير طريقة الدفع
    paymentMethod.addEventListener('change', function() {
        const method = this.value;
        
        bankDetails.style.display = method === 'تحويل بنكي' ? 'block' : 'none';
        checkDetails.style.display = method === 'شيك' ? 'block' : 'none';
    });

    // تحميل المبيعات
    function loadSales() {
        fetch('/api/sales/unpaid')
            .then(response => response.json())
            .then(data => {
                data.forEach(sale => {
                    const option = document.createElement('option');
                    option.value = sale.id;
                    option.textContent = `${sale.invoice_number} - ${sale.customer?.name || 'عميل عادي'} - ${sale.remaining_amount} ر.ي`;
                    payableId.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading sales:', error));
    }

    // تحميل المشتريات
    function loadPurchases() {
        fetch('/api/purchases/unpaid')
            .then(response => response.json())
            .then(data => {
                data.forEach(purchase => {
                    const option = document.createElement('option');
                    option.value = purchase.id;
                    option.textContent = `${purchase.purchase_number} - ${purchase.supplier?.name || 'مورد'} - ${purchase.remaining_amount} ر.ي`;
                    payableId.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading purchases:', error));
    }
});
</script>
@endsection
