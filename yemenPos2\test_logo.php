<?php

/**
 * ملف اختبار لتجربة رفع الشعار
 */

require_once __DIR__ . '/vendor/autoload.php';

// تحميل Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Setting;

echo "اختبار نظام رفع الشعار...\n\n";

// 1. التحقق من إعداد الشعار
echo "1. التحقق من إعداد الشعار في قاعدة البيانات...\n";
$logoSetting = Setting::where('key', 'company_logo')->first();

if ($logoSetting) {
    echo "✅ إعداد الشعار موجود\n";
    echo "   النوع: " . $logoSetting->type . "\n";
    echo "   القيمة: " . ($logoSetting->value ?: 'فارغ') . "\n";
    
    if ($logoSetting->type !== 'file') {
        echo "❌ نوع الحقل خاطئ! يجب أن يكون 'file'\n";
    } else {
        echo "✅ نوع الحقل صحيح\n";
    }
} else {
    echo "❌ إعداد الشعار غير موجود\n";
}

echo "\n";

// 2. التحقق من المجلدات
echo "2. التحقق من المجلدات...\n";

$directories = [
    'storage/app/public' => storage_path('app/public'),
    'storage/app/public/settings' => storage_path('app/public/settings'),
    'public/storage' => public_path('storage')
];

foreach ($directories as $name => $path) {
    if (is_dir($path)) {
        echo "✅ $name موجود\n";
        
        // التحقق من الصلاحيات
        if (is_writable($path)) {
            echo "   ✅ قابل للكتابة\n";
        } else {
            echo "   ❌ غير قابل للكتابة\n";
        }
    } else {
        echo "❌ $name غير موجود\n";
    }
}

echo "\n";

// 3. التحقق من الـ symbolic link
echo "3. التحقق من الـ symbolic link...\n";
$publicStorage = public_path('storage');
$storagePublic = storage_path('app/public');

if (is_link($publicStorage)) {
    echo "✅ symbolic link موجود\n";
    $target = readlink($publicStorage);
    echo "   يشير إلى: $target\n";
    
    if (realpath($target) === realpath($storagePublic)) {
        echo "   ✅ يشير للمكان الصحيح\n";
    } else {
        echo "   ❌ يشير لمكان خاطئ\n";
    }
} else {
    echo "❌ symbolic link غير موجود أو لا يعمل\n";
}

echo "\n";

// 4. اختبار الوصول للملفات
echo "4. اختبار الوصول للملفات...\n";
$settingsDir = storage_path('app/public/settings');
if (is_dir($settingsDir)) {
    $files = glob($settingsDir . '/*');
    if (count($files) > 0) {
        echo "✅ يوجد " . count($files) . " ملف في مجلد settings\n";
        foreach ($files as $file) {
            $filename = basename($file);
            $publicUrl = asset('storage/settings/' . $filename);
            echo "   📁 $filename\n";
            echo "   🔗 $publicUrl\n";
        }
    } else {
        echo "ℹ️ لا توجد ملفات في مجلد settings\n";
    }
} else {
    echo "❌ مجلد settings غير موجود\n";
}

echo "\n";

// 5. اختبار إنشاء ملف تجريبي
echo "5. اختبار إنشاء ملف تجريبي...\n";
$testFile = storage_path('app/public/settings/test.txt');
try {
    file_put_contents($testFile, 'اختبار');
    echo "✅ تم إنشاء ملف تجريبي\n";
    
    // اختبار الوصول عبر الرابط العام
    $publicTestFile = public_path('storage/settings/test.txt');
    if (file_exists($publicTestFile)) {
        echo "✅ يمكن الوصول للملف عبر الرابط العام\n";
    } else {
        echo "❌ لا يمكن الوصول للملف عبر الرابط العام\n";
    }
    
    // حذف الملف التجريبي
    unlink($testFile);
    echo "✅ تم حذف الملف التجريبي\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الملف: " . $e->getMessage() . "\n";
}

echo "\n🎉 انتهى الاختبار!\n";

if ($logoSetting && $logoSetting->type === 'file' && is_dir(public_path('storage'))) {
    echo "\n✅ النظام جاهز لرفع الشعار!\n";
    echo "يمكنك الآن الذهاب إلى الإعدادات ورفع شعار الشركة.\n";
} else {
    echo "\n❌ يوجد مشاكل تحتاج لحل!\n";
    echo "يرجى تشغيل fix_logo.bat أولاً.\n";
}
