<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Sale;
use App\Models\SalePayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DebtController extends Controller
{
    /**
     * صفحة إدارة المديونيات
     */
    public function index(Request $request)
    {
        // جلب العملاء الذين لديهم مديونيات
        $customers = Customer::where('is_active', true)
            ->whereHas('sales', function($query) {
                $query->where('is_posted', true)
                      ->where('remaining_amount', '>', 0)
                      ->where('status', '!=', 'مسترجعة');
            })
            ->withCount(['sales as total_debts_count' => function($query) {
                $query->where('is_posted', true)
                      ->where('remaining_amount', '>', 0)
                      ->where('status', '!=', 'مسترجعة');
            }])
            ->orderBy('name')
            ->get();

        // إضافة إجمالي المديونيات لكل عميل
        foreach ($customers as $customer) {
            $customer->total_remaining = $customer->sales()
                ->where('is_posted', true)
                ->where('remaining_amount', '>', 0)
                ->where('status', '!=', 'مسترجعة')
                ->sum('remaining_amount');
        }

        $selectedCustomer = null;
        $customerDebts = collect();
        $totalCustomerDebts = 0;

        if ($request->filled('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);
            if ($selectedCustomer) {
                $customerDebts = Sale::with(['user'])
                    ->where('customer_id', $selectedCustomer->id)
                    ->where('is_posted', true)
                    ->where('remaining_amount', '>', 0)
                    ->where('status', '!=', 'مسترجعة')
                    ->latest('sale_date')->latest('sale_time')
                    ->get();

                $totalCustomerDebts = $customerDebts->sum('remaining_amount');
            }
        }

        return view('debts.index', compact('customers', 'selectedCustomer', 'customerDebts', 'totalCustomerDebts'));
    }

    /**
     * دفع مديونيات عميل
     */
    public function payCustomerDebts(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'payment_amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:نقدي,بطاقة ائتمان,تحويل بنكي',
            'notes' => 'nullable|string',
            'distribution_method' => 'required|in:oldest_first,newest_first,proportional,manual'
        ]);

        try {
            DB::beginTransaction();

            $customer = Customer::findOrFail($request->customer_id);
            $paymentAmount = $request->payment_amount;
            $remainingPayment = $paymentAmount;

            // جلب مديونيات العميل
            $debts = Sale::where('customer_id', $customer->id)
                ->where('is_posted', true)
                ->where('remaining_amount', '>', 0)
                ->where('status', '!=', 'مسترجعة');

            // ترتيب حسب طريقة التوزيع
            switch ($request->distribution_method) {
                case 'oldest_first':
                    $debts = $debts->orderBy('sale_date')->get();
                    break;
                case 'newest_first':
                    $debts = $debts->orderByDesc('sale_date')->get();
                    break;
                case 'proportional':
                case 'manual':
                    $debts = $debts->orderBy('sale_date')->get();
                    break;
            }

            $totalDebts = $debts->sum('remaining_amount');

            if ($paymentAmount > $totalDebts) {
                return response()->json([
                    'success' => false,
                    'message' => 'المبلغ المدفوع أكبر من إجمالي المديونيات'
                ]);
            }

            $paidInvoices = [];

            if ($request->distribution_method === 'proportional') {
                // التوزيع النسبي
                foreach ($debts as $debt) {
                    if ($remainingPayment <= 0) break;

                    $proportion = $debt->remaining_amount / $totalDebts;
                    $amountToPay = min($paymentAmount * $proportion, $debt->remaining_amount, $remainingPayment);

                    if ($amountToPay > 0.01) { // تجنب المبالغ الصغيرة جداً
                        $this->processPayment($debt, $amountToPay, $request->payment_method, $request->notes);
                        $remainingPayment -= $amountToPay;
                        $paidInvoices[] = [
                            'invoice_number' => $debt->invoice_number,
                            'amount' => $amountToPay,
                            'remaining' => max(0, $debt->remaining_amount - $amountToPay)
                        ];
                    }
                }
            } elseif ($request->distribution_method === 'manual') {
                // التوزيع اليدوي
                if ($request->has('manual_amounts')) {
                    foreach ($request->manual_amounts as $saleId => $amount) {
                        if ($amount > 0 && $remainingPayment > 0) {
                            $debt = $debts->where('id', $saleId)->first();
                            if ($debt) {
                                $amountToPay = min($amount, $debt->remaining_amount, $remainingPayment);
                                $this->processPayment($debt, $amountToPay, $request->payment_method, $request->notes);
                                $remainingPayment -= $amountToPay;
                                $paidInvoices[] = [
                                    'invoice_number' => $debt->invoice_number,
                                    'amount' => $amountToPay,
                                    'remaining' => max(0, $debt->remaining_amount - $amountToPay)
                                ];
                            }
                        }
                    }
                }
            } else {
                // التوزيع التسلسلي (الأقدم أولاً أو الأحدث أولاً)
                foreach ($debts as $debt) {
                    if ($remainingPayment <= 0) break;

                    $amountToPay = min($remainingPayment, $debt->remaining_amount);
                    $this->processPayment($debt, $amountToPay, $request->payment_method, $request->notes);
                    $remainingPayment -= $amountToPay;

                    $paidInvoices[] = [
                        'invoice_number' => $debt->invoice_number,
                        'amount' => $amountToPay,
                        'remaining' => max(0, $debt->remaining_amount - $amountToPay)
                    ];
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الدفع بنجاح',
                'paid_invoices' => $paidInvoices,
                'total_paid' => $paymentAmount - $remainingPayment,
                'remaining_payment' => $remainingPayment
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تسجيل الدفع: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * معالجة دفعة واحدة
     */
    private function processPayment($sale, $amount, $paymentMethod, $notes)
    {
        // تحديث المبالغ
        $sale->paid_amount = ($sale->paid_amount ?? 0) + $amount;
        $sale->remaining_amount = $sale->total_amount - $sale->paid_amount;

        // تحديث الحالة وطريقة الدفع
        if ($sale->remaining_amount <= 0) {
            $sale->status = 'مكتملة';
            $sale->remaining_amount = 0;
            $sale->payment_method = $paymentMethod; // تحديث طريقة الدفع عند الإكمال
        } elseif ($sale->paid_amount > 0) {
            $sale->status = 'دفع جزئي';
            // في الدفع الجزئي، نحتفظ بطريقة الدفع الأصلية
        } else {
            $sale->status = 'غير مدفوعة';
        }

        $sale->save();

        // حفظ سجل الدفعة
        SalePayment::create([
            'sale_id' => $sale->id,
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'notes' => $notes,
            'user_id' => auth()->id(),
        ]);
    }

    /**
     * جلب مديونيات عميل (AJAX)
     */
    public function getCustomerDebts(Request $request)
    {
        $customer = Customer::find($request->customer_id);
        if (!$customer) {
            return response()->json(['success' => false, 'message' => 'العميل غير موجود']);
        }

        $debts = Sale::with(['user'])
            ->where('customer_id', $customer->id)
            ->where('is_posted', true)
            ->where('remaining_amount', '>', 0)
            ->where('status', '!=', 'مسترجعة')
            ->latest('sale_date')->latest('sale_time')
            ->get();

        $totalDebts = $debts->sum('remaining_amount');

        return response()->json([
            'success' => true,
            'customer' => $customer,
            'debts' => $debts,
            'total_debts' => $totalDebts
        ]);
    }
}
