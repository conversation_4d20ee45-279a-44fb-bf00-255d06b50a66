@echo off
chcp 65001 >nul
echo حل مشكلة رفع الشعار...
echo.

echo 1. تشغيل migration...
php artisan migrate --force
if %errorlevel% neq 0 (
    echo خطأ في تشغيل migration
    pause
    exit /b 1
)
echo ✅ تم تشغيل migration بنجاح
echo.

echo 2. إعادة إنشاء symbolic link...
if exist "public\storage" (
    rmdir /s /q "public\storage" 2>nul
)
php artisan storage:link
echo ✅ تم إنشاء symbolic link بنجاح
echo.

echo 3. مسح الكاش...
php artisan cache:clear
php artisan config:clear
php artisan view:clear
echo ✅ تم مسح الكاش بنجاح
echo.

echo 🎉 تم الانتهاء! يمكنك الآن:
echo 1. الذهاب إلى الإعدادات
echo 2. اختيار شعار الشركة
echo 3. رفع صورة الشعار
echo 4. حفظ الإعدادات
echo 5. سيظهر الشعار في جميع الفواتير
echo.
pause
