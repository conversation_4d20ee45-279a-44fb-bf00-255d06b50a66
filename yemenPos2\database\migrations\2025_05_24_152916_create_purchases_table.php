<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchases', function (Blueprint $table) {
            $table->id();
            $table->string('purchase_number')->unique(); // رقم أمر الشراء
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade'); // المورد
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // المستخدم
            $table->date('purchase_date'); // تاريخ الشراء
            $table->date('expected_delivery_date')->nullable(); // تاريخ التسليم المتوقع
            $table->decimal('subtotal', 10, 2); // المجموع الفرعي
            $table->decimal('tax_amount', 10, 2)->default(0); // مبلغ الضريبة
            $table->decimal('discount_amount', 10, 2)->default(0); // مبلغ الخصم
            $table->decimal('total_amount', 10, 2); // المجموع الكلي
            $table->decimal('paid_amount', 10, 2)->default(0); // المبلغ المدفوع
            $table->decimal('remaining_amount', 10, 2)->default(0); // المبلغ المتبقي
            $table->enum('payment_method', ['نقدي', 'آجل', 'تحويل بنكي', 'مختلط'])->default('نقدي'); // طريقة الدفع
            $table->enum('status', ['معلق', 'مؤكد', 'مستلم', 'ملغي'])->default('معلق'); // حالة الطلب
            $table->string('currency', 3)->default('YER'); // العملة
            $table->decimal('exchange_rate', 8, 4)->default(1); // سعر الصرف
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchases');
    }
};
