<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    protected $fillable = [
        'name_ar',
        'name_en',
        'barcode',
        'sku',
        'description_ar',
        'description_en',
        'category_id',
        'default_supplier_id',
        'purchase_price',
        'selling_price',
        'wholesale_price',
        'stock_quantity',
        'min_stock_level',
        'unit',
        'image',
        'is_active',
        'track_stock',
        'expiry_date',
        'expiry_alert_days',
        'expiry_status'
    ];

    protected $casts = [
        'purchase_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'is_active' => 'boolean',
        'track_stock' => 'boolean',
        'expiry_date' => 'date',
    ];

    /**
     * العلاقة مع التصنيف
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * العلاقة مع عناصر المبيعات
     */
    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * العلاقة مع عناصر المشتريات
     */
    public function purchaseItems(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * العلاقة مع المورد الافتراضي
     */
    public function defaultSupplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'default_supplier_id');
    }

    /**
     * العلاقة مع دفعات المنتج
     */
    public function batches(): HasMany
    {
        return $this->hasMany(ProductBatch::class);
    }

    /**
     * الحصول على الدفعات النشطة
     */
    public function activeBatches(): HasMany
    {
        return $this->hasMany(ProductBatch::class)->where('status', 'active')->where('remaining_quantity', '>', 0);
    }

    /**
     * الحصول على الاسم حسب اللغة الحالية
     */
    public function getNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    /**
     * الحصول على الوصف حسب اللغة الحالية
     */
    public function getDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : ($this->description_en ?? $this->description_ar);
    }

    /**
     * التحقق من انخفاض المخزون
     */
    public function isLowStock(): bool
    {
        return $this->track_stock && $this->stock_quantity <= $this->min_stock_level;
    }

    /**
     * التحقق من نفاد المخزون
     */
    public function isOutOfStock(): bool
    {
        return $this->track_stock && $this->stock_quantity <= 0;
    }

    /**
     * حساب الربح
     */
    public function getProfitAttribute(): float
    {
        return $this->selling_price - $this->purchase_price;
    }

    /**
     * حساب نسبة الربح
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->purchase_price == 0) return 0;
        return (($this->selling_price - $this->purchase_price) / $this->purchase_price) * 100;
    }

    /**
     * التحقق من انتهاء صلاحية المنتج
     */
    public function isExpired(): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return now()->gt($this->expiry_date);
    }

    /**
     * التحقق من قرب انتهاء صلاحية المنتج
     */
    public function isNearExpiry(): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        $alertDays = $this->expiry_alert_days ?? 30;
        return now()->addDays($alertDays)->gte($this->expiry_date) && !$this->isExpired();
    }

    /**
     * تحديث حالة انتهاء الصلاحية
     */
    public function updateExpiryStatus(): void
    {
        if (!$this->expiry_date) {
            $this->expiry_status = 'no_expiry';
        } elseif ($this->isExpired()) {
            $this->expiry_status = 'expired';
        } elseif ($this->isNearExpiry()) {
            $this->expiry_status = 'near_expiry';
        } else {
            $this->expiry_status = 'valid';
        }

        $this->save();
    }

    /**
     * الحصول على أقرب دفعة لانتهاء الصلاحية
     */
    public function getEarliestExpiryBatch()
    {
        return $this->activeBatches()
                   ->whereNotNull('expiry_date')
                   ->orderBy('expiry_date', 'asc')
                   ->first();
    }

    /**
     * الحصول على الدفعات منتهية الصلاحية
     */
    public function getExpiredBatches()
    {
        return $this->batches()
                   ->where('status', 'expired')
                   ->orWhere(function($query) {
                       $query->whereNotNull('expiry_date')
                             ->where('expiry_date', '<', now());
                   })
                   ->get();
    }

    /**
     * الحصول على الدفعات قريبة الانتهاء
     */
    public function getNearExpiryBatches(?int $days = null)
    {
        $alertDays = $days ?? $this->expiry_alert_days ?? 30;

        return $this->activeBatches()
                   ->whereNotNull('expiry_date')
                   ->where('expiry_date', '>', now())
                   ->where('expiry_date', '<=', now()->addDays($alertDays))
                   ->orderBy('expiry_date', 'asc')
                   ->get();
    }
}
