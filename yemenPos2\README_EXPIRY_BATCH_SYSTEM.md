# نظام تتبع انتهاء الصلاحية والدفعات

## ✅ **الميزات المكتملة**

### **🏪 1. مورد عام:**
- **✅ تم إنشاء مورد عام** (ID: 11)
- **الاسم:** مورد عام
- **الشركة:** موردين متنوعين
- **الاستخدام:** للمنتجات التي لا تحتاج مورد محدد

### **📅 2. نظام تاريخ انتهاء الصلاحية:**

#### **🔧 الحقول المضافة للمنتجات:**
```sql
- default_supplier_id: المورد الافتراضي (اختياري)
- expiry_date: تاريخ انتهاء الصلاحية (اختياري)
- expiry_alert_days: عدد أيام التنبيه (افتراضي: 30 يوم)
- expiry_status: حالة الصلاحية (valid, expired, near_expiry, no_expiry)
```

#### **🎯 حالات الصلاحية:**
- **`valid`**: صالح للاستخدام
- **`expired`**: منتهي الصلاحية
- **`near_expiry`**: قريب من انتهاء الصلاحية
- **`no_expiry`**: لا يوجد تاريخ انتهاء

### **📦 3. نظام الدفعات (Product Batches):**

#### **🗃️ جدول product_batches:**
```sql
- product_id: ربط بالمنتج
- supplier_id: ربط بالمورد
- purchase_id: ربط بالمشترى (اختياري)
- batch_number: رقم الدفعة (فريد)
- original_quantity: الكمية الأصلية
- remaining_quantity: الكمية المتبقية
- expiry_date: تاريخ انتهاء الصلاحية لهذه الدفعة
- production_date: تاريخ الإنتاج
- cost_price: سعر التكلفة لهذه الدفعة
- status: حالة الدفعة (active, expired, depleted, recalled)
- notes: ملاحظات
```

#### **🎯 حالات الدفعة:**
- **`active`**: نشطة ومتاحة للبيع
- **`expired`**: منتهية الصلاحية
- **`depleted`**: نفدت الكمية
- **`recalled`**: مسحوبة من السوق

## 🔧 **الوظائف المطورة**

### **📊 نموذج Product محسن:**

#### **🔗 العلاقات الجديدة:**
```php
// العلاقة مع المورد الافتراضي
public function defaultSupplier(): BelongsTo

// العلاقة مع دفعات المنتج
public function batches(): HasMany

// الحصول على الدفعات النشطة
public function activeBatches(): HasMany
```

#### **⚡ الدوال الجديدة:**
```php
// التحقق من انتهاء الصلاحية
public function isExpired(): bool

// التحقق من قرب انتهاء الصلاحية
public function isNearExpiry(): bool

// تحديث حالة انتهاء الصلاحية
public function updateExpiryStatus(): void

// الحصول على أقرب دفعة لانتهاء الصلاحية
public function getEarliestExpiryBatch()

// الحصول على الدفعات منتهية الصلاحية
public function getExpiredBatches()

// الحصول على الدفعات قريبة الانتهاء
public function getNearExpiryBatches(?int $days = null)
```

### **📦 نموذج ProductBatch:**

#### **⚡ الدوال الرئيسية:**
```php
// التحقق من انتهاء الصلاحية
public function isExpired(): bool

// التحقق من قرب انتهاء الصلاحية
public function isNearExpiry(int $days = 30): bool

// الحصول على الأيام المتبقية لانتهاء الصلاحية
public function getDaysToExpiry(): ?int

// تحديث حالة الدفعة
public function updateExpiryStatus(): void

// تقليل الكمية المتبقية
public function reduceQuantity(float $quantity): bool

// إنشاء رقم دفعة تلقائي
public static function generateBatchNumber(int $productId): string
```

## 🎯 **حل مشكلة الدفعات المتعددة**

### **🔄 آلية FIFO (First In, First Out):**

#### **عند البيع:**
1. **البحث عن أقرب دفعة لانتهاء الصلاحية**
2. **بيع من الدفعة الأقرب للانتهاء أولاً**
3. **تحديث الكمية المتبقية في الدفعة**
4. **الانتقال للدفعة التالية إذا نفدت الأولى**

#### **مثال عملي:**
```
منتج: حليب
- دفعة 1: تنتهي 2025-06-15 (كمية: 50)
- دفعة 2: تنتهي 2025-06-30 (كمية: 30)
- دفعة 3: تنتهي 2025-07-10 (كمية: 20)

عند بيع 60 قطعة:
1. بيع 50 من الدفعة 1 (تصبح فارغة)
2. بيع 10 من الدفعة 2 (تصبح 20)
3. الدفعة 3 تبقى كما هي (20)
```

### **📊 تتبع المخزون:**
```php
// الحصول على إجمالي المخزون المتاح
$totalStock = $product->activeBatches()->sum('remaining_quantity');

// الحصول على المخزون حسب تاريخ الانتهاء
$stockByExpiry = $product->activeBatches()
    ->selectRaw('expiry_date, SUM(remaining_quantity) as total')
    ->groupBy('expiry_date')
    ->orderBy('expiry_date')
    ->get();
```

## 🚨 **نظام التنبيهات**

### **📅 تنبيهات انتهاء الصلاحية:**

#### **للمنتجات:**
```php
// المنتجات منتهية الصلاحية
$expiredProducts = Product::where('expiry_status', 'expired')->get();

// المنتجات قريبة الانتهاء
$nearExpiryProducts = Product::where('expiry_status', 'near_expiry')->get();
```

#### **للدفعات:**
```php
// الدفعات منتهية الصلاحية
$expiredBatches = ProductBatch::where('status', 'expired')
    ->orWhere('expiry_date', '<', now())
    ->get();

// الدفعات قريبة الانتهاء (30 يوم)
$nearExpiryBatches = ProductBatch::where('status', 'active')
    ->whereNotNull('expiry_date')
    ->where('expiry_date', '>', now())
    ->where('expiry_date', '<=', now()->addDays(30))
    ->get();
```

## 🛒 **تطبيق النظام في المشتريات**

### **عند إضافة مشترى جديد:**
1. **اختيار المنتج**
2. **تحديد المورد** (أو استخدام المورد الافتراضي)
3. **إدخال الكمية وسعر التكلفة**
4. **إدخال تاريخ انتهاء الصلاحية** (اختياري)
5. **إنشاء دفعة جديدة** تلقائياً

### **مثال كود إنشاء دفعة:**
```php
// عند إضافة منتج في المشترى
$batch = ProductBatch::create([
    'product_id' => $productId,
    'supplier_id' => $supplierId,
    'purchase_id' => $purchaseId,
    'batch_number' => ProductBatch::generateBatchNumber($productId),
    'original_quantity' => $quantity,
    'remaining_quantity' => $quantity,
    'expiry_date' => $expiryDate, // من المستخدم
    'production_date' => $productionDate, // اختياري
    'cost_price' => $costPrice,
    'status' => 'active'
]);
```

## 🎯 **تطبيق النظام في المبيعات**

### **عند البيع:**
1. **البحث عن الدفعات النشطة للمنتج**
2. **ترتيبها حسب تاريخ انتهاء الصلاحية** (FIFO)
3. **خصم الكمية من أقرب دفعة للانتهاء**
4. **تحديث حالة الدفعة** إذا نفدت

### **مثال كود البيع:**
```php
public function sellProduct($productId, $quantityToSell)
{
    $product = Product::find($productId);
    $batches = $product->activeBatches()
        ->whereNotNull('expiry_date')
        ->orderBy('expiry_date', 'asc')
        ->get();
    
    $remainingToSell = $quantityToSell;
    
    foreach ($batches as $batch) {
        if ($remainingToSell <= 0) break;
        
        $quantityFromBatch = min($remainingToSell, $batch->remaining_quantity);
        
        if ($batch->reduceQuantity($quantityFromBatch)) {
            $remainingToSell -= $quantityFromBatch;
        }
    }
    
    return $remainingToSell == 0; // true إذا تم البيع بالكامل
}
```

## 📊 **التقارير والإحصائيات**

### **تقرير انتهاء الصلاحية:**
```php
// المنتجات منتهية الصلاحية
$expiredReport = [
    'products' => Product::where('expiry_status', 'expired')->count(),
    'batches' => ProductBatch::where('status', 'expired')->count(),
    'value' => ProductBatch::where('status', 'expired')
        ->sum(DB::raw('remaining_quantity * cost_price'))
];

// المنتجات قريبة الانتهاء
$nearExpiryReport = [
    'products' => Product::where('expiry_status', 'near_expiry')->count(),
    'batches' => ProductBatch::where('status', 'active')
        ->where('expiry_date', '<=', now()->addDays(30))->count()
];
```

## 🎉 **الفوائد المحققة**

### **✅ للمخزون:**
- **تتبع دقيق** لتواريخ انتهاء الصلاحية
- **منع بيع المنتجات منتهية الصلاحية**
- **تقليل الفاقد** من المنتجات
- **تحسين دوران المخزون**

### **✅ للمبيعات:**
- **ضمان جودة المنتجات** المباعة
- **بيع المنتجات الأقرب للانتهاء أولاً**
- **تجنب المشاكل القانونية**
- **تحسين رضا العملاء**

### **✅ للإدارة:**
- **تقارير شاملة** عن حالة المخزون
- **تنبيهات مبكرة** لانتهاء الصلاحية
- **تتبع الخسائر** من المنتجات منتهية الصلاحية
- **اتخاذ قرارات مدروسة** للمشتريات

---

**الآن النظام يدعم تتبع انتهاء الصلاحية والدفعات بشكل كامل ومتقدم!** 📦⏰✨
