@extends('layouts.app')

@section('title', 'إدارة المبيعات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-shopping-cart me-2"></i>
                        إدارة المبيعات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المبيعات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('pos.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>فاتورة جديدة
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('sales.index') }}">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search"
                                       value="{{ request('search') }}" placeholder="رقم الفاتورة أو اسم العميل...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from"
                                       value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to"
                                       value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="customer_id">
                                    <option value="">جميع العملاء</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                                {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" name="payment_method">
                                    <option value="">جميع الطرق</option>
                                    <option value="نقدي" {{ request('payment_method') == 'نقدي' ? 'selected' : '' }}>نقدي</option>
                                    <option value="بطاقة ائتمان" {{ request('payment_method') == 'بطاقة ائتمان' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                    <option value="تحويل بنكي" {{ request('payment_method') == 'تحويل بنكي' ? 'selected' : '' }}>تحويل بنكي</option>
                                    <option value="آجل" {{ request('payment_method') == 'آجل' ? 'selected' : '' }}>آجل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">حالة الترحيل</label>
                                <select class="form-select" name="posting_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="مرحلة" {{ request('posting_status') == 'مرحلة' ? 'selected' : '' }}>مرحلة</option>
                                    <option value="غير_مرحلة" {{ request('posting_status') == 'غير_مرحلة' ? 'selected' : '' }}>غير مرحلة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row g-3 mt-2">
                            <div class="col-md-2">
                                <label class="form-label">حالة الدفع</label>
                                <select class="form-select" name="payment_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="مدفوعة" {{ request('payment_status') == 'مدفوعة' ? 'selected' : '' }}>مدفوعة</option>
                                    <option value="غير_مدفوعة" {{ request('payment_status') == 'غير_مدفوعة' ? 'selected' : '' }}>غير مدفوعة</option>
                                    <option value="جزئي" {{ request('payment_status') == 'جزئي' ? 'selected' : '' }}>دفع جزئي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="{{ route('sales.unposted') }}" class="btn btn-warning">
                                        <i class="fas fa-clock me-2"></i>غير مرحلة
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <a href="{{ route('sales.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

<!-- Sales Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-receipt me-2"></i>
            قائمة المبيعات ({{ $sales->total() }})
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        @if($sales->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المتبقي</th>
                            <th>طريقة الدفع</th>
                            <th>حالة الدفع</th>
                            <th>حالة الترحيل</th>
                            <th>الكاشير</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sales as $sale)
                        <tr>
                            <td>
                                <a href="{{ route('sales.show', $sale) }}" class="text-decoration-none fw-bold">
                                    {{ $sale->invoice_number }}
                                </a>
                            </td>
                            <td>
                                @if($sale->customer)
                                    <div>
                                        <span class="fw-bold">{{ $sale->customer->name }}</span>
                                        <br>
                                        <small class="text-muted">{{ $sale->customer->phone }}</small>
                                    </div>
                                @else
                                    <span class="text-muted">عميل عادي</span>
                                @endif
                            </td>
                            <td>
                                <div>
                                    {{ $sale->sale_date->format('Y-m-d') }}
                                    <br>
                                    <small class="text-muted">
                                        {{ $sale->sale_time->format('g:i') }}
                                        {{ $sale->sale_time->format('A') == 'AM' ? 'ص' : 'م' }}
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold text-success">
                                    {{ number_format($sale->total_amount, 2) }} ر.ي
                                </span>
                            </td>
                            <td>
                                <span class="text-primary">
                                    {{ number_format($sale->paid_amount, 2) }} ر.ي
                                </span>
                            </td>
                            <td>
                                @if($sale->remaining_amount > 0)
                                    <span class="text-danger fw-bold">
                                        {{ number_format($sale->remaining_amount, 2) }} ر.ي
                                    </span>
                                @else
                                    <span class="text-success">
                                        <i class="fas fa-check"></i> مدفوع
                                    </span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $sale->payment_method }}</span>
                            </td>
                            <td>
                                @if($sale->status === 'مكتملة')
                                    <span class="badge bg-success">مكتملة</span>
                                @elseif($sale->status === 'غير مدفوعة')
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                @elseif($sale->status === 'جزئي')
                                    <span class="badge bg-warning">دفع جزئي</span>
                                @elseif($sale->status === 'مسترجعة')
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-undo me-1"></i>مسترجعة
                                    </span>
                                @else
                                    <span class="badge bg-info">{{ $sale->status }}</span>
                                @endif
                            </td>
                            <td>
                                @if($sale->is_posted)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>مرحلة
                                    </span>
                                    @if($sale->posted_at)
                                        <br><small class="text-muted">{{ $sale->posted_at->format('Y-m-d H:i') }}</small>
                                    @endif
                                @else
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>غير مرحلة
                                    </span>
                                @endif
                            </td>
                            <td>
                                <small class="text-muted">{{ $sale->user->name }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('sales.show', $sale) }}"
                                       class="btn btn-sm btn-outline-info"
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('sales.print', $sale) }}"
                                       class="btn btn-sm btn-outline-secondary"
                                       title="طباعة"
                                       target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    @if($sale->customer && $sale->customer->whatsapp)
                                        <button type="button"
                                                class="btn btn-sm btn-outline-success"
                                                title="إرسال واتساب"
                                                onclick="sendWhatsApp({{ $sale->id }})">
                                            <i class="fab fa-whatsapp"></i>
                                        </button>
                                    @endif

                                    <!-- أزرار الترحيل -->
                                    @if(!$sale->is_posted)
                                        <form method="POST" action="{{ route('sales.post', $sale) }}" style="display: inline;">
                                            @csrf
                                            <button type="submit"
                                                    class="btn btn-sm btn-outline-primary"
                                                    title="ترحيل الفاتورة"
                                                    onclick="return confirm('هل تريد ترحيل هذه الفاتورة؟')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    @else
                                        @if(auth()->user()->hasRole('admin'))
                                            <form method="POST" action="{{ route('sales.unpost', $sale) }}" style="display: inline;">
                                                @csrf
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-warning"
                                                        title="إلغاء ترحيل الفاتورة"
                                                        onclick="return confirm('هل تريد إلغاء ترحيل هذه الفاتورة؟')">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                        @endif
                                    @endif

                                    @if(!$sale->is_posted)
                                        @if($sale->status !== 'مسترجعة')
                                            <a href="{{ route('sales.edit', $sale) }}"
                                               class="btn btn-sm btn-outline-warning"
                                               title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <form method="POST" action="{{ route('sales.refund', $sale) }}" style="display: inline;">
                                                @csrf
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-info"
                                                        title="استرجاع"
                                                        onclick="return confirm('هل تريد استرجاع هذه الفاتورة؟ سيتم إعادة المخزون.')">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                        @endif

                                        @if($sale->status !== 'مسترجعة')
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    title="حذف"
                                                    onclick="deleteSale({{ $sale->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        @else
                                            <span class="badge bg-secondary">مسترجعة</span>
                                        @endif
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="صفحات المبيعات">
                    {{ $sales->links('pagination::bootstrap-4') }}
                </nav>
            </div>

            <!-- Summary -->
            <div class="row mt-4">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5>{{ $sales->count() }}</h5>
                            <p class="mb-0">إجمالي الفواتير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5>{{ number_format($postedSales ?? 0, 0) }}</h5>
                            <p class="mb-0">المبيعات المرحلة (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5>{{ number_format($unpostedSales ?? 0, 0) }}</h5>
                            <p class="mb-0">غير المرحلة (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h5>{{ number_format($cashSales ?? 0, 0) }}</h5>
                            <p class="mb-0">المبيعات النقدية (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h5>{{ number_format($creditSales ?? 0, 0) }}</h5>
                            <p class="mb-0">المبيعات الآجلة (ر.ي)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h5>{{ number_format($sales->sum('remaining_amount'), 2) }}</h5>
                            <p class="mb-0">إجمالي المتبقي (ر.ي)</p>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مبيعات</h5>
                <p class="text-muted">لم يتم العثور على مبيعات تطابق معايير البحث</p>
                <a href="{{ route('pos.index') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء أول فاتورة
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* إصلاح مشاكل الـ pagination */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* إصلاح أحجام الأيقونات */
.btn-sm i {
    font-size: 0.75rem;
}

.card-header i {
    font-size: 1rem;
}

/* تحسين الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.875rem;
}

/* تحسين الأزرار */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تحسين البطاقات الإحصائية */
.card h5 {
    font-size: 1.5rem;
    font-weight: bold;
}

.card p {
    font-size: 0.875rem;
}

/* تحسين الفلاتر */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-control, .form-select {
    font-size: 0.875rem;
}

/* تحسين breadcrumb */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item {
    font-size: 0.875rem;
}

/* تحسين الـ badges */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 2px;
        margin-right: 0;
    }
}
</style>
@endpush

@push('scripts')
<script>
function deleteSale(saleId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/sales/${saleId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function sendWhatsApp(saleId) {
    fetch(`/sales/${saleId}/send-whatsapp`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.whatsapp_url, '_blank');
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إرسال الرسالة');
    });
}
</script>
@endpush
