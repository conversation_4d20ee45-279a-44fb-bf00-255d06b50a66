<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Sale;
use App\Models\SalePayment;
use App\Services\PdfService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class CustomerStatementController extends Controller
{
    /**
     * صفحة كشف الحساب الرئيسية
     */
    public function index(Request $request)
    {
        // جلب جميع العملاء النشطين
        $customers = Customer::where('is_active', true)
            ->orderBy('name')
            ->get();

        $selectedCustomer = null;
        $statement = null;
        $summary = null;

        if ($request->filled('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);

            if ($selectedCustomer) {
                $statement = $this->generateStatement($request);
                $summary = $this->calculateSummary($statement);
            }
        }

        return view('statements.index', compact('customers', 'selectedCustomer', 'statement', 'summary'));
    }

    /**
     * توليد كشف الحساب
     */
    private function generateStatement(Request $request)
    {
        $customerId = $request->customer_id;
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;
        $month = $request->month;
        $year = $request->year ?? date('Y');

        // تحديد الفترة الزمنية
        if ($month) {
            $dateFrom = Carbon::createFromDate($year, $month, 1)->startOfMonth();
            $dateTo = Carbon::createFromDate($year, $month, 1)->endOfMonth();
        } else {
            $dateFrom = $dateFrom ? Carbon::parse($dateFrom) : Carbon::now()->startOfMonth();
            $dateTo = $dateTo ? Carbon::parse($dateTo) : Carbon::now()->endOfMonth();
        }

        // جلب الفواتير مع تفاصيل الدفعات
        $sales = Sale::with(['user', 'salePayments' => function($query) {
                $query->orderBy('created_at', 'desc');
            }])
            ->where('customer_id', $customerId)
            ->where('is_posted', true)
            ->whereBetween('sale_date', [$dateFrom, $dateTo])
            ->latest('sale_date')
            ->latest('sale_time')
            ->get();

        // حساب الرصيد الافتتاحي (المديونيات قبل الفترة)
        $openingBalance = Sale::where('customer_id', $customerId)
            ->where('is_posted', true)
            ->where('sale_date', '<', $dateFrom)
            ->sum('remaining_amount');

        return [
            'customer_id' => $customerId,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'sales' => $sales,
            'opening_balance' => $openingBalance
        ];
    }

    /**
     * حساب ملخص كشف الحساب
     */
    private function calculateSummary($statement)
    {
        if (!$statement) return null;

        $sales = $statement['sales'];
        $openingBalance = $statement['opening_balance'];

        // إجمالي المبيعات في الفترة
        $totalSales = $sales->sum('total_amount');

        // إجمالي المدفوع في الفترة (من الفواتير فقط)
        $totalPaid = $sales->sum('paid_amount');

        // إجمالي المتبقي من جميع الفواتير
        $totalRemaining = $sales->sum('remaining_amount');

        // الرصيد الختامي = الرصيد الافتتاحي + المتبقي من فواتير الفترة
        $closingBalance = $openingBalance + $totalRemaining;

        // حساب عدد الدفعات من جميع الفواتير
        $totalPaymentsCount = $sales->sum(function($sale) {
            return $sale->salePayments->count();
        });

        return [
            'opening_balance' => $openingBalance,
            'total_sales' => $totalSales,
            'total_paid' => $totalPaid,
            'total_remaining' => $totalRemaining,
            'closing_balance' => $closingBalance,
            'sales_count' => $sales->count(),
            'payments_count' => $totalPaymentsCount
        ];
    }

    /**
     * تصدير كشف الحساب PDF
     */
    public function exportPdf(Request $request)
    {
        $customer = Customer::findOrFail($request->customer_id);
        $statement = $this->generateStatement($request);
        $summary = $this->calculateSummary($statement);

        $pdfService = new PdfService();
        $pdf = $pdfService->loadView('statements.pdf', compact('customer', 'statement', 'summary'));

        $filename = 'كشف_حساب_' . $customer->name . '_' . date('Y-m-d') . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * إرسال كشف الحساب عبر الواتساب
     */
    public function sendWhatsApp(Request $request)
    {
        $customer = Customer::findOrFail($request->customer_id);

        if (!$customer->phone) {
            return response()->json([
                'success' => false,
                'message' => 'رقم الهاتف غير متوفر للعميل'
            ]);
        }

        $statement = $this->generateStatement($request);
        $summary = $this->calculateSummary($statement);

        // إنشاء ملف PDF
        $pdfService = new PdfService();
        $pdfService->loadView('statements.pdf', compact('customer', 'statement', 'summary'));

        // حفظ الملف مؤقتاً
        $customerName = preg_replace('/[^a-zA-Z0-9\u0600-\u06FF\s]/', '', $customer->name);
        $customerName = str_replace(' ', '_', $customerName);
        $filename = 'كشف_حساب_' . $customerName . '_' . date('Y-m-d_H-i-s') . '.pdf';
        $filepath = storage_path('app/public/temp/' . $filename);

        // إنشاء مجلد temp إذا لم يكن موجود
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        $pdfService->save($filepath);

        // تنسيق الرسالة
        $message = $this->formatWhatsAppMessage($customer, $statement, $summary);

        // رقم الهاتف مع رمز الدولة
        $phone = $this->formatPhoneNumber($customer->phone);

        // رابط تحميل الملف
        $fileUrl = url('storage/temp/' . $filename);

        // رسالة مع رابط الملف
        $messageWithFile = $message . "\n\n📎 *رابط كشف الحساب (قابل للطباعة):*\n" . $fileUrl . "\n\n💡 *ملاحظة:* اضغط على زر الطباعة في الصفحة لطباعة كشف الحساب";

        // رابط الواتساب مع النص فقط أولاً
        $whatsappUrl = "https://wa.me/{$phone}?text=" . urlencode($message);

        // جدولة حذف الملف بعد ساعة
        $this->scheduleFileCleanup($filepath);

        return response()->json([
            'success' => true,
            'whatsapp_url' => $whatsappUrl,
            'file_url' => $fileUrl,
            'file_path' => $filepath,
            'file_name' => $filename,
            'download_folder' => 'Downloads', // مجلد التحميلات الافتراضي
            'message' => 'تم إنشاء كشف الحساب. سيتم فتح الواتساب لإرسال الرسالة والملف.',
            'instructions' => 'بعد فتح الواتساب، اضغط على أيقونة المرفقات (📎) وارفق الملف المحفوظ.'
        ]);
    }

    /**
     * تنسيق رسالة الواتساب
     */
    private function formatWhatsAppMessage($customer, $statement, $summary)
    {
        $dateFrom = $statement['date_from']->format('Y-m-d');
        $dateTo = $statement['date_to']->format('Y-m-d');

        $message = "🧾 *كشف حساب العميل*\n\n";
        $message .= "👤 *العميل:* {$customer->name}\n";
        $message .= "📞 *الهاتف:* {$customer->phone}\n";
        $message .= "📅 *الفترة:* من {$dateFrom} إلى {$dateTo}\n\n";

        $message .= "💰 *ملخص الحساب:*\n";
        $message .= "• الرصيد الافتتاحي: " . number_format($summary['opening_balance'], 2) . " ر.ي\n";
        $message .= "• إجمالي المبيعات: " . number_format($summary['total_sales'], 2) . " ر.ي\n";
        $message .= "• إجمالي المدفوع: " . number_format($summary['total_paid'], 2) . " ر.ي\n";
        $message .= "• إجمالي المتبقي: " . number_format($summary['total_remaining'], 2) . " ر.ي\n";
        $message .= "• الرصيد الختامي: " . number_format($summary['closing_balance'], 2) . " ر.ي\n\n";

        if ($statement['sales']->count() > 0) {
            $message .= "📋 *تفاصيل الفواتير:*\n";
            foreach ($statement['sales']->take(5) as $sale) {
                $message .= "• {$sale->invoice_number}:\n";
                $message .= "  - الإجمالي: " . number_format($sale->total_amount, 2) . " ر.ي\n";
                $message .= "  - المدفوع: " . number_format($sale->paid_amount ?? 0, 2) . " ر.ي\n";
                $message .= "  - المتبقي: " . number_format($sale->remaining_amount, 2) . " ر.ي\n";

                // عرض تفاصيل الدفعات إذا وجدت
                if ($sale->salePayments->count() > 0) {
                    $message .= "  - الدفعات: ";
                    $payments = $sale->salePayments->take(2);
                    foreach ($payments as $payment) {
                        $message .= number_format($payment->amount, 2) . " ر.ي ";
                    }
                    if ($sale->salePayments->count() > 2) {
                        $message .= "و " . ($sale->salePayments->count() - 2) . " دفعة أخرى";
                    }
                    $message .= "\n";
                }
                $message .= "\n";
            }

            if ($statement['sales']->count() > 5) {
                $message .= "... و " . ($statement['sales']->count() - 5) . " فاتورة أخرى\n";
            }
        }

        $message .= "\n📱 *نظام نقطة المبيعات*\n";
        $message .= "شكراً لتعاملكم معنا 🙏";

        return $message;
    }

    /**
     * تنسيق رقم الهاتف
     */
    private function formatPhoneNumber($phone)
    {
        // إزالة المسافات والرموز
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // إضافة رمز اليمن إذا لم يكن موجود
        if (!str_starts_with($phone, '967')) {
            if (str_starts_with($phone, '0')) {
                $phone = '967' . substr($phone, 1);
            } else {
                $phone = '967' . $phone;
            }
        }

        return $phone;
    }

    /**
     * جلب كشف الحساب عبر AJAX
     */
    public function getStatement(Request $request)
    {
        $customer = Customer::find($request->customer_id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'العميل غير موجود'
            ]);
        }

        $statement = $this->generateStatement($request);
        $summary = $this->calculateSummary($statement);

        return response()->json([
            'success' => true,
            'customer' => $customer,
            'statement' => $statement,
            'summary' => $summary
        ]);
    }

    /**
     * جدولة حذف الملف المؤقت
     */
    private function scheduleFileCleanup($filepath)
    {
        // حذف الملف بعد ساعة واحدة
        $deleteTime = time() + 3600; // 3600 ثانية = ساعة واحدة

        // يمكن استخدام Laravel Queue هنا للحذف المجدول
        // أو يمكن إنشاء cron job لتنظيف الملفات القديمة

        // للبساطة، سنحذف الملفات القديمة عند إنشاء ملف جديد
        $this->cleanupOldFiles();
    }

    /**
     * تنظيف الملفات القديمة
     */
    private function cleanupOldFiles()
    {
        $tempDir = storage_path('app/public/temp/');

        if (!is_dir($tempDir)) {
            return;
        }

        $files = array_merge(
            glob($tempDir . '*.pdf'),
            glob($tempDir . '*.html')
        );
        $currentTime = time();

        foreach ($files as $file) {
            // حذف الملفات الأقدم من ساعة واحدة
            if (is_file($file) && ($currentTime - filemtime($file)) > 3600) {
                unlink($file);
            }
        }
    }

    /**
     * تحميل ملف PDF مؤقت
     */
    public function downloadTempPdf($filename)
    {
        $filepath = storage_path('app/public/temp/' . $filename);

        if (!file_exists($filepath)) {
            abort(404, 'الملف غير موجود أو انتهت صلاحيته');
        }

        return response()->download($filepath)->deleteFileAfterSend(true);
    }
}
