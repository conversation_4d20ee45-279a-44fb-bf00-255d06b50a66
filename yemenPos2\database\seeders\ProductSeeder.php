<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // الحصول على التصنيفات
        $beverages = Category::where('name_ar', 'مشروبات')->first();
        $food = Category::where('name_ar', 'مواد غذائية')->first();
        $cleaning = Category::where('name_ar', 'منظفات')->first();
        $personal = Category::where('name_ar', 'أدوات شخصية')->first();
        $sweets = Category::where('name_ar', 'حلويات')->first();
        $office = Category::where('name_ar', 'أدوات مكتبية')->first();

        $products = [
            // مشروبات
            [
                'name_ar' => 'كوكا كولا 330 مل',
                'name_en' => 'Coca Cola 330ml',
                'sku' => 'BEV001',
                'barcode' => '1234567890123',
                'category_id' => $beverages->id,
                'purchase_price' => 150.00,
                'selling_price' => 200.00,
                'wholesale_price' => 180.00,
                'stock_quantity' => 100,
                'min_stock_level' => 20,
                'unit' => 'علبة',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'ماء صافي 1.5 لتر',
                'name_en' => 'Pure Water 1.5L',
                'sku' => 'BEV002',
                'barcode' => '1234567890124',
                'category_id' => $beverages->id,
                'purchase_price' => 80.00,
                'selling_price' => 120.00,
                'wholesale_price' => 100.00,
                'stock_quantity' => 200,
                'min_stock_level' => 50,
                'unit' => 'زجاجة',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'عصير برتقال طبيعي',
                'name_en' => 'Natural Orange Juice',
                'sku' => 'BEV003',
                'barcode' => '1234567890125',
                'category_id' => $beverages->id,
                'purchase_price' => 300.00,
                'selling_price' => 400.00,
                'wholesale_price' => 350.00,
                'stock_quantity' => 50,
                'min_stock_level' => 10,
                'unit' => 'علبة',
                'is_active' => true,
                'track_stock' => true,
            ],

            // مواد غذائية
            [
                'name_ar' => 'أرز بسمتي 5 كيلو',
                'name_en' => 'Basmati Rice 5kg',
                'sku' => 'FOOD001',
                'barcode' => '1234567890126',
                'category_id' => $food->id,
                'purchase_price' => 2500.00,
                'selling_price' => 3000.00,
                'wholesale_price' => 2800.00,
                'stock_quantity' => 30,
                'min_stock_level' => 5,
                'unit' => 'كيس',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'زيت طبخ 1.8 لتر',
                'name_en' => 'Cooking Oil 1.8L',
                'sku' => 'FOOD002',
                'barcode' => '1234567890127',
                'category_id' => $food->id,
                'purchase_price' => 800.00,
                'selling_price' => 1000.00,
                'wholesale_price' => 900.00,
                'stock_quantity' => 25,
                'min_stock_level' => 5,
                'unit' => 'زجاجة',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'سكر أبيض 1 كيلو',
                'name_en' => 'White Sugar 1kg',
                'sku' => 'FOOD003',
                'barcode' => '1234567890128',
                'category_id' => $food->id,
                'purchase_price' => 400.00,
                'selling_price' => 500.00,
                'wholesale_price' => 450.00,
                'stock_quantity' => 40,
                'min_stock_level' => 10,
                'unit' => 'كيس',
                'is_active' => true,
                'track_stock' => true,
            ],

            // منظفات
            [
                'name_ar' => 'صابون غسيل أطباق',
                'name_en' => 'Dish Washing Soap',
                'sku' => 'CLEAN001',
                'barcode' => '1234567890129',
                'category_id' => $cleaning->id,
                'purchase_price' => 200.00,
                'selling_price' => 300.00,
                'wholesale_price' => 250.00,
                'stock_quantity' => 60,
                'min_stock_level' => 15,
                'unit' => 'زجاجة',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'مسحوق غسيل 3 كيلو',
                'name_en' => 'Washing Powder 3kg',
                'sku' => 'CLEAN002',
                'barcode' => '1234567890130',
                'category_id' => $cleaning->id,
                'purchase_price' => 1200.00,
                'selling_price' => 1500.00,
                'wholesale_price' => 1350.00,
                'stock_quantity' => 20,
                'min_stock_level' => 5,
                'unit' => 'علبة',
                'is_active' => true,
                'track_stock' => true,
            ],

            // أدوات شخصية
            [
                'name_ar' => 'شامبو للشعر 400 مل',
                'name_en' => 'Hair Shampoo 400ml',
                'sku' => 'PERS001',
                'barcode' => '1234567890131',
                'category_id' => $personal->id,
                'purchase_price' => 600.00,
                'selling_price' => 800.00,
                'wholesale_price' => 700.00,
                'stock_quantity' => 35,
                'min_stock_level' => 8,
                'unit' => 'زجاجة',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'معجون أسنان',
                'name_en' => 'Toothpaste',
                'sku' => 'PERS002',
                'barcode' => '1234567890132',
                'category_id' => $personal->id,
                'purchase_price' => 250.00,
                'selling_price' => 350.00,
                'wholesale_price' => 300.00,
                'stock_quantity' => 45,
                'min_stock_level' => 10,
                'unit' => 'أنبوب',
                'is_active' => true,
                'track_stock' => true,
            ],

            // حلويات
            [
                'name_ar' => 'شوكولاتة كيت كات',
                'name_en' => 'Kit Kat Chocolate',
                'sku' => 'SWEET001',
                'barcode' => '1234567890133',
                'category_id' => $sweets->id,
                'purchase_price' => 100.00,
                'selling_price' => 150.00,
                'wholesale_price' => 130.00,
                'stock_quantity' => 80,
                'min_stock_level' => 20,
                'unit' => 'قطعة',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'بسكويت أوريو',
                'name_en' => 'Oreo Biscuits',
                'sku' => 'SWEET002',
                'barcode' => '1234567890134',
                'category_id' => $sweets->id,
                'purchase_price' => 180.00,
                'selling_price' => 250.00,
                'wholesale_price' => 220.00,
                'stock_quantity' => 60,
                'min_stock_level' => 15,
                'unit' => 'علبة',
                'is_active' => true,
                'track_stock' => true,
            ],

            // أدوات مكتبية
            [
                'name_ar' => 'قلم حبر أزرق',
                'name_en' => 'Blue Ink Pen',
                'sku' => 'OFF001',
                'barcode' => '1234567890135',
                'category_id' => $office->id,
                'purchase_price' => 50.00,
                'selling_price' => 80.00,
                'wholesale_price' => 65.00,
                'stock_quantity' => 100,
                'min_stock_level' => 25,
                'unit' => 'قطعة',
                'is_active' => true,
                'track_stock' => true,
            ],
            [
                'name_ar' => 'دفتر مدرسي 100 ورقة',
                'name_en' => 'School Notebook 100 pages',
                'sku' => 'OFF002',
                'barcode' => '1234567890136',
                'category_id' => $office->id,
                'purchase_price' => 120.00,
                'selling_price' => 180.00,
                'wholesale_price' => 150.00,
                'stock_quantity' => 50,
                'min_stock_level' => 10,
                'unit' => 'دفتر',
                'is_active' => true,
                'track_stock' => true,
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
