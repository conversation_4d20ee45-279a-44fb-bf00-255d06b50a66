<?php
echo "<h2>🔍 اختبار اتصال قاعدة البيانات</h2>";

// اختبار الاتصال بـ MySQL على المنفذ 3306
echo "<h3>📊 اختبار المنفذ 3306:</h3>";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    echo "✅ الاتصال نجح بالمنفذ 3306<br>";
    
    // عرض قواعد البيانات
    $stmt = $pdo->query('SHOW DATABASES');
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<strong>قواعد البيانات الموجودة:</strong><br>";
    foreach ($databases as $db) {
        if ($db === 'pos_system') {
            echo "🎯 <strong style='color: green;'>$db</strong> ← هذه قاعدة بياناتنا!<br>";
        } else {
            echo "📁 $db<br>";
        }
    }
    
    // اختبار قاعدة البيانات pos_system
    if (in_array('pos_system', $databases)) {
        echo "<br><h4>📋 جداول قاعدة البيانات pos_system:</h4>";
        $pdo->exec('USE pos_system');
        $stmt = $pdo->query('SHOW TABLES');
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            echo "📊 $table<br>";
        }
        
        // عدد السجلات في بعض الجداول
        echo "<br><h4>📈 إحصائيات:</h4>";
        $important_tables = ['users', 'products', 'sales', 'customers', 'suppliers'];
        
        foreach ($important_tables as $table) {
            if (in_array($table, $tables)) {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "📊 $table: $count سجل<br>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "❌ فشل الاتصال بالمنفذ 3306: " . $e->getMessage() . "<br>";
}

// اختبار الاتصال بـ MySQL على المنفذ 3307
echo "<br><h3>📊 اختبار المنفذ 3307:</h3>";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3307', 'root', '');
    echo "✅ الاتصال نجح بالمنفذ 3307<br>";
    
    // عرض قواعد البيانات
    $stmt = $pdo->query('SHOW DATABASES');
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<strong>قواعد البيانات الموجودة:</strong><br>";
    foreach ($databases as $db) {
        if ($db === 'pos_system') {
            echo "🎯 <strong style='color: green;'>$db</strong> ← هذه قاعدة بياناتنا!<br>";
        } else {
            echo "📁 $db<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ فشل الاتصال بالمنفذ 3307: " . $e->getMessage() . "<br>";
}

// معلومات إضافية
echo "<br><h3>ℹ️ معلومات إضافية:</h3>";
echo "🖥️ خادم الويب: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "🐘 إصدار PHP: " . PHP_VERSION . "<br>";

// اختبار إعدادات Laravel
echo "<br><h3>🔧 إعدادات Laravel:</h3>";
if (file_exists('.env')) {
    $env_content = file_get_contents('.env');
    if (preg_match('/DB_HOST=(.+)/', $env_content, $matches)) {
        echo "🏠 DB_HOST: " . trim($matches[1]) . "<br>";
    }
    if (preg_match('/DB_PORT=(.+)/', $env_content, $matches)) {
        echo "🚪 DB_PORT: " . trim($matches[1]) . "<br>";
    }
    if (preg_match('/DB_DATABASE=(.+)/', $env_content, $matches)) {
        echo "🗄️ DB_DATABASE: " . trim($matches[1]) . "<br>";
    }
    if (preg_match('/DB_USERNAME=(.+)/', $env_content, $matches)) {
        echo "👤 DB_USERNAME: " . trim($matches[1]) . "<br>";
    }
} else {
    echo "❌ ملف .env غير موجود<br>";
}

echo "<br><h3>🎯 الخلاصة:</h3>";
echo "إذا كانت قاعدة البيانات pos_system ظاهرة في المنفذ 3306 ولكن غير ظاهرة في phpMyAdmin،<br>";
echo "فهذا يعني أن phpMyAdmin يتصل بخادم مختلف أو هناك مشكلة في الإعدادات.<br>";
?>
