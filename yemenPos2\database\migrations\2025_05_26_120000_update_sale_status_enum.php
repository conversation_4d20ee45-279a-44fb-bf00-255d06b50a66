<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تحديث enum لإضافة الحالات الجديدة
        DB::statement("ALTER TABLE sales MODIFY COLUMN status ENUM('مكتملة', 'غير مدفوعة', 'دفع جزئي', 'مسترجعة', 'معلقة') DEFAULT 'مكتملة'");
        
        // تحديث الحالات القديمة
        DB::table('sales')
            ->where('status', 'معلقة')
            ->update(['status' => 'غير مدفوعة']);
            
        DB::table('sales')
            ->where('status', 'جزئي')
            ->update(['status' => 'دفع جزئي']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // العكس: إرجاع الحالات القديمة
        DB::table('sales')
            ->where('status', 'غير مدفوعة')
            ->update(['status' => 'معلقة']);
            
        DB::table('sales')
            ->where('status', 'دفع جزئي')
            ->update(['status' => 'جزئي']);
            
        // إرجاع enum القديم
        DB::statement("ALTER TABLE sales MODIFY COLUMN status ENUM('مكتملة', 'معلقة', 'ملغية') DEFAULT 'مكتملة'");
    }
};
