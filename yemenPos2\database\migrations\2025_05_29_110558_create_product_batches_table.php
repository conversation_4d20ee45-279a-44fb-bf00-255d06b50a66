<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_batches', function (Blueprint $table) {
            $table->id();

            // ربط بالمنتج
            $table->foreignId('product_id')->constrained()->onDelete('cascade');

            // ربط بالمورد
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');

            // ربط بالمشترى (إن وجد)
            $table->foreignId('purchase_id')->nullable()->constrained()->onDelete('set null');

            // رقم الدفعة/اللوت
            $table->string('batch_number')->unique();

            // الكمية الأصلية للدفعة
            $table->decimal('original_quantity', 10, 2);

            // الكمية المتبقية
            $table->decimal('remaining_quantity', 10, 2);

            // تاريخ انتهاء الصلاحية لهذه الدفعة
            $table->date('expiry_date')->nullable();

            // تاريخ الإنتاج
            $table->date('production_date')->nullable();

            // سعر التكلفة لهذه الدفعة
            $table->decimal('cost_price', 10, 2);

            // حالة الدفعة
            $table->enum('status', ['active', 'expired', 'depleted', 'recalled'])
                  ->default('active');

            // ملاحظات
            $table->text('notes')->nullable();

            $table->timestamps();

            // فهارس للبحث السريع
            $table->index(['product_id', 'expiry_date']);
            $table->index(['expiry_date', 'status']);
            $table->index(['batch_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_batches');
    }
};
