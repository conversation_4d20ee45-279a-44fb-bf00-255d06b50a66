<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null'); // العميل
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // المستخدم (الكاشير)
            $table->date('sale_date'); // تاريخ البيع
            $table->time('sale_time'); // وقت البيع
            $table->decimal('subtotal', 10, 2); // المجموع الفرعي
            $table->decimal('tax_amount', 10, 2)->default(0); // مبلغ الضريبة
            $table->decimal('discount_amount', 10, 2)->default(0); // مبلغ الخصم
            $table->decimal('total_amount', 10, 2); // المجموع الكلي
            $table->decimal('paid_amount', 10, 2)->default(0); // المبلغ المدفوع
            $table->decimal('remaining_amount', 10, 2)->default(0); // المبلغ المتبقي
            $table->enum('payment_method', ['نقدي', 'آجل', 'تحويل بنكي', 'مختلط'])->default('نقدي'); // طريقة الدفع
            $table->enum('status', ['مكتملة', 'معلقة', 'ملغية'])->default('مكتملة'); // حالة الفاتورة
            $table->string('currency', 3)->default('YER'); // العملة (ريال يمني)
            $table->decimal('exchange_rate', 8, 4)->default(1); // سعر الصرف
            $table->text('notes')->nullable(); // ملاحظات
            $table->boolean('is_printed')->default(false); // تم طباعتها
            $table->boolean('is_sent_whatsapp')->default(false); // تم إرسالها واتساب
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales');
    }
};
