<?php $__env->startSection('title', 'إعدادات النظام'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">الإعدادات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success" onclick="exportSettings()">
                            <i class="fas fa-download me-2"></i>تصدير
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="fas fa-upload me-2"></i>استيراد
                        </button>
                        <button type="button" class="btn btn-warning" onclick="resetSettings()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Form -->
            <form action="<?php echo e(route('settings.update')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="row">
                    <!-- Navigation Tabs -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    مجموعات الإعدادات
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="nav flex-column nav-pills" id="settings-tabs" role="tablist">
                                    <?php
                                        $groupNames = [
                                            'general' => 'الإعدادات العامة',
                                            'invoice' => 'إعدادات الفواتير',
                                            'inventory' => 'إعدادات المخزون',
                                            'payment' => 'إعدادات الدفع',
                                            'notifications' => 'إعدادات الإشعارات',
                                            'backup' => 'إعدادات النسخ الاحتياطي'
                                        ];
                                        $isFirst = true;
                                    ?>

                                    <?php $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupSettings): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <button class="nav-link text-start <?php echo e($isFirst ? 'active' : ''); ?>"
                                                id="<?php echo e($group); ?>-tab" data-bs-toggle="pill"
                                                data-bs-target="#<?php echo e($group); ?>" type="button" role="tab">
                                            <i class="fas fa-<?php echo e($group === 'general' ? 'cog' : ($group === 'invoice' ? 'receipt' : ($group === 'inventory' ? 'boxes' : ($group === 'payment' ? 'credit-card' : ($group === 'notifications' ? 'bell' : 'save'))))); ?> me-2"></i>
                                            <?php echo e($groupNames[$group] ?? ucfirst($group)); ?>

                                            <span class="badge bg-secondary ms-auto"><?php echo e(count($groupSettings)); ?></span>
                                        </button>
                                        <?php $isFirst = false; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Content -->
                    <div class="col-md-9">
                        <div class="tab-content" id="settings-content">
                            <?php $isFirst = true; ?>
                            <?php $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupSettings): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="tab-pane fade <?php echo e($isFirst ? 'show active' : ''); ?>"
                                     id="<?php echo e($group); ?>" role="tabpanel">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">
                                                <?php echo e($groupNames[$group] ?? ucfirst($group)); ?>

                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <?php $__currentLoopData = $groupSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">
                                                            <?php echo e($setting->label); ?>

                                                            <?php if($setting->description): ?>
                                                                <i class="fas fa-info-circle text-muted ms-1"
                                                                   title="<?php echo e($setting->description); ?>"
                                                                   data-bs-toggle="tooltip"></i>
                                                            <?php endif; ?>
                                                        </label>

                                                        <?php if($setting->type === 'boolean'): ?>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox"
                                                                       name="<?php echo e($setting->key); ?>" value="1"
                                                                       id="<?php echo e($setting->key); ?>"
                                                                       <?php echo e($setting->value === 'true' ? 'checked' : ''); ?>>
                                                                <label class="form-check-label" for="<?php echo e($setting->key); ?>">
                                                                    تفعيل
                                                                </label>
                                                            </div>
                                                        <?php elseif($setting->type === 'select'): ?>
                                                            <select class="form-select" name="<?php echo e($setting->key); ?>">
                                                                <?php
                                                                    $options = json_decode($setting->options ?? '[]', true);
                                                                ?>
                                                                <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($value); ?>"
                                                                            <?php echo e($setting->value === $value ? 'selected' : ''); ?>>
                                                                        <?php echo e($label); ?>

                                                                    </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        <?php elseif($setting->type === 'textarea'): ?>
                                                            <textarea class="form-control" name="<?php echo e($setting->key); ?>"
                                                                      rows="3"><?php echo e($setting->value); ?></textarea>
                                                        <?php elseif($setting->type === 'file'): ?>
                                                            <div class="file-upload-container">
                                                                <input type="file" class="form-control"
                                                                       name="<?php echo e($setting->key); ?>"
                                                                       accept="image/*"
                                                                       onchange="previewImage(this, '<?php echo e($setting->key); ?>_preview')">
                                                                <div class="form-text">
                                                                    <i class="fas fa-info-circle me-1"></i>
                                                                    الحد الأقصى: 2 ميجابايت | الصيغ المدعومة: JPG, PNG, GIF, WebP
                                                                </div>

                                                                <?php if($setting->value): ?>
                                                                    <div class="mt-3">
                                                                        <label class="form-label text-muted">الصورة الحالية:</label>
                                                                        <div class="current-image-container">
                                                                            <img src="<?php echo e(asset('storage/' . $setting->value)); ?>"
                                                                                 alt="الصورة الحالية"
                                                                                 class="img-thumbnail current-image"
                                                                                 id="<?php echo e($setting->key); ?>_preview"
                                                                                 style="max-width: 150px; max-height: 150px; object-fit: contain;">
                                                                            <div class="mt-2">
                                                                                <small class="text-muted">
                                                                                    <i class="fas fa-image me-1"></i>
                                                                                    <?php echo e(basename($setting->value)); ?>

                                                                                </small>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="mt-3">
                                                                        <div class="no-image-placeholder" id="<?php echo e($setting->key); ?>_preview">
                                                                            <i class="fas fa-image fa-3x text-muted"></i>
                                                                            <p class="text-muted mt-2">لم يتم رفع صورة بعد</p>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php elseif($setting->type === 'number'): ?>
                                                            <input type="number" class="form-control"
                                                                   name="<?php echo e($setting->key); ?>"
                                                                   value="<?php echo e($setting->value); ?>"
                                                                   step="0.01" min="0">
                                                        <?php else: ?>
                                                            <input type="text" class="form-control"
                                                                   name="<?php echo e($setting->key); ?>"
                                                                   value="<?php echo e($setting->value); ?>">
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php $isFirst = false; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Save Button -->
                        <div class="text-end mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد الإعدادات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('settings.import')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">ملف الإعدادات (JSON)</label>
                        <input type="file" class="form-control" name="settings_file"
                               accept=".json" required>
                        <div class="form-text">
                            يرجى اختيار ملف JSON يحتوي على الإعدادات المصدرة مسبقاً
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> سيتم استبدال الإعدادات الحالية بالإعدادات الموجودة في الملف
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-upload me-2"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.file-upload-container {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.file-upload-container:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.current-image-container {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.current-image {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.no-image-placeholder {
    padding: 40px 20px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    text-align: center;
}

.image-preview {
    max-width: 150px;
    max-height: 150px;
    object-fit: contain;
    border: 2px solid #28a745;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}
</style>

<script>
// تصدير الإعدادات
function exportSettings() {
    window.location.href = '<?php echo e(route("settings.export")); ?>';
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("settings.reset")); ?>';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

// معاينة الصورة قبل الرفع
function previewImage(input, previewId) {
    const file = input.files[0];
    const preview = document.getElementById(previewId);

    if (file) {
        // التحقق من نوع الملف
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF أو WebP');
            input.value = '';
            return;
        }

        // التحقق من حجم الملف (2MB)
        if (file.size > 2048 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            if (preview.tagName === 'IMG') {
                preview.src = e.target.result;
                preview.className = 'img-thumbnail image-preview';
            } else {
                // إنشاء عنصر img جديد
                preview.innerHTML = `
                    <img src="${e.target.result}"
                         alt="معاينة الصورة"
                         class="img-thumbnail image-preview">
                    <p class="text-success mt-2">
                        <i class="fas fa-check-circle me-1"></i>
                        تم اختيار الصورة بنجاح
                    </p>
                `;
            }
        };
        reader.readAsDataURL(file);
    }
}

// تفعيل tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/settings/index.blade.php ENDPATH**/ ?>