@extends('layouts.app')

@section('title', 'نقطة البيع - نظام نقطة المبيعات')
@section('page-title', 'نقطة البيع')

@push('styles')
<style>
    .pos-container {
        height: calc(100vh - 200px);
        overflow: hidden;
    }

    .products-grid {
        height: 100%;
        overflow-y: auto;
        padding-right: 10px;
    }

    .product-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        height: 120px;
    }

    .product-card:hover {
        transform: translateY(-3px);
        border-color: #667eea;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    }

    .product-card.out-of-stock {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .cart-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .cart-items {
        flex: 1;
        overflow-y: auto;
        max-height: 400px;
    }

    .cart-item {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
    }

    .cart-summary {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        border: 2px solid #667eea;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .quantity-btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: none;
        background: #667eea;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .quantity-input {
        width: 60px;
        text-align: center;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 5px;
    }

    .search-box {
        position: sticky;
        top: 0;
        background: white;
        z-index: 10;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .category-filter {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-bottom: 15px;
    }

    .category-btn {
        padding: 8px 15px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .category-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .total-display {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        text-align: center;
        margin: 15px 0;
    }

    .payment-section {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-top: 15px;
    }
</style>
@endpush

@section('content')
<div class="pos-container">
    <div class="row h-100">
        <!-- Products Section -->
        <div class="col-md-8">
            <div class="search-box">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="productSearch" placeholder="البحث عن منتج بالاسم أو الباركود...">
                            <button class="btn btn-outline-secondary" type="button" id="barcodeBtn">
                                <i class="fas fa-barcode"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="customerSelect">
                            <option value="">عميل نقدي</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="category-filter mt-3">
                    <button class="category-btn active" data-category="all">الكل</button>
                    @foreach($products->groupBy('category.name_ar') as $categoryName => $categoryProducts)
                        <button class="category-btn" data-category="{{ $categoryName }}">{{ $categoryName }}</button>
                    @endforeach
                </div>
            </div>

            <div class="products-grid">
                <div class="row" id="productsContainer">
                    @foreach($products as $product)
                    <div class="col-md-3 col-sm-4 col-6 mb-3 product-item" data-category="{{ $product->category->name_ar ?? 'غير مصنف' }}">
                        <div class="card product-card {{ $product->isOutOfStock() ? 'out-of-stock' : '' }}"
                             data-product-id="{{ $product->id }}"
                             data-product-name="{{ $product->name_ar }}"
                             data-product-price="{{ $product->selling_price }}"
                             data-product-stock="{{ $product->stock_quantity }}">
                            <div class="card-body p-2 text-center">
                                <h6 class="card-title mb-1" style="font-size: 0.9rem;">{{ $product->name_ar }}</h6>
                                <p class="text-muted mb-1" style="font-size: 0.8rem;">{{ $product->sku }}</p>
                                <p class="text-success fw-bold mb-1">{{ number_format($product->selling_price, 2) }} ر.ي</p>
                                <small class="text-muted">
                                    المخزون: {{ $product->stock_quantity }}
                                    @if($product->isLowStock())
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                    @endif
                                </small>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Cart Section -->
        <div class="col-md-4">
            <div class="cart-section">
                <h5 class="mb-3">
                    <i class="fas fa-shopping-cart me-2"></i>
                    سلة المشتريات
                    <span class="badge bg-primary" id="cartCount">0</span>
                </h5>

                <div class="cart-items" id="cartItems">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>السلة فارغة</p>
                        <small>اضغط على المنتجات لإضافتها</small>
                    </div>
                </div>

                <div class="cart-summary">
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0.00 ر.ي</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الخصم:</span>
                        <input type="number" class="form-control form-control-sm" id="discountAmount" value="0" min="0" style="width: 80px;">
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الضريبة:</span>
                        <span id="taxAmount">0.00 ر.ي</span>
                    </div>
                    <hr>
                    <div class="total-display" id="totalAmount">0.00 ر.ي</div>
                </div>

                <div class="payment-section">
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="paymentMethod">
                            <option value="نقدي">نقدي</option>
                            <option value="آجل">آجل</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="مختلط">مختلط</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المبلغ المدفوع</label>
                        <input type="number" class="form-control" id="paidAmount" value="0" min="0" step="0.01">
                    </div>

                    <div class="d-grid gap-2">
                        <button class="btn btn-success btn-lg" id="completeBtn" disabled>
                            <i class="fas fa-check me-2"></i>
                            إتمام البيع
                        </button>
                        <button class="btn btn-outline-secondary" id="clearBtn">
                            <i class="fas fa-trash me-2"></i>
                            مسح السلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Customer Modal -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="mb-3">
                        <label class="form-label">اسم العميل</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" name="phone" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الواتساب</label>
                        <input type="text" class="form-control" name="whatsapp">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveCustomerBtn">حفظ</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let cart = [];
let taxRate = 0; // يمكن جلبها من الإعدادات

$(document).ready(function() {
    // إضافة منتج للسلة
    $('.product-card').on('click', function() {
        if ($(this).hasClass('out-of-stock')) {
            alert('هذا المنتج غير متوفر في المخزون');
            return;
        }

        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        const productPrice = parseFloat($(this).data('product-price'));
        const productStock = parseInt($(this).data('product-stock'));

        addToCart(productId, productName, productPrice, productStock);
    });

    // البحث عن المنتجات
    $('#productSearch').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterProducts(searchTerm);
    });

    // تصفية حسب الفئة
    $('.category-btn').on('click', function() {
        $('.category-btn').removeClass('active');
        $(this).addClass('active');

        const category = $(this).data('category');
        filterByCategory(category);
    });

    // تحديث المبلغ المدفوع تلقائياً
    $('#paymentMethod').on('change', function() {
        const total = parseFloat($('#totalAmount').text().replace(/[^\d.]/g, ''));
        if ($(this).val() === 'نقدي') {
            $('#paidAmount').val(total.toFixed(2));
        } else {
            $('#paidAmount').val('0');
        }
        updateCompleteButton();
    });

    // تحديث الخصم
    $('#discountAmount').on('input', function() {
        updateCartSummary();
    });

    // تحديث المبلغ المدفوع
    $('#paidAmount').on('input', function() {
        updateCompleteButton();
    });

    // إتمام البيع
    $('#completeBtn').on('click', function() {
        completeSale();
    });

    // مسح السلة
    $('#clearBtn').on('click', function() {
        if (confirm('هل أنت متأكد من مسح السلة؟')) {
            clearCart();
        }
    });
});

function addToCart(productId, productName, productPrice, productStock) {
    const existingItem = cart.find(item => item.productId === productId);

    if (existingItem) {
        if (existingItem.quantity >= productStock) {
            alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
            return;
        }
        existingItem.quantity++;
    } else {
        cart.push({
            productId: productId,
            productName: productName,
            unitPrice: productPrice,
            quantity: 1,
            maxStock: productStock
        });
    }

    updateCartDisplay();
    updateCartSummary();
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.productId !== productId);
    updateCartDisplay();
    updateCartSummary();
}

function updateQuantity(productId, newQuantity) {
    const item = cart.find(item => item.productId === productId);
    if (item) {
        if (newQuantity <= 0) {
            removeFromCart(productId);
        } else if (newQuantity <= item.maxStock) {
            item.quantity = newQuantity;
            updateCartDisplay();
            updateCartSummary();
        } else {
            alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
        }
    }
}

function updateCartDisplay() {
    const cartItems = $('#cartItems');
    const cartCount = $('#cartCount');

    cartCount.text(cart.length);

    if (cart.length === 0) {
        cartItems.html(`
            <div class="text-center text-muted py-5">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>السلة فارغة</p>
                <small>اضغط على المنتجات لإضافتها</small>
            </div>
        `);
        return;
    }

    let html = '';
    cart.forEach(item => {
        const total = item.quantity * item.unitPrice;
        html += `
            <div class="cart-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">${item.productName}</h6>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.productId})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.productId}, ${item.quantity - 1})">-</button>
                        <input type="number" class="quantity-input" value="${item.quantity}"
                               onchange="updateQuantity(${item.productId}, parseInt(this.value))"
                               min="1" max="${item.maxStock}">
                        <button class="quantity-btn" onclick="updateQuantity(${item.productId}, ${item.quantity + 1})">+</button>
                    </div>
                    <div class="text-end">
                        <div>${item.unitPrice.toFixed(2)} ر.ي</div>
                        <div class="fw-bold">${total.toFixed(2)} ر.ي</div>
                    </div>
                </div>
            </div>
        `;
    });

    cartItems.html(html);
}

function updateCartSummary() {
    const subtotal = cart.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const discountAmount = parseFloat($('#discountAmount').val()) || 0;
    const taxAmount = (subtotal - discountAmount) * (taxRate / 100);
    const total = subtotal - discountAmount + taxAmount;

    $('#subtotal').text(subtotal.toFixed(2) + ' ر.ي');
    $('#taxAmount').text(taxAmount.toFixed(2) + ' ر.ي');
    $('#totalAmount').text(total.toFixed(2) + ' ر.ي');

    // تحديث المبلغ المدفوع إذا كان الدفع نقدي
    if ($('#paymentMethod').val() === 'نقدي') {
        $('#paidAmount').val(total.toFixed(2));
    }

    updateCompleteButton();
}

function updateCompleteButton() {
    const total = parseFloat($('#totalAmount').text().replace(/[^\d.]/g, ''));
    const paid = parseFloat($('#paidAmount').val()) || 0;
    const hasItems = cart.length > 0;

    $('#completeBtn').prop('disabled', !hasItems || (total > 0 && paid < 0));
}

function filterProducts(searchTerm) {
    $('.product-item').each(function() {
        const productName = $(this).find('.card-title').text().toLowerCase();
        const productSku = $(this).find('.text-muted').first().text().toLowerCase();

        if (productName.includes(searchTerm) || productSku.includes(searchTerm)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

function filterByCategory(category) {
    if (category === 'all') {
        $('.product-item').show();
    } else {
        $('.product-item').each(function() {
            if ($(this).data('category') === category) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }
}

function clearCart() {
    cart = [];
    updateCartDisplay();
    updateCartSummary();
}

function completeSale() {
    if (cart.length === 0) {
        alert('لا يمكن إتمام البيع بدون منتجات');
        return;
    }

    const customerId = $('#customerSelect').val() || null;
    const paymentMethod = $('#paymentMethod').val();
    const paidAmount = parseFloat($('#paidAmount').val()) || 0;
    const discountAmount = parseFloat($('#discountAmount').val()) || 0;

    const saleData = {
        customer_id: customerId,
        payment_method: paymentMethod,
        paid_amount: paidAmount,
        discount_amount: discountAmount,
        items: cart.map(item => ({
            product_id: item.productId,
            quantity: item.quantity,
            unit_price: item.unitPrice,
            discount_amount: 0
        }))
    };

    // إظهار حالة التحميل
    $('#completeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');

    $.ajax({
        url: '{{ route("sales.store") }}',
        method: 'POST',
        data: saleData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                alert('تم إنشاء الفاتورة بنجاح!\nرقم الفاتورة: ' + response.invoice_number);

                // إعادة تعيين النموذج
                clearCart();
                $('#customerSelect').val('');
                $('#paymentMethod').val('نقدي');
                $('#paidAmount').val('0');
                $('#discountAmount').val('0');

                // يمكن إضافة خيارات للطباعة أو إرسال الواتساب هنا
                if (confirm('هل تريد طباعة الفاتورة؟')) {
                    window.open('{{ url("sales") }}/' + response.sale_id + '/print', '_blank');
                }
            } else {
                alert('خطأ: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('حدث خطأ أثناء إنشاء الفاتورة');
            console.error(xhr.responseText);
        },
        complete: function() {
            $('#completeBtn').prop('disabled', false).html('<i class="fas fa-check me-2"></i>إتمام البيع');
            updateCompleteButton();
        }
    });
}
</script>
@endpush
