<!DOCTYPE html>
<html>
<head>
    <title>اختبار POS API</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <h1>اختبار POS API</h1>
    
    <button onclick="testAPI()">اختبار تحميل المنتجات</button>
    <button onclick="testCategory(1)">اختبار التصنيف 1</button>
    <button onclick="testSearch('كوكا')">اختبار البحث</button>
    
    <div id="results"></div>
    
    <script>
        async function testAPI() {
            try {
                const formData = new FormData();
                formData.append('search', '');
                formData.append('category_id', '');
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                
                const response = await fetch('/pos/search-products', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                document.getElementById('results').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = 'خطأ: ' + error.message;
            }
        }
        
        async function testCategory(categoryId) {
            try {
                const formData = new FormData();
                formData.append('search', '');
                formData.append('category_id', categoryId);
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                
                const response = await fetch('/pos/search-products', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                document.getElementById('results').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = 'خطأ: ' + error.message;
            }
        }
        
        async function testSearch(search) {
            try {
                const formData = new FormData();
                formData.append('search', search);
                formData.append('category_id', '');
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                
                const response = await fetch('/pos/search-products', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                document.getElementById('results').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = 'خطأ: ' + error.message;
            }
        }
    </script>
</body>
</html>
