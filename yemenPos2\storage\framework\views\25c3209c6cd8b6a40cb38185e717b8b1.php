<?php $__env->startSection('title', 'إدارة المدفوعات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-credit-card me-2"></i>
                        إدارة المدفوعات
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المدفوعات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?php echo e(route('payments.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>دفعة جديدة
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('payments.index')); ?>">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search" 
                                       value="<?php echo e(request('search')); ?>" placeholder="رقم الدفعة أو المرجع...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from" 
                                       value="<?php echo e(request('date_from')); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to" 
                                       value="<?php echo e(request('date_to')); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" name="payment_method">
                                    <option value="">جميع الطرق</option>
                                    <option value="نقدي" <?php echo e(request('payment_method') == 'نقدي' ? 'selected' : ''); ?>>نقدي</option>
                                    <option value="تحويل بنكي" <?php echo e(request('payment_method') == 'تحويل بنكي' ? 'selected' : ''); ?>>تحويل بنكي</option>
                                    <option value="شيك" <?php echo e(request('payment_method') == 'شيك' ? 'selected' : ''); ?>>شيك</option>
                                    <option value="بطاقة ائتمان" <?php echo e(request('payment_method') == 'بطاقة ائتمان' ? 'selected' : ''); ?>>بطاقة ائتمان</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">النوع</label>
                                <select class="form-select" name="payment_type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="دفعة" <?php echo e(request('payment_type') == 'دفعة' ? 'selected' : ''); ?>>دفعة</option>
                                    <option value="استرداد" <?php echo e(request('payment_type') == 'استرداد' ? 'selected' : ''); ?>>استرداد</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <a href="<?php echo e(route('payments.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المدفوعات</h6>
                                    <h4><?php echo e(number_format($totalPayments, 2)); ?> ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-credit-card fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المدفوعات النقدية</h6>
                                    <h4><?php echo e(number_format($cashPayments, 2)); ?> ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">التحويلات البنكية</h6>
                                    <h4><?php echo e(number_format($bankPayments, 2)); ?> ر.ي</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-university fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">عدد المعاملات</h6>
                                    <h4><?php echo e($totalCount); ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-receipt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payments Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المدفوعات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($payments->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الدفعة</th>
                                    <th>النوع</th>
                                    <th>العميل/المورد</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($payment->payment_number); ?></strong>
                                        <?php if($payment->reference_number): ?>
                                            <br><small class="text-muted"><?php echo e($payment->reference_number); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo e($payment->payable_type_arabic); ?></span>
                                        <br><small class="text-muted"><?php echo e($payment->payment_type); ?></small>
                                    </td>
                                    <td>
                                        <?php if($payment->customer): ?>
                                            <strong><?php echo e($payment->customer->name); ?></strong>
                                            <br><small class="text-muted">عميل</small>
                                        <?php elseif($payment->supplier): ?>
                                            <strong><?php echo e($payment->supplier->name); ?></strong>
                                            <br><small class="text-muted">مورد</small>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong class="<?php echo e($payment->payment_type === 'استرداد' ? 'text-danger' : 'text-success'); ?>">
                                            <?php echo e($payment->payment_type === 'استرداد' ? '-' : '+'); ?><?php echo e(number_format($payment->amount, 2)); ?> ر.ي
                                        </strong>
                                    </td>
                                    <td>
                                        <?php echo $payment->payment_method_with_icon; ?>

                                        <?php if($payment->bank_name): ?>
                                            <br><small class="text-muted"><?php echo e($payment->bank_name); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($payment->payment_date->format('Y-m-d')); ?>

                                        <br><small class="text-muted"><?php echo e($payment->created_at->format('H:i')); ?></small>
                                    </td>
                                    <td>
                                        <?php echo $payment->status_badge; ?>

                                    </td>
                                    <td><?php echo e($payment->user->name ?? 'غير محدد'); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('payments.show', $payment)); ?>" 
                                               class="btn btn-sm btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('payments.edit', $payment)); ?>" 
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-danger" 
                                                    onclick="deletePayment(<?php echo e($payment->id); ?>)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="صفحات المدفوعات">
                            <?php echo e($payments->links('pagination::bootstrap-4')); ?>

                        </nav>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مدفوعات</h5>
                        <p class="text-muted">ابدأ بإضافة دفعة جديدة</p>
                        <a href="<?php echo e(route('payments.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>دفعة جديدة
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه الدفعة؟</p>
                <p class="text-danger"><strong>تحذير:</strong> سيتم إعادة حساب حالة الفاتورة المرتبطة</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deletePayment(id) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/payments/${id}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\yemenPos2\yemenPos2\resources\views/payments/index.blade.php ENDPATH**/ ?>